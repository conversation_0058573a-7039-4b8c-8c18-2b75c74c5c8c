import React, {
    ButtonHTMLAttributes,
    CSSProperties,
    useState,
    useCallback,
    useMemo,
} from "react";
import { cn } from "@repo/ui/lib/utils";
import { StudyType } from "../../../core/src/enums";
import { getStudyTypeTheme } from "../../../core/src/exercise/theme";
import { motion } from "framer-motion";

export type ButtonColor =
    | "orange"
    | "orange-light"
    | "red"
    | "green"
    | "gray"
    | "white"
    | "study-theme"
    | "study-theme-light";
export type ButtonMode = "primary" | "secondary";
export type ButtonSize = "sm" | "md" | "lg";

export interface ButtonProps
    extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, "style" | "className"> {
    color?: ButtonColor;
    secondary?: boolean;
    size?: ButtonSize;
    showShadow?: boolean;
    className?: string;
    style?: CSSProperties;
    children: React.ReactNode;
    clickDelay?: number;
    studyType?: StudyType;
    source?: "dialog" | "button";
    loading?: boolean;
}

// 提取常量，提高可维护性
const BUTTON_COLORS = {
    orange: {
        primary:
            "bg-[var(--color-orange-1)] text-white shadow-[0_0.1875rem_0_0_var(--btn-shadow-orange-primary)]",
        secondary:
            "bg-[var(--color-orange-2)] text-[var(--color-orange-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-orange-secondary)]",
    },
    "orange-light": {
        primary:
            "bg-[var(--color-orange-2)] text-[var(--color-orange-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-orange-primary)]",
        secondary:
            "bg-[var(--color-orange-2)] text-[var(--color-orange-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-orange-secondary)]",
    },
    red: {
        primary:
            "bg-[var(--color-red-1)] text-white shadow-[0_0.1875rem_0_0_var(--btn-shadow-red-primary)]",
        secondary:
            "bg-[var(--color-red-2)] text-[var(--color-red-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-red-secondary)]",
    },
    green: {
        primary:
            "bg-[var(--color-green-1)] text-white shadow-[0_0.1875rem_0_0_var(--btn-shadow-green-primary)]",
        secondary:
            "bg-[var(--color-green-2)] text-[var(--color-green-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-green-secondary)]",
    },
    gray: {
        primary:
            "bg-[var(--color-gray-bg)] text-[var(--color-gray-text)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-gray)]",
        secondary:
            "bg-transparent text-[var(--color-gray-text)] border border-[var(--color-gray-text)]",
    },
    white: {
        primary:
            "bg-[var(--color-white-bg)] text-[var(--color-text-3)] border border-[var(--color-divider-1)] shadow-[0_0.1875rem_0_0_var(--btn-shadow-white)] hover:bg-[var(--color-white-hover)]",
        secondary:
            "bg-transparent text-[var(--color-text-3)] border border-[var(--color-divider-1)]",
    },
    "study-theme": {
        primary: "",
        secondary: "",
    },
    "study-theme-light": {
        primary: "",
        secondary: "",
    },
} as const;

const BUTTON_SIZES = {
    sm: "text-xs h-[2.0625rem] px-[0.75rem] rounded-[0.5625rem]", // 33px = 2.0625rem, 12px = 0.75rem, 9px = 0.5625rem
    md: "text-sm h-[2.3125rem] px-[1rem] rounded-[0.625rem]", // 37px = 2.3125rem, 16px = 1rem, 10px = 0.625rem
    lg: "text-[1rem] h-[2.75rem] px-[1rem] rounded-[0.75rem]", // 44px = 2.75rem, 16px = 1rem, 12px = 0.75rem
} as const;

// 提取样式计算逻辑
const getButtonMode = (
    secondary: boolean,
    effectiveColor: ButtonColor
): ButtonMode => {
    if (!secondary) return "primary";

    if (
        ["white", "gray"].includes(effectiveColor) ||
        effectiveColor === "study-theme"
    ) {
        return "secondary";
    }

    return "secondary";
};

const getStudyThemeStyles = (
    studyType: StudyType,
    mode: ButtonMode,
    isPressed: boolean
): { colorStyle: string; dynamicStyle: CSSProperties } => {
    const theme = getStudyTypeTheme(studyType);

    if (mode === "primary") {
        return {
            colorStyle: "text-white",
            dynamicStyle: {
                backgroundColor: theme.primary,
                boxShadow: isPressed ? "none" : `0 0.1875rem 0 0 ${theme.shadowColor}`,
            },
        };
    }

    return {
        colorStyle: "",
        dynamicStyle: {
            backgroundColor: theme.primaryLight,
            color: theme.textColor,
            boxShadow: isPressed
                ? "none"
                : `0 0.1875rem 0 0 ${theme.shadowColorLight}`,
        },
    };
};

// 三个圆点加载动画组件
const LoadingDots: React.FC<{ size?: ButtonSize }> = ({ size = "lg" }) => {
    const dotSize =
        size === "sm" ? "w-1 h-1" : size === "md" ? "w-1.5 h-1.5" : "w-2 h-2";
    const gap = size === "sm" ? "gap-1" : size === "md" ? "gap-1" : "gap-1.5";

    // 经典的三点加载动画：每个点依次从 0.2 -> 1 -> 0.2，形成波浪效果
    return (
        <div className={cn("flex items-center justify-center", gap)}>
            <motion.div
                className={cn(dotSize, "rounded-full bg-current")}
                animate={{
                    opacity: [0.2, 1, 0.2],
                }}
                transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0,
                }}
            />
            <motion.div
                className={cn(dotSize, "rounded-full bg-current")}
                animate={{
                    opacity: [0.2, 1, 0.2],
                }}
                transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.2,
                }}
            />
            <motion.div
                className={cn(dotSize, "rounded-full bg-current")}
                animate={{
                    opacity: [0.2, 1, 0.2],
                }}
                transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.4,
                }}
            />
        </div>
    );
};

const Button: React.FC<ButtonProps> = ({
    color = "orange",
    secondary = false,
    size = "lg",
    showShadow = true,
    className = "",
    style,
    children,
    disabled,
    clickDelay = 0,
    studyType,
    onClick,
    onMouseDown,
    onMouseUp,
    onMouseLeave,
    source = "button",
    loading,
    ...props
}) => {
    const [isPressed, setIsPressed] = useState(false);

    // 使用 useMemo 优化计算，避免重复计算
    const effectiveColor = useMemo(() => {
        if (disabled) return "gray";

        // 如果 source 是 dialog 且 studyType === 2，则使用 red
        if (
            source === "dialog" &&
            color === "study-theme" &&
            studyType &&
            (Number(studyType) === StudyType.AI_COURSE || Number(studyType) === StudyType.REINFORCEMENT_EXERCISE)
        ) {
            return "red";
        }

        return color;
    }, [disabled, color, source, studyType]);

    const mode = useMemo(
        () => getButtonMode(secondary, effectiveColor),
        [secondary, effectiveColor]
    );

    // 计算样式
    const { colorStyle, dynamicStyle } = useMemo(() => {
        let baseColorStyle = BUTTON_COLORS[effectiveColor]?.[mode] || "";
        let baseDynamicStyle: CSSProperties = {};

        if (effectiveColor === "study-theme" && studyType) {
            const themeStyles = getStudyThemeStyles(studyType, mode, isPressed);
            baseColorStyle = themeStyles.colorStyle;
            baseDynamicStyle = themeStyles.dynamicStyle;
        }

        return { colorStyle: baseColorStyle, dynamicStyle: baseDynamicStyle };
    }, [effectiveColor, mode, studyType, isPressed]);



    const sizeStyle = useMemo(
        () => BUTTON_SIZES[size] || BUTTON_SIZES.lg,
        [size]
    );

    const shadowClass = useMemo(() => {
        if (!showShadow) return "!shadow-none";
        if (disabled) return "";
        if (isPressed) return "!shadow-none";
        return "";
    }, [showShadow, disabled, isPressed]);

    // 使用 useCallback 优化事件处理函数
    const handleClick = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            if (disabled) return;

            if (clickDelay) {
                setTimeout(() => {
                    onClick?.(e);
                }, clickDelay);
            } else {
                onClick?.(e);
            }
        },
        [disabled, clickDelay, onClick]
    );

    const handleMouseDown = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            if (!disabled) {
                setIsPressed(true);
                setTimeout(() => {
                    setIsPressed(false);
                }, 150);
            }
            onMouseDown?.(e);
        },
        [disabled, onMouseDown]
    );

    const handleMouseUp = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {

            onMouseUp?.(e);
        },
        [onMouseUp]
    );

    const handleMouseLeave = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            onMouseLeave?.(e);
        },
        [onMouseLeave]
    );

    // 移除调试日志
    // console.log("dynamicStyle", dynamicStyle);

    return (
        <button
            className={cn(
                "relative flex min-w-28 cursor-pointer items-center justify-center gap-1 font-medium transition-all duration-100 ease-out disabled:cursor-not-allowed",
                colorStyle,
                sizeStyle,
                shadowClass,
                !disabled && isPressed ? "transform translate-y-[0.1875rem] opacity-70" : "",
                className
            )}
            style={{ ...dynamicStyle, ...style }}
            disabled={disabled}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            onClick={handleClick}
            {...props}
        >
            <span
                className={cn(
                    "flex h-full w-full select-none items-center justify-center font-medium",
                    disabled ? "opacity-40" : "" // disabled 时降低透明度
                )}
                style={{ userSelect: "none", WebkitUserSelect: "none" }}
            >
                {loading ? <LoadingDots size={size} /> : children}
            </span>
        </button>
    );
};

export default Button;
export { Button };
