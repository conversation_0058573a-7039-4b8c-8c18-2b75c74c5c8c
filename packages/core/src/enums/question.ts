import EnumManager from "../utils/enum-manager";

/**
 * 题目类型枚举
 */
export enum QUESTION_TYPE {
  QUESTION_TYPE_SINGLE_CHOICE = 1, // 单选
  QUESTION_TYPE_MULTIPLE_CHOICE = 2, // 多选
  QUESTION_TYPE_FILL_IN_THE_BLANK = 3, // 填空
  QUESTION_TYPE_JUDGMENT = 4, // 判断
  QUESTION_TYPE_QA = 5, // 问答
  QUESTION_TYPE_CLOZE = 6, // 完型填空 (注释掉的类型)
  QUESTION_TYPE_PARENT_CHILD = 7, // 默认母子题，主要是无法区分出母题是怎样的题型，在调用方使用综合题为名字展示进行筛选
}

/**
 * 题目类型选项配置
 */
export const QUESTION_TYPE_OPTIONS = [
  {
    label: "单选题",
    value: QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
    emptyAnswer: {
      answerOptionList: [],
    },
  },
  {
    label: "多选题",
    value: QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE,
    emptyAnswer: {
      answerOptionList: [],
    },
  },
  {
    label: "填空题",
    value: QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK,
    emptyAnswer: {
      answerOptionList: [],
    },
  },
  {
    label: "判断题",
    value: QUESTION_TYPE.QUESTION_TYPE_JUDGMENT,
  },
  {
    label: "问答题",
    value: QUESTION_TYPE.QUESTION_TYPE_QA,
  },
  {
    label: "完型填空",
    value: QUESTION_TYPE.QUESTION_TYPE_CLOZE,
  },
  {
    label: "综合题",
    value: QUESTION_TYPE.QUESTION_TYPE_PARENT_CHILD,
  },
] as const;

/**
 * 题目类型枚举管理器
 */
export const questionTypeEnumManager = new EnumManager(QUESTION_TYPE_OPTIONS);

// 前端传参类型定义 - 使用 TypeScript 类型

export enum StudyType {
  /* AI课 */
  AI_COURSE = 1,
  /* 巩固练习 */
  REINFORCEMENT_EXERCISE = 2,
  /* 拓展练习 */
  EXPAND_EXERCISE = 3,
}


/**
 * 反馈类型
 */
export enum FeedbackType {
  AnswerCarelessly = "answer_carelessly",
  ContinuousCorrect = "continuous_correct",
  DifficultyDown = "difficulty_down",
  DifficultyUp = "difficulty_up",

  // 下面两种不是后端返回的，是前端自己设置的
  Resume = "resume",
  GiveUp = "give_up",
}

// 自评结果
export enum SelfEvaluationResult {
  Correct = 1,
  PartialCorrect = 2,
  Incorrect = 3,
}