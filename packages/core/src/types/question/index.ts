import { QUESTION_TYPE } from '@repo/core/enums';

export * from './rich-text';



/**
 * 互动解题数据接口
 */
export interface AiExplanation {
    /** 题目分析 */
    problemAnalysis: {
        questionRequirement: string;
        essentialInfo: string;
    };
    /** 考点分析 */
    examPointAnalysis: {
        target: string;
        examPoint: string;
        solutionBreakthrough: string;
    };
    /** 逐步讲解 */
    stepByStepGuide: {
        steps: Array<{
            stepTitle: string;
            thinkAboutIt: {
                questionExplanation: string;
                questionStem: string;
                questionOptionList: CommonQuestionAnswerOption[];
                questionAnswer: {
                    answerOptionList: CommonQuestionAnswerOption[];
                };
            };
            calculateTogether: string;
        }>;
    };
    /** 思路总结 */
    solutionSummary: {
        solutionIdea: string;
        finalAnswer: string;
    };
}

/**
 * 互动解题记录接口
 */
export interface AiExplanationRecord {
    /** 当前tab */
    currentProgress: number;
    /** 考点分析随机引导语 */
    examPointAnalysisGuideText?: string;
    /** 题目分析随机引导语 */
    problemAnalysisGuideText?: string;
    /** 思路总结随机引导语 */
    solutionSummaryGuideText?: string;
    /** 逐步讲解记录 */
    stepByStepGuide: Array<{
        /** 第N步引导语文案 */
        guideText?: string;
        /** 是否作答 */
        isAnswer?: boolean;
    }>;
    /** 逐步讲解进度 */
    stepByStepGuideCurrentProgress: number;
    /** 版本id */
    version: number;
}

/**
 * API 题目内容接口
 */
export interface CommonQuestionContent {
    questionOptionList?: CommonQuestionAnswerOption[] | null;
    questionOrder?: number | null;
    questionOriginName?: string | null;
    questionScore?: number | null;
    questionStem?: string | null;

}

/**
 * 题目标签接口
 */
export interface QuestionTag {
    /** 地区，海淀区 */
    area?: string;
    /** 城市，北京 */
    city?: string;
    /** 省份，北京市 */
    province?: string;
    /** 年份，2023年 */
    year?: string;
}


/**
 * 题目信息接口
 */
export interface CommonQuestion {
    /** 互动解题数据 */
    aiExplanation?: AiExplanation | null;
    /** 互动解题记录 */
    aiExplanationRecord?: AiExplanationRecord | null;
    questionAnswer?: CommonQuestionAnswer | null;
    questionAnswerMode?: number | null;
    questionContent: CommonQuestionContent;
    questionDifficult?: number | null;
    questionExplanation?: string | null;
    questionExtra?: string | null;
    questionId: string;
    questionTopic?: string | null;
    questionType?: QUESTION_TYPE | null;
    questionTag?: QuestionTag | null;
}

/**
 * 题目答案选项接口 - 兼容旧格式
 */
export interface CommonQuestionAnswerOption {
    optionKey?: string;
    optionVal?: string;
}

/**
 * 题目答案接口 - 兼容旧格式
 */
export interface CommonQuestionAnswer {
    answerOptionList: CommonQuestionAnswerOption[];
}