"use client";

import { useDebounceFn } from "ahooks";
import { useLayoutEffect, useRef, useState } from "react";
import { QuestionItemProps } from "../type";
import { AnswerAndExplain } from "./AnswerAndExplain";
import { QuestionContent } from "./QuestionContent";

// 通用的 cn 函数
function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(" ");
}

// TODO: 这个要考虑svgr在工程上集成的问题，这个还没统一
// 现状：当前工程用的svgr，但是如果引用这个组件的工程没用，就会报错
const ArrowDownIcon = ({ className }: { className?: string }) => (
  <svg
    className={cn("ml-0 transition-transform duration-300", className)}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    width="1em"
    height="1em"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

// 简化的查看按钮，保持原有样式
const ViewButton = ({
  onClick,
  children,
}: {
  onClick?: () => void;
  children: React.ReactNode;
}) => (
  <button
    className="inline-flex h-8 cursor-pointer items-center justify-center whitespace-nowrap rounded-2xl border border-indigo-500 bg-transparent px-4 text-sm font-medium text-indigo-500 transition-colors hover:bg-indigo-50 active:bg-indigo-100"
    onClick={onClick}
  >
    {children}
  </button>
);

// 简化的解析按钮
const AnalysisButton = ({
  onClick,
  isActive,
}: {
  onClick?: () => void;
  isActive: boolean;
}) => (
  <button
    className={cn(
      "inline-flex h-8 cursor-pointer items-center justify-center gap-1.5 whitespace-nowrap rounded-md bg-white px-2 py-0 text-sm font-normal transition-colors hover:bg-slate-50 active:bg-slate-200",
      isActive ? "text-primary-2" : "text-gray-2"
    )}
    onClick={onClick}
  >
    答案解析
    <ArrowDownIcon className={cn(isActive ? "rotate-[-180deg]" : "rotate-0")} />
  </button>
);

export default function QuestionItem({
  qaContent,
  index,
  customIndex,
  onClickView,
  customFooter: CustomFooter,
  footerButton: FooterButton,
  className = "",
  hasFooterButton = true,
  onAnalysisToggle,
  showAnalysisButton = true,
  ossHost,
  maxContentHeight = 270,
  // reportButton,
  showViewButton = true,
}: QuestionItemProps & {
  onAnalysisToggle?: (isShow: boolean) => void;
  showAnalysisButton?: boolean;
  ossHost?: string;
  maxContentHeight?: number; // 题目内容最大高度，超过就启动折叠
  // reportButton?: React.ReactNode;
  showViewButton?: boolean;
}) {
  const [showAnalysis, setShowAnalysis] = useState(false);

  const toggleAnalysis = () => {
    const newShowAnalysis = !showAnalysis;
    setShowAnalysis(newShowAnalysis);
    onAnalysisToggle?.(newShowAnalysis);
  };

  // 计算正确率
  const correctCount = qaContent.answerDetails.filter(
    (detail) => detail.isCorrect || detail.answerResult === 1
  ).length;
  const totalCount = qaContent.answerCount || qaContent.answerDetails.length;
  const correctRate =
    (qaContent.correctRate || 0) / 100 ||
    (totalCount > 0 ? correctCount / totalCount : 0);
  const correctRatePercent = Math.floor(correctRate * 100);

  // 判断是否为共性错题
  const isCommonWrong = totalCount > 5 && correctRate < 0.6;

  // 获取题目类型标签
  const getQuestionTypeLabel = (type: number) => {
    switch (type) {
      case 1:
        return "单选题";
      case 2:
        return "多选题";
      case 3:
        return "填空题";
      case 4:
        return "判断题";
      case 5:
        return "解答题";
      default:
        return "未知题型";
    }
  };

  // 获取题目类型颜色
  const getQuestionTypeColor = (type: number) => {
    switch (type) {
      case 1:
        return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      case 2:
        return "bg-purple-100 text-purple-800 hover:bg-purple-200";
      case 3:
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case 4:
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
      case 5:
        return "bg-red-100 text-red-800 hover:bg-red-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  // 创建上下文对象，供自定义组件使用
  const context = {
    qaContent,
    index,
    customIndex,
    correctCount,
    totalCount,
    correctRate,
    correctRatePercent,
    isCommonWrong,
    options: qaContent.options || [],
    getQuestionTypeLabel,
    getQuestionTypeColor,
  };

  const FooterElement = () => {
    if (!hasFooterButton) {
      return null;
    }
    return (
      <>
        {/* 间隔 */}
        <div className="bg-line-3 mx-2 h-4 w-px" />

        {/* 自定义底部按钮 */}
        {FooterButton}

        {showViewButton && (
          <ViewButton
            onClick={() =>
              onClickView?.(qaContent.rootQuestionId || qaContent.questionId)
            }
          >
            查 看
          </ViewButton>
        )}
      </>
    );
  };

  const [isExpanded, setIsExpanded] = useState(false);
  const [isTooHigh, setIsTooHigh] = useState(false);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<ResizeObserver>(null);

  const setIsTooHighDebounced = useDebounceFn(setIsTooHigh, {
    wait: 200,
  });

  useLayoutEffect(() => {
    if (contentContainerRef.current?.children[0]) {
      observerRef.current = new ResizeObserver((entries) => {
        const entry = entries[0];
        if (
          entry &&
          entry.contentRect.height &&
          entry.contentRect.height < maxContentHeight
        ) {
          setIsTooHighDebounced.run(false);
        } else {
          setIsTooHighDebounced.run(true);
        }
      });
      observerRef.current.observe(contentContainerRef.current.children[0]);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [maxContentHeight, setIsTooHighDebounced]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={cn(
        "flex flex-col gap-6 rounded-xl border border-[#E9ECF5] bg-white px-5 pb-2 pt-3",
        className
      )}
    >
      <div className="p-0">
        <div
          className={cn("flex gap-2 overflow-hidden")}
          style={{
            maxHeight: isExpanded ? "unset" : `${maxContentHeight}px`,
          }}
        >
          <div className="mt-1 flex h-8 min-w-8 items-center justify-center p-1">
            <h3 className="text-gray-2 font-medium leading-[150%]">
              {customIndex
                ? customIndex
                : typeof index === "number"
                  ? index + 1 + "."
                  : ""}
            </h3>
          </div>
          <div
            className="w-full flex-1 overflow-hidden"
            ref={contentContainerRef}
          >
            <div>
              {/* 题目内容 */}
              <QuestionContent qaContent={qaContent} ossHost={ossHost} />
            </div>
          </div>
          {isTooHigh && !isExpanded && (
            <div
              className="pointer-events-none absolute bottom-0 left-0 right-0 h-20"
              style={{
                background:
                  "linear-gradient(to top, rgba(255, 255, 255, 0.7), transparent)",
              }}
            />
          )}
        </div>

        {/* 题目标签 questionTags */}
        {qaContent.questionTags.length > 0 || isTooHigh ? (
          <div className="mt-3 flex justify-between pl-10">
            <div className="flex flex-wrap items-start gap-2">
              {qaContent.questionTags.map(
                (tag) =>
                  tag && (
                    <div
                      key={tag}
                      className="border-line-2 flex h-[1.375rem] items-center gap-[0.125rem] rounded-sm border bg-[#F2F4F9] px-[0.375rem] py-[0.125rem]"
                    >
                      <span className="text-xs font-normal leading-[150%] text-gray-600">
                        {tag}
                      </span>
                    </div>
                  )
              )}
            </div>
            {isTooHigh && (
              <div className="flex items-start">
                <button
                  className="text-primary-1 flex cursor-pointer items-center justify-center gap-0.5 whitespace-nowrap rounded-sm bg-[#F2F4F9] px-1.5 py-0.5 text-xs leading-normal text-gray-600 hover:bg-indigo-50 active:opacity-60"
                  onClick={toggleExpanded}
                >
                  {isExpanded ? "收起" : "展开查看更多"}
                  <ArrowDownIcon
                    className={cn(
                      isExpanded ? "rotate-180" : "rotate-0",
                      "text-sm"
                    )}
                  />
                </button>
              </div>
            )}
          </div>
        ) : null}
      </div>

      {/* 底部组件 */}
      <div className="border-line-1 flex w-full flex-col items-center border-t border-dashed px-0 !pt-0">
        <div className="flex h-[3.5rem] w-full items-center justify-between pl-6">
          {/* 左侧内容：使用自定义组件或默认内容 */}
          {CustomFooter ? <CustomFooter context={context} /> : <div></div>}

          {/* 右侧内容：查看详情和查看解析按钮 */}
          <div className="flex items-center gap-2">
            {showAnalysisButton && (
              <AnalysisButton
                onClick={toggleAnalysis}
                isActive={showAnalysis}
              />
            )}

            {/* {reportButton} */}

            <FooterElement />
          </div>
        </div>

        {showAnalysis && (
          <div className="text-gray-2 bg-fill-gray-2 mb-3 flex w-full max-w-full flex-col items-start gap-2 rounded-xl p-4 px-6 pt-4">
            <AnswerAndExplain qaContent={qaContent} ossHost={ossHost} />
          </div>
        )}
      </div>
    </div>
  );
}
