import { StudyType } from "../../enums/question";

export const PRESS_BUTTON_THEMES = {
  "--color-white-bg": "#FFFFFF",
  "--color-white-hover": "#F9FAFB",
  "--color-white-border": "rgba(51, 46, 41, 0.06)",
  "--color-gray-bg": "#F4F3F2",
  "--color-gray-text": "rgba(51, 48, 45, 0.7)",
  "--color-green-1": "#84D64B",
  "--color-green-2": "#DFF2D1",
  "--color-green-text": "#449908",
  "--color-red-1": "#F66042",
  "--color-red-2": "#FFE5DF",
  "--color-red-text": "#D1320A",
  "--color-orange-1": "#FF902E",
  "--color-orange-2": "#FFEDDF",
  "--color-orange-text": "#CC6204",
  "--btn-orange-shadow": "#F27813",
  "--btn-red-shadow": "#D94123",
  "--btn-green-shadow": "rgba(115, 178, 71, 0.3)",
  "--btn-gray-shadow": "rgba(51, 46, 41, 0.2)",
  "--btn-white-shadow": "rgba(51, 46, 41, 0.2)",
  "--btn-shadow-orange-primary": "#F27813",
  "--btn-shadow-orange-secondary": "#F1CFB5",
  "--btn-shadow-red-primary": "#E36544",
  "--btn-shadow-red-secondary": "#F4D3CC",
  "--btn-shadow-green-primary": "#73B247",
  "--btn-shadow-green-secondary": "#D0E7C0",
  "--btn-shadow-white": "var(--color-divider-1)",
  "--btn-shadow-gray": "#E0DEDC",
};

/**
 * 练习类型主题色配置
 */
export interface StudyTypeTheme {
  /** 主色调 */
  primary: string;
  /** 主色调浅色版本 */
  primaryLight: string;
  /** 主色调深色版本 */
  primaryDark: string;
  /** 背景色 */
  background: string;
  /** 背景色浅色版本 */
  backgroundLight: string;
  /** 文字颜色 */
  textColor: string;
  /** 边框颜色 */
  borderColor: string;
  /** 阴影颜色 */
  shadowColor: string;
  /** 阴影颜色浅色版本 */
  shadowColorLight: string;
  /** 渐变背景 */
  gradient?: {
    from: string;
    to: string;
    direction:
      | "to-r"
      | "to-l"
      | "to-t"
      | "to-b"
      | "to-tr"
      | "to-tl"
      | "to-br"
      | "to-bl";
  };
  /** 反馈颜色 */
  feedback?: {
    correct: {
      primary: string;
      rightBottom: string;
      topOne: string;
      topTwo: string;
      gradient: {
        from: string;
        to: string;
        direction: string;
      };
    };
    continuousCorrect: {
      primary: string;
      rightBottom: string;
      topOne: string;
      topTwo: string;
      topGradient: string;
      centerGradient: string;
      centerTwoGradient: string;
      bottomGradient: string;
    };
  };
}

/**
 * 练习类型主题色映射表
 */
export const STUDY_TYPE_THEMES: Record<StudyType, StudyTypeTheme> = {
  [StudyType.AI_COURSE]: {
    primary: "#4DB5FF", // 橙色 - 巩固练习主色调
    primaryLight: "#FFEDDF",
    primaryDark: "#CC6204",
    background: "#F3FBFF", // 浅橙色背景
    backgroundLight: "#FFEDDF",
    textColor: "#CC6204",
    borderColor: "#FED7CC",
    shadowColor: "#30A3F5",
    shadowColorLight: "#F1CFB5",
    gradient: {
      from: "#FF5416",
      to: "#FF50A4",
      direction: "to-r",
    },
    feedback: {
      correct: {
        primary: "#FF902E",
        rightBottom: "#FF3351",
        topOne: "#FF7125",
        topTwo: "#FF7125",
        gradient: {
          from: "#FE512E",
          to: "#FA8329",
          direction: "to-r",
        },
      },
      continuousCorrect: {
        primary: "rgba(255,236,233,0.80)",
        rightBottom: "#FF7125",
        topOne: "#FF7125",
        topTwo: "#FF7125",
        topGradient:
          "linear-gradient(271deg,#FE8652 -0.74%,rgba(254, 134, 82, 0.00) 67.05%)",
        centerGradient:
          "linear-gradient(278deg,rgba(255, 109, 52, 0.50) 0.57%,#F55366 77.32%,#FF85BA 103.15%)",
        centerTwoGradient:
          "linear-gradient(270deg, #FE8652 0%, rgba(254,134,82,0) 45%)",
        bottomGradient:
          "linear-gradient(271deg,rgba(254, 82, 119, 0.00) 31.03%,#FE5277 98.82%)",
      },
    },
  },
  [StudyType.REINFORCEMENT_EXERCISE]: {
    primary: "#4DB5FF", // 橙色 - 巩固练习主色调
    primaryLight: "#FFEDDF",
    primaryDark: "#CC6204",
    background: "#F3FBFF", // 浅橙色背景
    backgroundLight: "#FFEDDF",
    textColor: "#CC6204",
    borderColor: "#FED7CC",
    shadowColor: "#30A3F5",
    shadowColorLight: "#F1CFB5",
    gradient: {
      from: "#FF5416",
      to: "#FF50A4",
      direction: "to-r",
    },
    feedback: {
      correct: {
        primary: "#FF902E",
        rightBottom: "#FF3351",
        topOne: "#FF7125",
        topTwo: "#FF7125",
        gradient: {
          from: "#FE512E",
          to: "#FA8329",
          direction: "to-r",
        },
      },
      continuousCorrect: {
        primary: "rgba(255,236,233,0.80)",
        rightBottom: "#FF7125",
        topOne: "#FF7125",
        topTwo: "#FF7125",
        topGradient:
          "linear-gradient(271deg,#FE8652 -0.74%,rgba(254, 134, 82, 0.00) 67.05%)",
        centerGradient:
          "linear-gradient(278deg,rgba(255, 109, 52, 0.50) 0.57%,#F55366 77.32%,#FF85BA 103.15%)",
        centerTwoGradient:
          "linear-gradient(270deg, #FE8652 0%, rgba(254,134,82,0) 45%)",
        bottomGradient:
          "linear-gradient(271deg,rgba(254, 82, 119, 0.00) 31.03%,#FE5277 98.82%)",
      },
    },
  },
  [StudyType.EXPAND_EXERCISE]: {
    primary: "#52CC7A", // 绿色 - 拓展练习主色调
    primaryLight: "#F0F9E8",
    primaryDark: "#5A9B2A",
    background: "#E6FAF5", // 浅青色背景
    backgroundLight: "#F0FDFA",
    textColor: "#5A9B2A",
    borderColor: "#D4F0B0",
    shadowColor: "#00B23B",
    shadowColorLight: "#D0E7C0",
    gradient: {
      from: "#7EE5CC",
      to: "#57D962",
      direction: "to-r",
    },
    feedback: {
      correct: {
        primary: "#52CC7A",
        rightBottom: "#66FF33",
        topOne: "#3FFF8C",
        topTwo: "#3FFF8C",
        gradient: {
          from: "#1FC689",
          to: "#30CD5F",
          direction: "to-r",
        },
      },
      continuousCorrect: {
        primary: "rgba(214,244,236,0.4)",
        rightBottom: "#66FF33",
        topOne: "#3FFF8C",
        topTwo: "#3FFF8C",
        topGradient:
          "linear-gradient(271deg,#73F1E9 -0.74%,rgba(115, 241, 233, 0) 67.05%)",
        centerGradient:
          "linear-gradient(278deg,rgba(101, 217, 130, 0.5) 0.57%,#4DD16E 77.32%,#46DDE3 103.15%)",
        centerTwoGradient:
          "linear-gradient(270deg, #FEF552 0%, rgba(254, 245, 82, 0) 45%)",
        bottomGradient:
          "linear-gradient(271deg,rgba(142, 254, 82, 0) 31.03%,#8EFE52 98.82%)",
      },
    },
  },
};

/**
 * 获取练习类型对应的主题色配置
 * @param studyType 练习类型
 * @returns 主题色配置
 */
export function getStudyTypeTheme(studyType: StudyType): StudyTypeTheme {
  return STUDY_TYPE_THEMES[studyType] || STUDY_TYPE_THEMES[StudyType.AI_COURSE];
}

/**
 * 根据主题配置生成CSS变量对象
 * @param theme 主题配置
 * @returns CSS变量对象
 */
export function createCSSVariablesFromTheme(
  theme: StudyTypeTheme
): Record<string, string> {
  return {
    ...PRESS_BUTTON_THEMES,
    "--study-primary": theme.primary,
    "--study-primary-light": theme.primaryLight,
    "--study-primary-dark": theme.primaryDark,
    "--study-background": theme.background,
    "--study-background-light": theme.backgroundLight,
    "--study-text-color": theme.textColor,
    "--study-border-color": theme.borderColor,
    "--study-shadow-color": theme.shadowColor,
    "--study-shadow-color-light": theme.shadowColorLight,
    "--study-gradient-from": theme.gradient?.from || theme.primary,
    "--study-gradient-to": theme.gradient?.to || theme.primary,
    "--study-gradient-direction": theme.gradient?.direction || "to-r",
    "--study-feedback-correct-primary": theme.feedback?.correct.primary || "",
    "--study-feedback-correct-gradient-from":
      theme.feedback?.correct.gradient.from || "",
    "--study-feedback-correct-gradient-to":
      theme.feedback?.correct.gradient.to || "",
    "--study-feedback-correct-gradient-direction":
      theme.feedback?.correct.gradient.direction || "",
    "--study-feedback-continuous-correct-primary":
      theme.feedback?.continuousCorrect.primary || "",
    "--study-feedback-correct-right-bottom":
      theme.feedback?.correct.rightBottom || "",
    "--study-feedback-correct-top-one": theme.feedback?.correct.topOne || "",
    "--study-feedback-correct-top-two": theme.feedback?.correct.topTwo || "",
    "--study-feedback-continuous-correct-right-bottom":
      theme.feedback?.continuousCorrect.rightBottom || "",
    "--study-feedback-continuous-correct-top-one":
      theme.feedback?.continuousCorrect.topOne || "",
    "--study-feedback-continuous-correct-top-two":
      theme.feedback?.continuousCorrect.topTwo || "",
    "--study-feedback-continuous-correct-top-gradient":
      theme.feedback?.continuousCorrect.topGradient || "",
    "--study-feedback-continuous-correct-center-gradient":
      theme.feedback?.continuousCorrect.centerGradient || "",
    "--study-feedback-continuous-correct-center-two-gradient":
      theme.feedback?.continuousCorrect.centerTwoGradient || "",
    "--study-feedback-continuous-correct-bottom-gradient":
      theme.feedback?.continuousCorrect.bottomGradient || "",
  };
}

/**
 * 获取练习类型对应的CSS变量
 * @param studyType 练习类型
 * @returns CSS变量对象
 */
export function getStudyTypeCSSVariables(
  studyType: StudyType
): Record<string, string> {
  const theme = getStudyTypeTheme(studyType);
  return createCSSVariablesFromTheme(theme);
}

/**
 * 获取练习类型对应的Tailwind CSS类名
 * @param studyType 练习类型
 * @returns Tailwind CSS类名对象
 */
export function getStudyTypeTailwindClasses(studyType: StudyType): {
  background: string;
  text: string;
  border: string;
  shadow: string;
  gradient: string;
} {
  const theme = getStudyTypeTheme(studyType);

  return {
    background: `bg-[${theme.background}]`,
    text: `text-[${theme.textColor}]`,
    border: `border-[${theme.borderColor}]`,
    shadow: `shadow-[0_4px_12px_${theme.shadowColor}]`,
    gradient: theme.gradient
      ? `bg-gradient-${theme.gradient.direction} from-[${theme.gradient.from}] to-[${theme.gradient.to}]`
      : `bg-[${theme.primary}]`,
  };
}
