"use client";

import { QUESTION_TYPE, questionTypeEnumManager } from "@repo/core/enums";
import type { QuestionTag } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React, { useMemo } from "react";
import { FormatMath } from "../format-math";

interface QuestionContentProps {
  content: React.ReactNode | string;
  type: QUESTION_TYPE;
  questionSourceInfo?: QuestionTag | null;
  questionTags?: string[];
  className?: string;
  fillBlankQuestionContentView?: React.ReactNode;
  questionId: string;
}

export const QuestionContent: React.FC<QuestionContentProps> = ({
  questionId,
  content,
  type,
  questionSourceInfo,
  questionTags = [],
  className,
  fillBlankQuestionContentView,
}) => {
  const getQuestionTypeLabel = (type: QUESTION_TYPE): string => {
    return questionTypeEnumManager.getLabelByValue(type) || "";
  };

  // 格式化题目来源信息：年份加点加省份，如果没有省份就不加点，如果都没有就什么都不展示
  const displaySourceInfo = useMemo(() => {
    // console.log("questionSourceInfo", questionSourceInfo);

    if (!questionSourceInfo) {
      return "";
    }

    const { year, province = "", city = "", area = "" } = questionSourceInfo;
    // 拼接省市区信息
    const areaInfo = province + city + area;
    // 如果年份和省份都为空，返回空字符串
    if (!year && !areaInfo) {
      return "";
    }

    // 如果只有年份，返回年份
    if (year && !areaInfo) {
      return year;
    }
    // 如果只有省份，返回空字符串（按照需求，年份是必须的）
    if (!year && areaInfo) {
      return "";
    }
    // 如果年份和省份都有，返回"年份·省份"
    return `${year}·${areaInfo}`;
  }, [questionSourceInfo]);

  return (
    <div className={cn("question-content relative w-full", className)}>
      {/* 副背景SVG - 自适应高度 */}
      {/* <div className="absolute -left-4 top-0 z-0 -rotate-[1] w-32 h-full pt-[5%]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="126"
          height="100%"
          viewBox="0 0 136 485"
          fill="none"
          preserveAspectRatio="none"
          className="w-full h-full"
        >
          <path
            d="M0.855913 15.1349C0.740249 8.50848 6.01825 3.04295 12.6447 2.92729L126.627 0.937716L135.353 500.862L9.37269 503.061L0.855913 15.1349Z"
            fill="url(#paint0_linear_3864_13121)"
            fillOpacity="0.5"
          />
          <path
            d="M12.6534 3.42721L126.136 1.44637L134.845 500.37L9.86388 502.552L1.35584 15.1262C1.24499 8.77586 6.30308 3.53806 12.6534 3.42721Z"
            stroke="#332E29"
            strokeOpacity="0.03"
          />
          <defs>
            <linearGradient
              id="paint0_linear_3864_13121"
              x1="63.6369"
              y1="2.03722"
              x2="72.3631"
              y2="501.961"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#EED4C3" />
              <stop offset="1" stopColor="#F4ECE6" />
            </linearGradient>
          </defs>
        </svg>
      </div> */}

      {/* 白色主背景 */}
      <div className="question-main-content relative z-10 h-full w-full rounded-t-xl border border-[#F3F2F2] bg-white px-6 pb-3">
        <div className="mt-3 h-full max-h-full w-full overflow-y-auto pt-5">
          {/* 题目内容 */}
          <div className="question-header mb-5 flex items-center gap-2">
            <div className="question-type-tag flex h-6 items-center rounded-[0.25rem] border border-[rgba(103,191,255,0.40)] bg-[rgba(103,191,255,0.20)] px-[0.375rem] text-[0.8125rem] font-medium leading-[1.75] text-[#3390D1]">
              {getQuestionTypeLabel(type)}
            </div>
            {questionTags.map((tag) => (
              <div className="question-tag flex h-6 items-center rounded-[0.25rem] border border-[rgba(103,191,255,0.40)] bg-[rgba(103,191,255,0.20)] px-[0.375rem] text-[0.8125rem] font-medium leading-[1.75] text-[#3390D1]">
                {tag}
              </div>
            ))}
            {displaySourceInfo && (
              <div className="question-source text-text-4 text-sm font-[1.0625rem]">
                {displaySourceInfo}
              </div>
            )}
          </div>
          {type === QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK ||
          type === QUESTION_TYPE.QUESTION_TYPE_CLOZE ? (
            fillBlankQuestionContentView
          ) : (
            <div className="question-title text-text-1 mb-5 text-base">
              <FormatMath
                htmlContent={String(content)}
                questionId={questionId}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
