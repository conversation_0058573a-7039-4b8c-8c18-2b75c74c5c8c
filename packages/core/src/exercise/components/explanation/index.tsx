"use client";

import type { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React, {
  lazy,
  memo,
  Suspense,
  useCallback,
  useMemo,
  useState,
} from "react";
import { FormatMath } from "../format-math";

// 🔥 动态导入互动讲题组件，避免影响主包体积 - 移到组件外部避免重复创建
const InteractiveExplanationLazy = lazy(() =>
  import("@repo/core/interactive-explanation/view/index").catch(() => ({
    default: () => <div>互动讲题组件加载失败</div>,
  }))
);

// 导入SVG图标
import { StudyType } from "@repo/core/enums";
import { enterTracking } from "@repo/core/interactive-explanation/hooks/use-interactive-explanation-tracking";
import ArrowRightIcon from "@repo/core/public/assets/interactive-explanation/arrow-right.svg";
import StarIcon from "@repo/core/public/assets/interactive-explanation/star-icon.svg";
import { useParams, usePathname } from "next/navigation";

// 🔥 优化：使用memo避免不必要的重渲染
const InteractiveExplanationEntry = memo(function InteractiveExplanationEntry({
  onOpen,
}: {
  onOpen: () => void;
}) {
  return (
    <div className="interactive-explanation-entry mb-4 flex h-12 w-full items-center gap-1 rounded-[2.25rem] bg-white py-2 pl-4 pr-2 shadow-[0px_12px_40px_0px_rgba(64,43,26,0.05)]">
      {/* 左侧内容区域 */}
      <div className="flex min-w-0 flex-1 items-center gap-1">
        {/* 星形图标 */}
        <StarIcon className="mr-1 h-4 w-4 flex-shrink-0" />

        {/* 文本内容 */}
        <div className="flex min-w-0 items-center gap-[0.0625rem]">
          <span className="text-text-2 whitespace-nowrap text-[1.0625rem] font-normal leading-[1.25em]">
            解析没看懂？一步一步学明白
          </span>
        </div>
      </div>

      {/* 右侧按钮 */}
      <button
        onClick={onOpen}
        className="interactive-explanation-button flex h-8 flex-shrink-0 items-center justify-center gap-1 rounded-[1.75rem] border border-[#FFA666] bg-[rgba(255,166,102,0.15)] px-3 py-[0.625rem] pl-3 pr-2 transition-colors duration-200 hover:bg-[rgba(255,166,102,0.25)]"
      >
        <span className="whitespace-nowrap text-xs font-medium leading-[1.25em] text-[#CC6204]">
          逐步讲解
        </span>
        <div className="flex h-[0.875rem] w-[0.875rem] items-center justify-center">
          <ArrowRightIcon className="h-full w-full" />
        </div>
      </button>
    </div>
  );
});

interface QuestionExplanationProps {
  questionId: string;
  /** 解析内容 */
  explanation: string;
  /** 额外的CSS类名 */
  className?: string;
  /** 是否显示标题 */
  showTitle?: boolean;
  /** 自定义标题文本 */
  title?: string;
  /** 🔥 互动解题数据 */
  aiExplanation?: AiExplanation | null;
  /** 🔥 互动解题记录 */
  aiExplanationRecord?: AiExplanationRecord | null;
  /** 🔥 学习会话ID，用于互动讲题进度上报 */
  studySessionId?: number;
  /** 是否是预览模式 */
  preview?: boolean;
  studyType?: StudyType;
}

/**
 * 题目解析组件
 * 用于显示题目的答案解析内容，支持数学公式渲染
 * 🔥 新增：支持互动讲题功能，当有aiExplanation数据时显示互动讲题按钮
 * 根据Figma设计稿实现：水平布局，标题在左，解析内容在右
 */
export const QuestionExplanation: React.FC<QuestionExplanationProps> = memo(
  function QuestionExplanation({
    questionId,
    explanation = "暂无解析",
    className,
    showTitle = true,
    title = "题目解析:",
    aiExplanation,
    aiExplanationRecord,
    studySessionId,
    preview = false,
    studyType,
  }: QuestionExplanationProps) {
    // 🔥 互动讲题浮层状态
    const [isInteractiveVisible, setIsInteractiveVisible] = useState(false);

    // 🔥 优化：使用useMemo缓存计算结果, 预览模式下也显示互动讲题按钮
    const hasInteractiveExplanation = useMemo(
      () => !!(aiExplanation && (studyType === StudyType.AI_COURSE || preview)),
      [aiExplanation, studyType]
    );
    const pathname = usePathname();
    const lessonId = useParams().lessonId as string;
    // 🔥 优化：使用useCallback缓存事件处理函数
    const handleInteractiveClick = useCallback(() => {
      enterTracking({
        pathname,
        preview,
        questionId,
        studySessionId,
        studyType: studyType!,
        lessonId: lessonId!,
      });
      setIsInteractiveVisible(true);
    }, []);

    // 🔥 优化：使用useCallback缓存事件处理函数
    const handleInteractiveClose = useCallback(() => {
      setIsInteractiveVisible(false);
    }, []);

    return (
      <div
        className={cn(
          "question-explanation flex w-full flex-col items-start gap-2",
          className
        )}
      >
        {/* 标题 */}
        {showTitle && (
          <div className="explanation-title text-text-1 text-[1.0625rem] font-bold leading-[1.5em]">
            {title}
          </div>
        )}

        {/* 传统解析内容 - 🔥 优化：使用useMemo缓存FormatMath组件 */}
        <div className="explanation-content text-text-4 flex-1 text-[1.0625rem] font-normal">
          {useMemo(
            () => (
              <FormatMath htmlContent={explanation} questionId={questionId} />
            ),
            [explanation, questionId]
          )}
        </div>

        {/* 🔥 互动讲题按钮 - 优先显示 */}
        {hasInteractiveExplanation && (
          <>
            <InteractiveExplanationEntry onOpen={handleInteractiveClick} />
            {/* 🔥 互动讲题浮层 - 动态导入避免打包体积 */}
            {isInteractiveVisible && (
              <Suspense fallback={<></>}>
                <InteractiveExplanationLazy
                  preview={preview}
                  questionId={questionId}
                  studySessionId={studySessionId || 0}
                  isVisible={isInteractiveVisible}
                  onClose={handleInteractiveClose}
                  initialData={aiExplanation!}
                  initialRecord={aiExplanationRecord || undefined}
                  studyType={studyType!}
                />
              </Suspense>
            )}
          </>
        )}
      </div>
    );
  }
);
