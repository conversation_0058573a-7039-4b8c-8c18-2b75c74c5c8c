"use client";

import {
  FeedbackType,
  SelfEvaluationResult,
  StudyType,
} from "@repo/core/enums";
import { CommonQuestion } from "@repo/core/types";

// 🔥 原生 TypeScript 类型定义，替代 zod schema 推导

/**
 * IP角色类型
 */
export type IpRole = string;

/**
 * 反馈数据接口
 */
export interface FeedbackData {
  content?: string | null;
}

/**
 * 特殊反馈数据接口
 */
export interface SpecialFeedback {
  content?: string | null;
  type?: FeedbackType | null;
  title?: string | null;
}

/**
 * API 进度信息接口
 */
export interface ApiProgressInfo {
  /** 已完成题目数 */
  completedQuestions?: number;
  /** 当前进度百分比，0-100 */
  currentProgress?: number;
  /** 预估题目总数 */
  totalQuestions: number;
}

/**
 * 下一题信息接口
 */
export interface NextQuestionInfo extends CommonQuestion {
  questionIndex?: number;
  questionDifficulty?: number;
  progressInfo?: ApiProgressInfo;
  hasNextQuestion?: boolean | null;
  isResume?: boolean;
  lastAnswerDuration?: number;
  /** 题目标签，如：相似题、易错题 */
  questionTags?: string[];
}

/**
 * 答案结果接口
 */
export interface AnswerResult {
  /** 自动判题结果 (0:未作答, 1:正确, 2:错误, 3:部分正确) */
  answerVerify?: number | null;
  /** 序号 */
  index: number;
  /** 题目 id，支持母子题 */
  questionId: string;
}

/**
 * 提交答案响应接口
 */
export interface SubmitAnswerResponse {
  answerCount: number;
  answerResult: AnswerResult[];
  /**
   * 答题统计，支持母子题
   */
  answerStats?: AnswerStat[];
  correctComboCount?: number;
  maxCorrectComboCount?: number;
  exerciseCompleted?: boolean;
  feedback: FeedbackData;
  hasNextQuestion: boolean;
  nextQuestionGroupInfo?: NextQuestionGroupInfo | null;
  nextQuestionInfo?: NextQuestionInfo | null;
  nextQuestionTags?: string[];
  progressInfo?: ApiProgressInfo | null;
  specialFeedbacks?: SpecialFeedback[] | null;
  allCorrect?: boolean;
}

export interface NextQuestionGroupInfo {
  groupCount: number;
  groupIndex: number;
  groupName: string;
  groupQuestionCount: number;
  preGroupName?: string; // 上一个题组名称
  nextGroupName?: string; // 下一个题组名称
}

/**
 * 提交答案接口
 */
export interface SubmitAnswer {
  questionId: string;
  questionType: number;
  studentAnswers: Array<{
    content: string[];
    index: number;
    questionId: string;
    type: number;
    selfEvaluations?: (0 | 1 | 2 | 3)[];
  }>;
  isGiveup?: boolean;
  answerTime?: number;
}

/**
 * 获取下一题数据接口
 */
export interface ApiGetNextQuestionData {
  hasNextQuestion: boolean;
  isResume: boolean;
  progressInfo?: ApiProgressInfo;
  questionInfo?: NextQuestionInfo;
  questionTags?: string[];
  studySessionId: number;
  feedback?: FeedbackData;
  lastAnswerDuration?: number;
  questionIndex?: number;
  questionDifficulty?: number;
  studentAnswer: StudentAnswer;
  nextQuestionGroupInfo?: NextQuestionGroupInfo;
}

/**
 * 退出会话响应接口
 */
export interface ApiExitSessionResponse {
  exited: boolean;
}

// 题目状态
export type QuestionState =
  | "answering" // 正在作答（首次或二次错误后重置）
  | "uncertain" // 不确定
  | "giving_up" // 放弃作答
  | "first_attempt_incorrect" // 首次提交错误（仅适用于允许二次提交的题型）
  | "awaiting_self_evaluation" // 等待自评（主观题第一步提交完成，等待用户自评）
  | "submitted"; // 已提交（最终状态，进入解析）
/**
 * 进度信息 - 匹配真实后端定义
 */
export interface ProgressInfo {
  /**
   * 已完成题目数（后端定义为 number，可选）
   */
  completedQuestions?: number;
  /**
   * 当前进度百分比（可选）
   */
  currentProgress?: number;
  /**
   * 预估题目总数（后端定义为 number，必填）
   */
  totalQuestions: number;
}

/**
 /**
  * 提交答案请求参数
  */
export interface SubmitAnswerPayload {
  /**
   * 作答时长（毫秒）
   */
  answerDuration: number;
  /**
   * 是否放弃作答
   */
  isGiveup?: boolean;
  /**
   * 题目ID，如果是母子题，则为母题 ID
   */
  questionId: string;
  /**
   * 学生作答，包括母子题
   */
  studentAnswers: Array<{
    /**
     * 作答内容，数组格式，支持多选题，多填空题等类型题目，支持多图片地址
     */
    content: string[];
    /**
     * 答题序号，从0开始
     */
    index: number;
    /**
     * 题目 ID，用于母子题识别
     */
    questionId: string;
    /**
     * 回答类型, 1:文本(默认), 2:图片, 3:视频
     */
    type: number;
  }>;
  /**
   * 练习会话ID
   */
  studySessionId: number;
  /**
   * 组件序号，仅 AI 课中的练习需要此字段
   */
  widgetIndex?: number;
}

/**
 * 获取下一题请求参数
 */
export interface GetNextQuestionParams {
  // ===== 必填参数 =====
  /**
   * 知识点ID，必须
   */
  knowledgeId: number;
  /**
   * 学段ID，必须
   */
  phaseId: number;
  /**
   * 学习类型 (1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 99:其他)，必须
   */
  studyType: StudyType;
  /**
   * 科目ID，必须
   */
  subjectId: number;
  /**
   * 教材ID，巩固练习必须
   */
  textbookId: number;

  // ===== 可选参数 =====
  /**
   * 课程ID，AI课必须
   */
  lessonId?: number;
  /**
   * 题集ID，巩固练习需提供
   */
  questionSetId?: number;
  /**
   * 会话ID，巩固练习需提供，AI课首次请求不需要
   */
  studySessionId?: number;
  /**
   * 组件序号，AI课必须
   */
  widgetIndex?: number;
}

/**
 * 🔥 新版答题统计数据结构
 */
export interface AnswerStat {
  /**
   * 答题统计，只客观题有统计
   */
  answerStat: AnswerStatRate[];
  /**
   * 题目 ID
   */
  questionId: string;
}

/**
 * 答案选择分布统计（单个选项）
 */
export interface AnswerStatRate {
  /**
   * 选项
   */
  optionKey: string;
  /**
   * 选择比例
   */
  rate: string;
}

export type GroupTransitionFeedbackData = NextQuestionGroupInfo & {
  type: "question_group_transition";
};

/**
 * 题组信息
 */
export interface QuestionGroupInfo {
  /**
   * 题组数量
   */
  groupCount: number;
  /**
   * 题组序号
   */
  groupIndex: number;
  /**
   * 题组名称
   */
  groupName: string;
  /**
   * 题组题目数量
   */
  groupQuestionCount: number;
}

/**
 * 学生答题内容接口
 */
export interface StudentAnswerItem {
  questionId: string;
  index: number;
  type: number;
  content: string[];
  selfEvaluations?: SelfEvaluationResult[];
  answerResult?: number;
}

/**
 * 答题详情历史数据接口-学生作答的数据
 */
export interface StudentAnswer {
  answerStats?: AnswerStat[];
  /**
   * 答题详情，数组，支持母子题
   */
  answerContents: StudentAnswerItem[];
  /**
   * 答题用时，毫秒
   */
  answerDuration: number;
  /**
   * 判题结果 (0:未作答, 1:正确, 2:错误, 3:部分正确)
   */
  answerResult: number;

  /**
   * 回答类型, 1:文本(默认), 2:图片, 3:视频
   */
  answerType: number;
  /**
   * 判题类型，1:系统判题, 2:自评判题
   */
  evaluationType: number;
  /**
   * 题目 id
   */
  questionId: string;
}

type PartialByKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// 🔥 扩展 PreviewQuestionInfo 类型，支持对象格式的 questionContent
export type PreviewQuestionInfo = PartialByKeys<
  NextQuestionInfo,
  "progressInfo" | "hasNextQuestion"
>;

// 预览模式数据类型 - 包含题目信息和学生答题记录
export interface PreviewQuestionData {
  // 题目基础信息
  questionInfo: PreviewQuestionInfo;
  // 学生答题记录（可选，有则表示已作答）
  studentAnswer?: StudentAnswer;
}

// 🔥 通用的按钮控制参数类型，未来可复用
export interface PreviewExerciseCbParams {
  currentIndex: number;
  total: number;
  isLastQuestion: boolean;
  isFirstQuestion: boolean;
  progress: number;
  questionId?: string;
  questionType?: number;
}

// 用户答案数据类型定义
export interface UserAnswerData {
  // 选择题答案: 单选题、多选题、判断题
  choiceAnswers?: string[];

  // 主观题答案: 问答题、填空题
  subjectiveAnswer?: string[];

  // 判断题答案
  trueFalseAnswer?: boolean;

  // 主观题输入模式
  inputMode?: "keyboard" | "camera";

  // 图片文件 (拍照模式)
  imgFiles?: string[];
}
