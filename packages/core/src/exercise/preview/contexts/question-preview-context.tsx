"use client";

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";

import { ProgressDisplayMode } from "@repo/core/exercise/components";
import {
  AnswerStat,
  NextQuestionInfo,
  QuestionState,
  StudentAnswer,
  StudentAnswerItem,
} from "@repo/core/exercise/model/types";

// 集成了ViewModel的完整Context接口
interface QuestionContextValue {
  showExplanations: boolean;
  actionButtons: ReactNode;
  answerStats: AnswerStat[] | null;
  // ===== 原始配置 =====
  // mode: 'direct' | 'url';
  questionData?: NextQuestionInfo;

  onComplete?: (nextQuestionInfo?: NextQuestionInfo) => void;
  onBack?: () => void;

  // ===== 题目数据 =====
  currentQuestion: NextQuestionInfo;
  currentStudentAnswer?: StudentAnswerItem;
  // ===== 题目状态 =====
  questionState: QuestionState;
  isInWrongQuestionBank: boolean;

  // ===== 计时器 =====
  getTimeSpent: () => number; // 业务逻辑用（获取准确时间）
  timerControl: {
    isActive: boolean | null;
    onTimeUpdate: (timeMs: number) => void; // 改为毫秒
    shouldReset: string; // 题目ID变化时重置
  };

  // ===== 答案状态管理 =====
  userAnswerData: {
    choiceAnswers?: string[];
    subjectiveAnswer?: string[];
    englishFillBlankAnswers?: string[];
    inputMode?: "keyboard" | "camera";
    imgFiles?: File[];
  };
  updateUserAnswer: (
    answerData: Partial<QuestionContextValue["userAnswerData"]>
  ) => void;
  clearUserAnswer: () => void;
  clearChoiceAnswer: () => void;
  isAnswerComplete: boolean;
  setQuestionState: (newState: QuestionState) => void;

  currentIndex?: number;
  totalCount?: number;
  // 🔥 新增：进度显示模式控制
  progressDisplayMode?: ProgressDisplayMode;
  // 预览数据（包含用时、答案统计等）
  previewData?: {
    answerDuration?: number; // 答题用时（毫秒）
    answerResult?: number; // 答题结果
    studentAnswer?: StudentAnswer; // 学生答题记录
  };
  studentAnswer?: StudentAnswer;
}

interface QuestionContextProviderProps {
  showExplanations?: boolean;
  children: ReactNode;
  currentQuestion: NextQuestionInfo;
  actionButtons: ReactNode;
  currentIndex?: number;
  totalCount?: number;
  // 🔥 新增：进度显示模式控制
  progressDisplayMode?: ProgressDisplayMode;
  // 预览数据（包含用时、答案统计等）
  previewData?: {
    answerDuration?: number; // 答题用时（毫秒）
    answerResult?: number; // 答题结果
    studentAnswer?: StudentAnswer; // 学生答题记录
  };
}

// 创建上下文 - 使用唯一名称避免与练习模式的 Context 冲突
const QuestionPreviewContext = createContext<QuestionContextValue | null>(null);

/**
 * 统一的题目上下文提供者
 *
 * ✅ 集成了 ViewModel 的完整功能：
 * - 自动调用 useQuestionViewModel 获取所有状态和方法
 * - 集成 useTransitionViewModel 管理转场逻辑
 * - 将配置和 ViewModel 数据合并提供给下层组件
 * - 下层组件只需要调用 useQuestionPreviewContext 即可获得所有功能
 *
 * 优势：
 * - 简化了组件调用，不需要同时使用多个 hooks
 * - 统一的数据源，避免状态不一致
 * - 更好的类型推导和代码提示
 */
export function QuestionPreviewContextProvider({
  showExplanations = true,
  children,
  currentQuestion,
  actionButtons,
  currentIndex,
  totalCount,
  progressDisplayMode = "number", // 🔥 默认显示数字模式
  previewData,
}: QuestionContextProviderProps) {
  // 🔧 根据是否有学生答案数据来设置初始状态
  const getInitialQuestionState = useCallback((): QuestionState => {
    // 预览模式：根据是否有学生答案数据决定状态
    const hasStudentAnswer =
      previewData?.studentAnswer?.answerContents &&
      previewData.studentAnswer.answerContents.length > 0;
    return hasStudentAnswer ? "submitted" : "answering";
  }, [previewData?.studentAnswer]);

  const [questionState, setQuestionState] = useState<QuestionState>(() =>
    getInitialQuestionState()
  );

  const currentStudentAnswer = useMemo(() => {
    return previewData?.studentAnswer?.answerContents?.find(
      (item) => item.questionId === currentQuestion.questionId
    );
  }, [previewData?.studentAnswer]);

  // 🔧 初始答案数据状态
  const initialUserAnswerData = useMemo(
    () => ({
      selectedOptions: [] as string[],
      subjectiveAnswer: [] as string[],
      englishFillBlankAnswers: [] as string[],
      inputMode: "keyboard" as "keyboard" | "camera",
      imgFiles: [] as File[],
    }),
    []
  );

  // 🔧 新增：统一的答案状态管理
  const [userAnswerData, setUserAnswerData] = useState(initialUserAnswerData);

  // 🔧 当题目变化时更新状态
  useEffect(() => {
    const newQuestionState = getInitialQuestionState();
    setQuestionState(newQuestionState);
  }, [currentQuestion.questionId, previewData]);

  // 更新用户答案数据
  const updateUserAnswer = useCallback(
    (answerData: Partial<typeof userAnswerData>) => {
      setUserAnswerData((prev) => ({ ...prev, ...answerData }));
    },
    []
  );

  // 清空用户答案数据
  const clearUserAnswer = useCallback(() => {
    setUserAnswerData(initialUserAnswerData);
  }, [initialUserAnswerData]);

  // 🔧 新增：清空选择题答案（用于首次错误后重新选择）
  const clearChoiceAnswer = useCallback(() => {
    // 🔧 优化：减少频繁的日志输出
    // console.log('[QuestionContext] 🔄 清空选择题答案');
    updateUserAnswer({ selectedOptions: [] });
  }, [updateUserAnswer]);

  // 合并配置和 ViewModel 数据 - 使用 useMemo 优化性能
  const contextValue: QuestionContextValue = useMemo(
    () => ({
      showExplanations,
      // 答案状态管理
      userAnswerData,
      updateUserAnswer,
      clearUserAnswer,
      clearChoiceAnswer,
      isAnswerComplete: false,
      setQuestionState,
      currentQuestion: currentQuestion,
      currentStudentAnswer,
      questionState,
      isInWrongQuestionBank: false,
      getTimeSpent: () => 0,
      timerControl: {
        isActive: false,
        onTimeUpdate: () => {},
        shouldReset: "",
      },
      // 🔥 传递完整的 answerStats 数据，由具体组件自行处理
      answerStats: previewData?.studentAnswer?.answerStats || null,
      actionButtons: actionButtons,

      currentIndex,
      totalCount,
      progressDisplayMode,
      previewData,
      studentAnswer: previewData?.studentAnswer,
    }),
    [
      showExplanations,
      userAnswerData,
      updateUserAnswer,
      clearUserAnswer,
      clearChoiceAnswer,
      setQuestionState,
      currentQuestion,
      questionState,
      actionButtons,
      currentIndex,
      totalCount,
      progressDisplayMode,
      previewData,
    ]
  );

  return (
    <QuestionPreviewContext.Provider value={contextValue}>
      {children}
    </QuestionPreviewContext.Provider>
  );
}

export function useQuestionPreviewContext() {
  const context = useContext(QuestionPreviewContext);

  if (!context) {
    // 🔥 增强错误信息，帮助调试生产环境问题
    console.error("[QuestionPreviewContext] Context not found", {
      stack: new Error().stack,
      location:
        typeof window !== "undefined" ? window.location.href : "unknown",
      userAgent:
        typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
      timestamp: new Date().toISOString(),
    });
    throw new Error(
      "useQuestionPreviewContext must be used within a QuestionPreviewContextProvider"
    );
  }

  return context;
}
