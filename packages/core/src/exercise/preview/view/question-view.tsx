"use client";

import { QUESTION_TYPE } from "@repo/core/enums";
import { QuestionExplanation } from "@repo/core/exercise/components";
import { cn } from "@repo/ui/lib/utils";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useScrollDetection } from "../../../hooks/useScrollDetection";
import { ChoiceQuestionView } from "./components/choice-question-view";

import { QuestionContent as QuestionContentComponent } from "../../components/content/QuestionContent";
import type { NextQuestionInfo } from "../../model";
import { useQuestionPreviewContext } from "../contexts/question-preview-context";
import { useFillBlankViewModel } from "../viewmodel/fill-blank-question-viewmodel";
import { AnswerAreaView } from "./components/fill-blank-answer-area-view";
import { FillBlankQuestionContentView } from "./components/fill-blank-question-content-view";

export const AnswerMask = (props: {
  className?: string;
  bgColor?: string;
  hasOverflow: { y: boolean };
  scrollState: { bottom: boolean };
}) => {
  const _bgColor = props.bgColor || "#FEF8F4";
  return (
    <div
      className={cn(
        "pointer-events-none absolute bottom-2 right-0 h-[7rem] w-[50%] transition-opacity duration-200",
        props.hasOverflow && !props.scrollState.bottom
          ? "opacity-100"
          : "opacity-0"
      )}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="518"
        height="112"
        viewBox="0 0 518 112"
        fill="none"
        className={props.className}
      >
        <path
          d="M518 2.38419e-07L0 4.5285e-05L9.79135e-06 112L518 112L518 2.38419e-07Z"
          fill="url(#paint0_linear_7677_3727)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_7677_3727"
            x1="245.31"
            y1="0"
            x2="245.31"
            y2="112"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor={_bgColor} stopOpacity="0" />
            <stop offset="0.1" stopColor={_bgColor} stopOpacity="0.7" />
            <stop offset="0.3" stopColor={_bgColor} />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};
export const QuestionView: React.FC = () => {
  // 🎯 从统一Context获取所有数据和方法（包括转场功能）
  const { currentQuestion, questionState, actionButtons, showExplanations } =
    useQuestionPreviewContext();

  // 添加过渡状态管理，减少页面闪烁
  const previousQuestionRef = useRef<NextQuestionInfo | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🔧 新增：滚动位置保持
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);

  // 🆕 滚动检测功能 - 监听底部滚动状态
  const { sentinels, hasOverflow, scrollState } = useScrollDetection(
    scrollContainerRef,
    [{ direction: "bottom", threshold: 0.1 }]
  );

  // 使用 useMemo 来稳定题目数据，避免不必要的重新渲染
  const stableQuestion = useMemo(() => {
    if (!currentQuestion) return null;

    // 如果题目ID发生变化，标记为过渡状态
    if (
      previousQuestionRef.current &&
      previousQuestionRef.current.questionId !== currentQuestion.questionId
    ) {
      setIsTransitioning(true);
      // 短暂延迟后取消过渡状态，让新题目平滑显示
      setTimeout(() => setIsTransitioning(false), 50);
    }

    previousQuestionRef.current = currentQuestion;
    return currentQuestion;
  }, [currentQuestion]);

  // ✅ 使用ViewModel处理业务逻辑 - 符合MVVM架构（必须在顶层调用）
  // 为了遵循 React Hooks 规则，总是调用 hook，但传入安全的默认值
  const fillBlankViewModel = useFillBlankViewModel(
    currentQuestion || {
      questionId: "",
      questionType: QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
      questionContent: {
        questionOrder: 0,
        questionScore: 0,
        questionStem: "",
        questionOptionList: [],
      },
      questionAnswer: { answerOptionList: [] },
      isInWrongQuestionBank: false,
    }
  );

  // 🔧 新增：滚动位置保持逻辑
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 保存当前滚动位置
    const handleScroll = () => {
      scrollPositionRef.current = container.scrollTop;
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, []);

  // 🔧 新增：在组件更新后恢复滚动位置
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container && scrollPositionRef.current > 0) {
      // 使用 requestAnimationFrame 确保 DOM 更新完成后再恢复滚动位置
      requestAnimationFrame(() => {
        container.scrollTop = scrollPositionRef.current;
      });
    }
  });

  // 早期返回：如果 currentQuestion 为空，显示加载状态
  if (!stableQuestion) {
    return (
      <div className="question-loading-state flex h-full items-center justify-center">
        加载题目中...
      </div>
    );
  }

  return (
    <div
      className={cn(
        "question-view relative flex h-full flex-col overflow-hidden transition-opacity duration-200",
        isTransitioning ? "opacity-50" : "opacity-100"
      )}
    >
      <div className="question-content-wrapper flex flex-1 overflow-hidden pl-8">
        <QuestionContentComponent
          fillBlankQuestionContentView={
            <FillBlankQuestionContentView
              viewModel={{
                ...fillBlankViewModel,
                isReview: questionState === "giving_up",
              }}
            />
          }
          questionId={stableQuestion.questionId}
          className="w-[50%] pt-3"
          content={stableQuestion.questionContent.questionStem}
          type={
            stableQuestion.questionType ||
            QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE
          }
          questionSourceInfo={stableQuestion.questionTag}
          questionTags={stableQuestion.questionTags || []}
        />
        {/* 右侧答题区域 */}
        <div className="answer-area-wrapper flex w-[50%] flex-col rounded-xl">
          {/* Answer Area */}
          <div
            ref={scrollContainerRef}
            className="scroll-container w-full flex-1 overflow-y-auto overflow-x-hidden px-8 pt-3"
          >
            {(stableQuestion.questionType ===
              QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE ||
              stableQuestion.questionType ===
                QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE ||
              stableQuestion.questionType ===
                QUESTION_TYPE.QUESTION_TYPE_JUDGMENT) && (
              <ChoiceQuestionView questionState={questionState} />
            )}
            {stableQuestion.questionType ===
              QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK && (
              <AnswerAreaView
                viewModel={{
                  ...fillBlankViewModel,
                  isReview:
                    questionState === "submitted" ||
                    questionState === "giving_up",
                }}
                type="fill-blank"
              />
            )}
            {stableQuestion.questionType ===
              QUESTION_TYPE.QUESTION_TYPE_JUDGMENT && (
              <div className="true-false-container">
                <span className="text-text-4">判断题答题界面将在这里渲染</span>
              </div>
            )}
            {stableQuestion.questionType === QUESTION_TYPE.QUESTION_TYPE_QA && (
              <AnswerAreaView
                viewModel={{
                  ...fillBlankViewModel,
                  isReview:
                    questionState === "submitted" ||
                    questionState === "giving_up",
                }}
                type="solution"
              />
            )}
            {/* Explanation Area - 基于 questionState 显示 */}
            {showExplanations && (
              <div className="explanation-area mt-6 pb-4">
                <QuestionExplanation
                  preview
                  aiExplanation={stableQuestion.aiExplanation}
                  aiExplanationRecord={stableQuestion.aiExplanationRecord}
                  questionId={stableQuestion.questionId}
                  explanation={
                    stableQuestion.questionExplanation || "暂无解析数据"
                  }
                  className="mt-3"
                />
              </div>
            )}

            {/* 🆕 滚动检测哨兵元素 */}
            <div className="h-0">{sentinels.bottom}</div>
          </div>
          {/* 底部操作区域 - 现在只处理UI渲染，业务逻辑由ViewModel处理 */}
          <div className="action-buttons-wrapper sticky bottom-0 z-10 flex items-center justify-end gap-x-5 pb-9 pr-8 pt-[0.38rem]">
            {actionButtons}
          </div>
          {/* 🆕 底部渐变遮罩层 - 只在有滚动且未滚动到底部时显示 */}
          <AnswerMask className="w-full h-full" bgColor="var(--study-background)" hasOverflow={hasOverflow} scrollState={scrollState} />
        </div>
      </div>
    </div>
  );
};
