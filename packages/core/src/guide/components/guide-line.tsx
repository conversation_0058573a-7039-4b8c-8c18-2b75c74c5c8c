import { useComputed, useSignal } from "@preact-signals/safe-react";
import BgBtoGoto from "@repo/core/assets/images/btn-goto.svg";
import IconGoto from "@repo/core/assets/images/ic-goto.svg";
import { GuideMode, LineTexture } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useCurrentFrame } from "remotion";
import { useGuideContext } from "../context/guide-context";
import { useSectionH3Context } from "../context/section-h3-context";
import {
  useCDNAssets,
  useH2TitleBg,
} from "../viewmodels/guide-theme-viewmodel";
import { LineTreeNode } from "../viewmodels/guide-tree-viewmodel";
import { H2, H3, H4, <PERSON><PERSON>, <PERSON>, Picture, Texture, UL } from "./guide-elements";

export const BtnJumpLine: FC = () => {
  const { refContainer, selectedLine, selectedLineId, onLineSelected } =
    useGuideContext();

  const ref = useRef<HTMLDivElement | null>(null);

  const handleClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation();
      if (!selectedLine.value) return;
      const { frame } = selectedLine.value;
      onLineSelected(frame);
      selectedLine.value = null;
    },
    [selectedLine, onLineSelected]
  );

  const pos = useComputed(() => {
    if (!refContainer.current || !selectedLine.value)
      return { left: 0, top: 0 };
    const { left, top } = selectedLine.value;
    const view = refContainer.current;
    return {
      left: left - 70,
      top: top + view.scrollTop - 70,
    };
  });

  useEffect(() => {
    if (!selectedLineId) return;
    const timer = setTimeout(() => {
      selectedLine.value = null;
    }, 2000);
    return () => clearTimeout(timer);
  }, [selectedLineId]);

  if (!selectedLineId) return null;

  return (
    <div
      className="z-100 absolute size-fit"
      style={{ left: pos.value.left, top: pos.value.top }}
    >
      <div
        ref={ref}
        className="relative h-[86px] w-[140px]"
        onClick={handleClick}
      >
        <BgBtoGoto className="h-full w-full" />
        <div className="z-1 absolute inset-0">
          <div className="flex h-full w-full flex-row items-center justify-center gap-1 pb-3">
            <IconGoto />
            <span className="text-sm font-bold text-gray-700">从这里学</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export const GuideLine: FC<{
  data: LineTreeNode;
  className?: string;
  root?: boolean;
  parentStyleText?: number;
}> = ({ data, className, root = false, parentStyleText }) => {
  const {
    client,
    selectedLineId,
    guideMode,
    refContainer,
    handleLineClick,
    onClickReference,
  } = useGuideContext();
  const {
    ref: refSectionH3,
    h3Line,
    mergedReferences,
    index: sectionH3Index,
  } = useSectionH3Context();
  const { id, line, index, children } = data;
  const rootId = h3Line?.lineId;
  const rootTitle = h3Line?.content.reduce((acc, cur) => acc + cur.content, "");
  const isStuClient = client === "stu";

  const { tag, content, pic, inFrame, outFrame, width, styleText, lineId } =
    line;
  const childrenClassName = useMemo(() => {
    if (styleText === 1) return cn("flex w-full flex-col mt-4");
    if (styleText === 2) return cn("flex w-full flex-col gap-5");
    if (styleText === 3)
      return cn("w-full grid gap-5 px-6 py-7 rounded-3xl bg-[#807a751a]", {
        "grid-cols-3": children.length > 2,
        "grid-cols-2": children.length <= 2,
      });
    return cn("flex w-full flex-col gap-2");
  }, [children.length, styleText]);

  const frame = useCurrentFrame();
  const refLine = useRef<HTMLDivElement>(null);

  const handleLineSelected = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation();
      if (tag === "h2") return;
      const left = e.clientX;
      const top = refLine.current?.getBoundingClientRect().top ?? 0;
      handleLineClick(left, top, id, inFrame);
    },
    [tag, id, inFrame, handleLineClick, refLine]
  );

  // 判断是否在播放范围内, 用于显示当前句
  const isTalking = useMemo(() => {
    if (frame === 0) return false;
    return frame >= inFrame && frame < outFrame;
  }, [frame, inFrame, outFrame]);

  const isSelected = useMemo(() => {
    return selectedLineId === id;
  }, [selectedLineId, id]);

  const isAllUnderlined = useSignal(false);

  useEffect(() => {
    isAllUnderlined.value = true;
    const doms = Array.from(
      refLine.current?.querySelectorAll<HTMLSpanElement>(
        "span[data-line-id]"
      ) || []
    );
    isAllUnderlined.value = !doms.some((dom) => !dom.dataset.underline);
  }, [isAllUnderlined, mergedReferences]);

  const compContent = useMemo(() => {
    if (content === undefined || content.length === 0) return null;
    if (tag === "block") return null;

    const result = content.map((it, idx) => {
      return (
        <Texture
          lineId={lineId}
          id={String(idx)}
          rootId={rootId}
          rootTitle={rootTitle}
          key={`${lineId}:${idx}`}
          data={it as LineTexture}
          references={lineId ? mergedReferences[lineId]?.[idx] : undefined}
          onClick={onClickReference}
          isAllUnderlined={isAllUnderlined.value}
        />
      );
    });
    return result;
  }, [
    content,
    tag,
    lineId,
    rootId,
    rootTitle,
    mergedReferences,
    onClickReference,
    isAllUnderlined.value,
  ]);

  const compWrapper = useMemo(() => {
    switch (tag) {
      case "h2":
        return <H2 content={compContent} />;
      case "h3":
        return <H3 content={compContent} />;
      case "h4":
        return <H4 content={compContent} />;
      case "ul":
        return <UL content={compContent} userSelectable={isStuClient} />;
      case "ol":
        return (
          <OL
            index={index + 1}
            content={compContent}
            userSelectable={isStuClient}
            styleText={styleText || parentStyleText}
            isSelected={isSelected}
          />
        );
      case "block":
        return <p></p>;
      default:
        return <P content={compContent} userSelectable={isStuClient} />;
    }
  }, [
    compContent,
    tag,
    index,
    styleText,
    parentStyleText,
    isSelected,
    isStuClient,
  ]);

  // 滚动到当前元素, 只有h3时需要滚动, 其他元素不滚动
  useEffect(() => {
    if (!refContainer.current) return;
    if (!refSectionH3.current) return;
    if (!refLine.current) return;
    if (!isTalking) return;
    if (guideMode !== GuideMode.follow) return;

    const viewHeight = refContainer.current.clientHeight;
    const scrollTop = refContainer.current.scrollTop;

    const retLine = refLine.current.getBoundingClientRect();
    const retSection = refSectionH3.current.getBoundingClientRect();
    const retContainer = refContainer.current.getBoundingClientRect();
    // 滚动条
    const marginTop = 36;
    const posLineTop = retLine.top - retSection.top;

    const halfViewHeight = Math.round(viewHeight * 0.5);
    if (posLineTop > halfViewHeight) {
      // 检查refLine到底部空间是否不足

      // 可用高度
      const availableHeight =
        refContainer.current.scrollHeight -
        refContainer.current.scrollTop -
        retLine.top;
      if (availableHeight <= viewHeight * 0.5) {
        refContainer.current.scrollTop = scrollTop + retLine.top;
      } else {
        refLine.current.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    } else {
      // 否则固定H3顶部, retContainer.top 用于修正在预览场景中不是全屏的情况
      refContainer.current.scrollTop =
        sectionH3Index === 0
          ? 0
          : scrollTop + retSection.top - retContainer.top - marginTop;
    }
  }, [
    isTalking,
    refLine,
    guideMode,
    refContainer,
    refSectionH3,
    sectionH3Index,
  ]);

  if (tag === "block") {
    const { layout, imageRatio } = line;
    return (
      <div
        data-name={`wrapper:${tag}`}
        data-id={id}
        className={cn(
          "flex w-full",
          layout === "vertical" ? "flex-col gap-4" : "h-full flex-row gap-6",
          className
        )}
      >
        {layout === "vertical" ? (
          // 上下布局：文字在上，图片在下
          <>
            <div className="w-full">
              {children.length > 0 && (
                <div
                  data-name={`children:${tag}`}
                  className={
                    styleText
                      ? childrenClassName
                      : cn("flex w-full flex-col", !root && "gap-2")
                  }
                >
                  {children.map((it) => (
                    <GuideLine
                      key={it.id}
                      data={it}
                      parentStyleText={styleText}
                    />
                  ))}
                </div>
              )}
            </div>

            {pic && (
              <div className="mt-4 w-full">
                <div
                  className="relative w-full"
                  style={{
                    aspectRatio:
                      imageRatio === "16:9"
                        ? "16/9"
                        : imageRatio === "1:1"
                          ? "1/1"
                          : "9/16",
                  }}
                >
                  <Picture
                    lineId={lineId}
                    rootId={rootId}
                    rootTitle={rootTitle}
                    userSelectable={isStuClient}
                    data={pic}
                    mergedReference={
                      lineId
                        ? mergedReferences[lineId]?.["pic"]?.[0]
                        : undefined
                    }
                    onClick={onClickReference}
                    layout={layout}
                    imageRatio={imageRatio}
                    styleType={line.styleType}
                  />
                </div>
              </div>
            )}
          </>
        ) : (
          // 左右布局：文字在左，图片在右
          <>
            <div style={{ width }}>
              {children && children.length > 0 && (
                <div
                  data-name={`children:${tag}`}
                  className={
                    styleText
                      ? childrenClassName
                      : cn("flex h-min w-full flex-col", !root && "gap-2")
                  }
                >
                  {children.map((it) => (
                    <GuideLine
                      key={it.id}
                      data={it}
                      parentStyleText={styleText}
                    />
                  ))}
                </div>
              )}
            </div>
            {pic && (
              <div
                className={cn(
                  "flex flex-1 flex-col items-center justify-start"
                )}
              >
                <div
                  className={cn(
                    "w-full",
                    imageRatio === "9:16"
                      ? pic?.fileType === "html" || pic?.fileType === "js"
                        ? "aspect-[9/16] min-h-[400px]" // HTML/JS文件使用固定宽高比和最小高度
                        : "h-fit max-h-full" // 普通图片保持原有逻辑
                      : imageRatio === "1:1"
                        ? pic?.fileType === "html" || pic?.fileType === "js"
                          ? line.styleType === "style2"
                            ? "aspect-square min-h-[300px]" // 样式2: 75%文字，25%图片，图片相对较小
                            : line.styleType === "style3"
                              ? "aspect-square min-h-[350px]" // 样式3: 65%文字，35%图片，图片相对较大
                              : "aspect-square min-h-[300px]" // 默认
                          : "aspect-square"
                        : "aspect-[16/9]"
                  )}
                  style={{
                    aspectRatio: undefined, // 使用className控制宽高比
                  }}
                >
                  <Picture
                    lineId={lineId}
                    rootId={rootId}
                    rootTitle={rootTitle}
                    userSelectable={isStuClient}
                    data={pic}
                    mergedReference={
                      lineId
                        ? mergedReferences[lineId]?.["pic"]?.[0]
                        : undefined
                    }
                    onClick={onClickReference}
                    layout={layout}
                    imageRatio={imageRatio}
                    styleType={line.styleType}
                  />
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  }

  return (
    <div
      data-name={`wrapper:${tag}`}
      className={cn(
        "flex w-full flex-col",
        tag === "h3" && "gap-6",
        tag === "h4" && "gap-4"
      )}
    >
      <div
        data-name="line-container"
        className="font-resource-han-rounded relative w-full"
      >
        <div
          ref={refLine}
          className={cn(
            "z-100 relative",
            styleText === 2 || parentStyleText === 2 ? "w-full" : "w-fit",
            tag !== "ol" &&
              isSelected &&
              "before:bg-text-4/20 before:absolute before:-inset-1 before:rounded-lg"
          )}
          onClick={handleLineSelected}
        >
          {compWrapper}
        </div>

        {/* {isTalking && (
          <PlayingLine
            className={tag === "h3" ? "-left-[28px] w-[calc(100%+28px)]" : ""}
          />
        )} */}
      </div>

      {pic && (
        <Picture
          rootId={rootId}
          rootTitle={rootTitle}
          data={pic}
          lineId={lineId}
          userSelectable={isStuClient}
          mergedReference={
            lineId !== undefined
              ? mergedReferences[lineId]?.["pic"]?.[0]
              : undefined
          }
          onClick={onClickReference}
          styleType={line.styleType}
        />
      )}

      {children.length > 0 && (
        <div
          data-name={`children:${tag}`}
          data-id="xx"
          className={cn(childrenClassName, tag === "h4" && "gap-2")}
        >
          {children.map((it) => (
            <GuideLine key={it.id} data={it} parentStyleText={styleText} />
          ))}
        </div>
      )}
    </div>
  );
};

export const GuideTitle: FC<{
  title?: string;
}> = ({ title = "" }) => {
  const { theme } = useGuideContext();
  const { bgLeft, bgRepeat, bgRight } = useH2TitleBg(theme.titleBg);
  const refTitle = useRef<HTMLDivElement>(null);
  const defaultFontSize = 28; // 默认字号
  const letterSpacing = 1.08; // 字间距
  const [fontSize, setFontSize] = useState(defaultFontSize);

  useEffect(() => {
    if (!refTitle.current) return;
    const width = refTitle.current.clientWidth;
    const charWidth = defaultFontSize * letterSpacing;
    if (title.length * charWidth > width) {
      const fontSize = Math.floor(width / title.length / letterSpacing);
      setFontSize(fontSize);
    }
  }, [title, refTitle]);

  if (title === "" || title === undefined) return null;

  return (
    <h2
      data-name="section::title"
      className="relative flex h-[126px] w-max max-w-[var(--width-guide)] translate-y-14 flex-row items-center"
    >
      <div
        ref={refTitle}
        className="tracking-guide ml-25 z-20 mr-14 w-full font-bold leading-normal text-white"
        style={{ fontSize }}
      >
        {title}
      </div>
      <ol className="absolute left-0 top-0 z-0 flex h-full w-full flex-row">
        <li
          className="w-[156px]"
          style={{
            backgroundImage: `url(${bgLeft})`,
            backgroundPosition: "left 0",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
          }}
        ></li>
        <li
          className="flex-1"
          style={{
            backgroundImage: `url(${bgRepeat})`,
            backgroundPosition: "left",
            backgroundSize: "3px 100%",
            backgroundRepeat: "repeat-x",
          }}
        ></li>
        <li
          className="w-[100px]"
          style={{
            backgroundImage: `url(${bgRight})`,
            backgroundPosition: "left",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
          }}
        ></li>
      </ol>
    </h2>
  );
};

/**
 * 用于封面和封底的通栏长图
 */
export const Cover: FC<{
  name: string;
  className?: string;
}> = ({ name, className }) => {
  const path = useCDNAssets(name);
  if (!path) {
    return null;
  }
  return (
    <Image
      src={path}
      alt="last-plate"
      priority
      className={cn("h-auto", className)}
      width={1000}
      height={261}
    />
  );
};
