import { Signal } from "@preact-signals/safe-react";
import { PlayerRef } from "@remotion/player";
import { MergedReference, Reference } from "@repo/core/types/data/comment";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { RefObject } from "react";

/**
 * Guide 组件的 Props 类型
 */
export interface GuideComponentProps {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData;
  theme?: GuideTheme;
  showSubtitle?: boolean;
  refContainer?: RefObject<HTMLDivElement | null>;
  onLineClick?: (frame: number) => void;
  selectable?: boolean;
  onScrollFlip?: (index: number) => void;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
}

/**
 * VolcenginePlayer 接口
 */
export interface VolcenginePlayer {
  play: () => void;
  pause: () => void;
  seek: (seconds: number) => void;
  playbackRate: number;
  currentTime: number;
  duration: number;
  addEventListener: (event: string, callback: (e: unknown) => void) => void;
  removeEventListener: (event: string, callback: (e: unknown) => void) => void;
  destroy: () => void;
}

/**
 * VolcengineVideo 配置
 */
export interface VolcengineVideoConfig {
  src: string;
  userId?: string;
  tag?: string;
}

/**
 * 播放器控制回调函数
 */
export interface GuidePlayerCallbacks {
  /** 时间更新回调 */
  onTimeUpdate?: (frame: number) => void;
  /** 播放结束回调 */
  onEnded?: () => void;
  /** 开始播放回调 */
  onPlay?: () => void;
  /** 暂停播放回调 */
  onPause?: () => void;
  /** 跳转回调 */
  onSeek?: (frame: number) => void;
  /** 事件追踪回调 */
  trackEvent?: (event: string) => void;
  /** 显示提示消息回调 */
  showToast?: (message: string) => void;
}

/**
 * 手势控制配置
 */
export interface GestureConfig {
  /** 是否启用手势控制 */
  enableGestures?: boolean;
  /** 是否启用长按加速 */
  enableLongPress?: boolean;
  /** 是否启用双击播放/暂停 */
  enableDoubleClick?: boolean;
  /** 是否启用单击显示控件 */
  enableSingleClick?: boolean;
}

/**
 * 播放器状态
 */
export interface PlayerState {
  isPlaying: boolean;
  canPlay: boolean;
  playRate: number;
  showPlayerControls: boolean;
  currentFrame: number;
}

/**
 * 播放器控制方法
 */
export interface PlayerControls {
  togglePlay: () => void;
  seekTo: (frame: number) => void;
  forwardTo: (seconds: number) => void;
  setPlayRate: (rate: number) => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  togglePlayerControls: () => void;
  hidePlayerControls: () => void;
}

/**
 * 播放器引用
 */
export interface PlayerRefs {
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
}

/**
 * GuidePlayerView 组件 Props
 */
export interface GuidePlayerViewProps<T extends Record<string, unknown>> {
  // 核心播放参数
  /** Remotion 播放器组件 */
  component: React.ComponentType<T>;
  /** 传给 component 的 props */
  inputProps: T;
  /** 总帧数 */
  durationInFrames: number;
  /** 帧率 */
  fps: number;
  /** 初始帧位置 */
  initialFrame?: number;
  /** 初始播放速率 */
  initialPlayRate?: number;

  // 显示配置
  /** 自定义样式类名 */
  className?: string;
  /** 组合宽度 */
  compositionWidth: number;
  /** 组合高度 */
  compositionHeight: number;
  /** 播放器样式 */
  style?: React.CSSProperties;

  // VolcengineVideo 配置（可选）
  volcengineVideo?: VolcengineVideoConfig;
  /** 是否显示 VolcengineVideo */
  showVolcengineVideo?: boolean;

  // 回调函数
  callbacks?: GuidePlayerCallbacks;

  // 手势控制配置
  gestureConfig?: GestureConfig;

  // 其他配置
  /** 是否允许全屏 */
  allowFullscreen?: boolean;
  /** 错误回调组件 */
  errorFallback?: (error: { error: { message: string } }) => React.ReactNode;
}

/**
 * useGuidePlayerControl Hook 参数
 */
export interface UseGuidePlayerControlOptions {
  durationInFrames: number;
  fps: number;
  initialFrame?: number;
  initialPlayRate?: number;
  callbacks?: GuidePlayerCallbacks;
  volcengineVideo?: VolcengineVideoConfig;
}
