"use client";
import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { GuidePlayerViewProps } from "./types";
import { useGuidePlayerControl } from "./use-guide-player-control";

const VolcengineVideo = dynamic(
  () => import("../../volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

export function GuidePlayerView<T extends Record<string, unknown>>(
  props: GuidePlayerViewProps<T>
) {
  const {
    component,
    inputProps,
    durationInFrames,
    fps,
    initialFrame = 0,
    initialPlayRate = 1,
    className,
    compositionWidth,
    compositionHeight,
    style,
    volcengineVideo,
    showVolcengineVideo = true,
    callbacks,
    gestureConfig = {
      enableGestures: true,
      enableLongPress: true,
      enableDoubleClick: true,
      enableSingleClick: true,
    },
    allowFullscreen = false,
    errorFallback,
  } = props;
  const { playerState, playerControls, playerRefs } = useGuidePlayerControl({
    durationInFrames,
    fps,
    initialFrame,
    initialPlayRate,
    callbacks,
    volcengineVideo,
  });

  const { refPlayer, refVolcenginePlayer } = playerRefs;
  const { playRate } = playerState;
  const { togglePlay, togglePlayerControls, set3XPlayRate, resetPlayRate } =
    playerControls;

  const isComment = useSignal(false);

  // 长按手势处理
  const longPressHandlers = useLongPress(
    (e) => {
      if (!gestureConfig.enableLongPress) return;

      // 检查是否点击在评论区域
      const doms = document.querySelectorAll("[data-name=line-container]");
      if (
        Array.from(doms).some((dom) => dom.contains(e.target as HTMLElement))
      ) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
      callbacks?.trackEvent?.("doc_fast_forward_longpress");
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
      },
      detect: LongPressEventType.Touch,
    }
  );

  // 单击处理
  const handleClick = useCallback(() => {
    if (!gestureConfig.enableSingleClick) return;
    togglePlayerControls();
  }, [togglePlayerControls, gestureConfig.enableSingleClick]);

  // 双击处理
  const handleDoubleClick = useCallback(() => {
    if (!gestureConfig.enableDoubleClick) return;
    togglePlay();
    callbacks?.trackEvent?.("doc_play_pause_doubleclick");
  }, [togglePlay, callbacks, gestureConfig.enableDoubleClick]);

  // 双击手势处理
  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  // 默认错误回调
  const defaultErrorFallback = useCallback(
    (e: { error: { message: string } }) => (
      <span className="text-sm text-red-500">错误: {e.error.message}</span>
    ),
    []
  );

  if (!component) {
    return <div>无播放组件</div>;
  }

  return (
    <div
      {...(gestureConfig.enableGestures ? doubleTapHandlers : {})}
      data-name="guide-player"
      className="relative h-screen w-full"
      {...(gestureConfig.enableLongPress ? longPressHandlers() : {})}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={style}
        component={component}
        inputProps={inputProps}
        initialFrame={initialFrame}
        durationInFrames={durationInFrames}
        fps={fps}
        playbackRate={playRate}
        allowFullscreen={allowFullscreen}
        compositionWidth={compositionWidth}
        compositionHeight={compositionHeight}
        acknowledgeRemotionLicense
        errorFallback={errorFallback || defaultErrorFallback}
      />
      {showVolcengineVideo && volcengineVideo && (
        <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
          <VolcengineVideo
            ref={refVolcenginePlayer}
            className="relative flex h-full w-full flex-col items-center justify-end"
            src={volcengineVideo.src}
            startTime={initialFrame / fps}
            playRate={playRate}
            userId={volcengineVideo.userId}
            tag={volcengineVideo.tag || "播放器组件"}
          />
        </div>
      )}
    </div>
  );
}
