# GuidePlayerView 可复用播放器组件

这是一个从学生端应用中提取出来的可复用播放器组件，支持 Remotion 播放器和 VolcengineVideo 的集成播放控制。

## 特性

- 🎮 完整的播放控制逻辑（播放/暂停、跳转、倍速播放等）
- 🎯 手势控制支持（长按加速、双击播放/暂停、单击显示控件）
- 📱 VolcengineVideo 集成支持
- 🔧 高度可配置的接口
- 🎨 支持自定义组件和输入参数
- 📊 事件追踪和回调支持

## 使用方法

### 基本用法

```tsx
import {
  GuidePlayerView,
  type GuideComponentProps,
  type GuidePlayerCallbacks
} from "@repo/core/components/player/guide-player-view";
import { Guide } from "@repo/core/guide/guide";

function MyPlayer() {
  // 使用具体的类型定义
  const inputProps: GuideComponentProps = {
    client: "stu",
    data: guideData,
    theme: theme,
    guideMode: GuideMode.follow,
    showSubtitle: true,
    // ... 其他 props
  };

  const callbacks: GuidePlayerCallbacks = {
    trackEvent: (event: string) => console.log("Event:", event),
    showToast: (message: string) => alert(message),
    onTimeUpdate: (frame: number) => saveProgress(frame),
  };

  return (
    <GuidePlayerView
      component={Guide as React.ComponentType<Record<string, unknown>>}
      inputProps={inputProps}
      durationInFrames={3600}
      fps={24}
      compositionWidth={1920}
      compositionHeight={1080}
      volcengineVideo={{
        src: "https://example.com/video.mp4",
        userId: "user123",
        tag: "播放器组件",
      }}
      callbacks={callbacks}
    />
  );
}
```

### 高级配置

```tsx
<GuidePlayerView
  component={MyCustomComponent}
  inputProps={customProps}
  durationInFrames={7200}
  fps={30}
  initialFrame={100}
  initialPlayRate={1.5}
  compositionWidth={1280}
  compositionHeight={720}
  className="my-custom-player"
  style={{ border: "1px solid #ccc" }}
  
  // VolcengineVideo 配置
  volcengineVideo={{
    src: videoUrl,
    userId: userId,
    tag: "自定义播放器",
  }}
  showVolcengineVideo={true}
  
  // 回调函数
  callbacks={{
    onTimeUpdate: (frame) => saveProgress(frame),
    onEnded: () => handleVideoEnd(),
    onPlay: () => trackPlay(),
    onPause: () => trackPause(),
    onSeek: (frame) => trackSeek(frame),
    trackEvent: (event) => analytics.track(event),
    showToast: (message) => notification.show(message),
  }}
  
  // 手势控制配置
  gestureConfig={{
    enableGestures: true,
    enableLongPress: true,
    enableDoubleClick: true,
    enableSingleClick: true,
  }}
  
  allowFullscreen={false}
  errorFallback={(error) => <div>播放错误: {error.error.message}</div>}
/>
```

## API 参考

### GuidePlayerViewProps

| 属性 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `component` | `React.ComponentType<any>` | ✅ | - | Remotion 播放器组件 |
| `inputProps` | `any` | ✅ | - | 传给 component 的 props |
| `durationInFrames` | `number` | ✅ | - | 总帧数 |
| `fps` | `number` | ✅ | - | 帧率 |
| `compositionWidth` | `number` | ✅ | - | 组合宽度 |
| `compositionHeight` | `number` | ✅ | - | 组合高度 |
| `initialFrame` | `number` | ❌ | `0` | 初始帧位置 |
| `initialPlayRate` | `number` | ❌ | `1` | 初始播放速率 |
| `className` | `string` | ❌ | - | 自定义样式类名 |
| `style` | `React.CSSProperties` | ❌ | - | 播放器样式 |
| `volcengineVideo` | `VolcengineVideoConfig` | ❌ | - | VolcengineVideo 配置 |
| `showVolcengineVideo` | `boolean` | ❌ | `true` | 是否显示 VolcengineVideo |
| `callbacks` | `GuidePlayerCallbacks` | ❌ | - | 回调函数 |
| `gestureConfig` | `GestureConfig` | ❌ | 全部启用 | 手势控制配置 |
| `allowFullscreen` | `boolean` | ❌ | `false` | 是否允许全屏 |
| `errorFallback` | `Function` | ❌ | - | 错误回调组件 |

### 回调函数类型

```typescript
interface GuidePlayerCallbacks {
  onTimeUpdate?: (frame: number) => void;
  onEnded?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onSeek?: (frame: number) => void;
  trackEvent?: (event: string) => void;
  showToast?: (message: string) => void;
}
```

## 从原有代码迁移

如果你之前使用的是学生端的 GuidePlayerView，迁移步骤如下：

1. 更新导入路径：
```tsx
// 之前
import { GuidePlayerView } from "./guide-player-view";

// 现在
import { GuidePlayerView } from "@repo/core/components/player/guide-player-view";
```

2. 调整 props 结构：
```tsx
// 之前的用法会自动从 context 获取数据
<GuidePlayerView className="my-player" theme={theme} />

// 现在需要显式传入所有必需的参数
<GuidePlayerView
  component={Guide}
  inputProps={{
    client: "stu",
    guideMode: guideMode.value,
    data: data,
    theme: theme,
    // ... 其他原本在 inputProps 中的参数
  }}
  durationInFrames={durationInFrames}
  fps={avatar.fps}
  // ... 其他必需参数
/>
```

## 类型安全改进

本组件已经进行了类型优化，减少了 `any` 的使用：

### 主要类型定义

- `GuideComponentProps`: Guide 组件的具体 Props 类型
- `VolcenginePlayer`: VolcengineVideo 播放器的接口定义
- `GuidePlayerCallbacks`: 回调函数的类型定义
- `PlayerState`, `PlayerControls`, `PlayerRefs`: 播放器状态和控制的类型

### 类型使用建议

```tsx
// 推荐：使用具体的类型定义
import type {
  GuideComponentProps,
  GuidePlayerCallbacks,
  VolcenginePlayer
} from "@repo/core/components/player/guide-player-view";

// 为 inputProps 提供类型安全
const inputProps: GuideComponentProps = {
  data: guideData, // 必需
  client: "stu",   // 可选
  // TypeScript 会提供完整的类型提示
};

// 为回调函数提供类型安全
const callbacks: GuidePlayerCallbacks = {
  trackEvent: (event: string) => analytics.track(event),
  showToast: (message: string) => toast.show(message),
  onTimeUpdate: (frame: number) => saveProgress(frame),
};
```

## 注意事项

- 确保 `use-double-tap` 和 `use-long-press` 依赖已安装
- VolcengineVideo 使用 `Signal<VolcenginePlayer | null>` 类型引用
- 手势控制依赖于 DOM 查询，确保相关元素存在
- 播放控制逻辑已从原有 context 中提取，保持了完整的功能
- 由于 Remotion Player 的类型限制，component 需要类型断言为 `React.ComponentType<Record<string, unknown>>`
