import VePlayer from "@volcengine/veplayer";
import { useEffect } from "react";
import { getLicense } from "./license/license";

const VolcengineLicense = () => {
  const isSSR = typeof window === "undefined";
  useEffect(() => {
    if (isSSR) return;
    const license = getLicense();
    const { url } = license;
    VePlayer.setLicenseConfig({
      license: url,
    });
  });

  return <></>;
};

export default VolcengineLicense;
