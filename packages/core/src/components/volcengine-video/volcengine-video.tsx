"use client";
import { Signal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import VePlayer from "@volcengine/veplayer";
import { FC, useEffect, useRef } from "react";
import { getLicense } from "./license/license";
import "./volcengine-video.css";

export interface VolcenginePlayer {
  play: () => void;
  pause: () => void;
  seek: (seconds: number) => void;
  playbackRate: number;
  currentTime: number;
  duration: number;
  addEventListener: (event: string, callback: (e: unknown) => void) => void;
  removeEventListener: (event: string, callback: (e: unknown) => void) => void;
  destroy: () => void;
}
/**
 * volcengine-video 在build时报错，需要动态导入: 
 *
 * ```typescript
 * import dynamic from "next/dynamic";
 * const VolcengineVideo = dynamic(
 *   () => import("@repo/core/components/volcengine-video/volcengine-video"),
 *   {
 *     ssr: false,
 *   }
 * );
 * 注意: 需在页面顶层位置尽早的注册SDK, 引用VolcengineLicense
 * const VolcengineLicense = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-license"),
  {
    ssr: false,
  }
);
 * ```
 */
export const VolcengineVideo: FC<{
  ref: Signal<VolcenginePlayer | null>;
  src: string | undefined;
  className?: string;
  startTime?: number;
  playRate?: number;
  userId?: number | string;
  tag?: string;
}> = ({
  ref,
  src,
  className,
  startTime,
  playRate = 1,
  userId,
  tag = "未定义",
}) => {
  const refDiv = useRef<HTMLDivElement>(null);
  const isSSR =
    typeof window === "undefined" || typeof localStorage === "undefined";
  const refPlayerSDK = useRef<VePlayer | null>(null);
  const license = getLicense();

  useEffect(() => {
    if (isSSR) return;
    if (!refDiv.current) return;
    if (refPlayerSDK.current) return;

    const playerSDK = new VePlayer({
      root: refDiv.current,
      disableLocalCache: true,
      url: src,
      startTime,
      fitVideoSize: "fixHeight",
      videoFillMode: "cover",
      closeVideoClick: true,
      closeVideoDblclick: true,
      vodLogOpts: {
        line_app_id: license.appId,
        line_user_id: userId,
        tag,
      },
    });
    playerSDK.on("ready", () => {
      // console.log("====ready");
      const player = Object.assign(playerSDK.player, {
        addEventListener: (event: string, callback: (e: unknown) => void) => {
          playerSDK.on(event, callback);
        },
        removeEventListener: (
          event: string,
          callback: (e: unknown) => void
        ) => {
          playerSDK.off(event, callback);
        },
      });
      ref.value = player as VolcenginePlayer;
    });

    refPlayerSDK.current = playerSDK;

    return () => {
      // console.log("====destroy");
      ref.value?.destroy();
      ref.value = null;
    };
  }, [isSSR, src, ref, refPlayerSDK, license, startTime, userId]);

  useEffect(() => {
    if (ref.value === null) return;
    ref.value.playbackRate = playRate;
  }, [playRate, ref.value]);

  if (src === undefined || src === "") return null;

  return <div ref={refDiv} className={cn("h-full", className)}></div>;
};

export default VolcengineVideo;
