import {
  AppInfo,
  DeviceInfo,
  NetworkHeaderParams,
  StudentUserInfo,
} from "@repo/core/types/client/jsbridge";
import bridge from "@repo/lib/utils/jsbridge";

export function getDeviceInfo() {
  try {
    const data = bridge.invokeSync<DeviceInfo>("getDeviceInfo");
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getAppInfo() {
  try {
    const data = bridge.invokeSync<AppInfo>("getAppInfo");
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getStudentUserInfo() {
  try {
    const data = bridge.invokeSync<StudentUserInfo>("getStudentUserInfo");
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getStatusBarHeight() {
  console.log("getStatusBarHeight");
  try {
    const deviceInfo = bridge.invokeSync<DeviceInfo>("getDeviceInfo");
    const statusBarHeight =
      deviceInfo?.statusBarHeight && deviceInfo.deviceScreenDensity
        ? deviceInfo.statusBarHeight / deviceInfo.deviceScreenDensity
        : 0;
    return statusBarHeight;
  } catch (error) {
    console.error(error);
    return 0;
  }
}

type SetStatusBarParams =
  | {
      eventType: "setStatusBarVisibility";
      isVisible: boolean;
    }
  | {
      eventType: "setStatusBarColor";
      color: string;
    }
  | {
      eventType: "setStatusBarTextColor";
      isDark: boolean;
    };

export function setStatusBar(params: SetStatusBarParams) {
  try {
    bridge.invokeSync("controlStatusBar", params);
  } catch (error) {
    console.error(error);
  }
}

export function getScreenSize() {
  try {
    const data = bridge.invokeSync<DeviceInfo>("getDeviceInfo");
    if (!data) {
      return null;
    }
    return {
      width: Math.floor(data.fullScreenWidth / data.deviceScreenDensity),
      height: Math.floor(data.fullScreenHeight / data.deviceScreenDensity),
    };
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function getNetworkHeaderParams() {
  try {
    const data = bridge.invokeSync<NetworkHeaderParams>(
      "getNetworkHeaderParams"
    );
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export function finishLesson() {
  try {
    console.log("finishLesson");
    bridge.invokeSync("lessonFinish");
  } catch (error) {
    console.error(error);
  }
}

export function exitLesson() {
  try {
    bridge.invokeSync("lessonClose");
  } catch (error) {
    console.error(error);
  }
}

export function closePage() {
  try {
    bridge.invokeSync("closePage");
  } catch (error) {
    console.error(error);
  }
}

/*
 * 埋点 trackEvent
 * 参数：
 * eventID: 事件ID
 * map: 事件参数
 * 返回值：
 * 无
 * 注意：map 中不支持复杂类型，如数组、对象等，请使用JSON.stringify()将复杂类型转换为字符串
 */
export function trackEvent(eventID: string, map: Record<string, unknown>) {
  const userInfo = getStudentUserInfo();
  const {
    userId: user_id,
    classId: class_id,
    schoolId: school_id,
  } = userInfo || {};

  try {
    // 遍历处理 map，将所有类型都转换为字符串
    const processedMap: Record<string, unknown> = {
      user_id,
      class_id,
      school_id,
    };
    Object.entries(map).forEach(([key, value]) => {
      if (typeof value === "object" || Array.isArray(value)) {
        processedMap[key] = JSON.stringify(value);
      } else {
        processedMap[key] = value;
      }
    });
    bridge.invokeSync("reportEvent", { eventID, map: processedMap });
  } catch (error) {
    console.error(error);
  }
}

// 处理安卓设备的返回动作
export function listenDeviceBackAction(
  callback: (result: {
    code: number;
    msg: string;
    data?: { event?: "backPressed" };
  }) => void
) {
  bridge.invokeAsync("addEventListener", {
    eventName: "backPressedIntercept",
    callback,
  });

  return () => {
    bridge.invokeAsync("removeEventListener", {
      eventName: "backPressedIntercept",
      callback,
    });
  };
}
