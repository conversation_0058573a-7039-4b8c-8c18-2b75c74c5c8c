"use client";

import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useMemo, useRef, useState } from "react";
import GuideText from "./guide-text";

interface SimpleDelayedContentProps {
  guideText?: string;
  items: React.ReactNode[];
  guideDelay?: number;
  staggerDelay?: number;
  className?: string;
  stageKey?: string;
  onAnimationComplete?: () => void; // 新增：动画完成回调
}

// 全局状态管理，记录已访问的阶段
const visitedStages = new Set<string>();

// 导出清除函数，用于重置访问状态
export const clearVisitedStages = () => {
  visitedStages.clear();
};

// 导出检查函数，用于判断阶段是否已访问
export const isStageVisited = (stageKey: string) => {
  return visitedStages.has(stageKey);
};

export const SimpleDelayedContent: React.FC<SimpleDelayedContentProps> = ({
  guideText,
  items,
  guideDelay = 500, // 引导语的初始延迟
  staggerDelay = 1000, // 每个阶段之间的间隔
  className,
  stageKey = "default",
  onAnimationComplete,
}) => {
  const [isGuideTextVisible, setIsGuideTextVisible] = useState(false);
  // ✨ 核心改动：不再存储整个React节点，而是存储它们的索引(number)
  const [visibleItemIndices, setVisibleItemIndices] = useState<Set<number>>(
    new Set()
  );

  const guideTextDelay = useMemo(() => {
    return guideText && guideText.trim() !== "" ? guideDelay : 0;
  }, [guideText, guideDelay]);
  const isFirstVisit = useRef(!visitedStages.has(stageKey));

  useEffect(() => {
    // 如果不是第一次访问（例如，答题后刷新），则立即显示所有内容，不播放动画
    if (!isFirstVisit.current) {
      setIsGuideTextVisible(true);
      // ✨ 直接将所有项的索引添加到Set中
      const allIndices = new Set(items.map((_, index) => index));
      setVisibleItemIndices(allIndices);
      // 非首次访问时，确保已标记为访问过
      if (stageKey) {
        visitedStages.add(stageKey);
      }
      // 立即触发动画完成回调
      onAnimationComplete?.();
      return;
    }

    // --- 下面的逻辑只在第一次访问时执行 ---
    const timers: NodeJS.Timeout[] = [];

    // 1. 设置引导语的计时器
    const guideTimer = setTimeout(() => {
      setIsGuideTextVisible(true);
    }, guideTextDelay);
    timers.push(guideTimer);

    // 2. 设置内容块的串联计时器
    items.forEach((_, index) => {
      const cumulativeDelay = guideTextDelay + (index + 1) * staggerDelay;

      const itemTimer = setTimeout(() => {
        // ✨ 将项的索引添加到Set中
        setVisibleItemIndices((prev) => {
          const newSet = new Set(prev).add(index);
          // 检查是否所有项都已显示
          if (newSet.size === items.length) {
            // 所有动画项都已完成，触发完成回调
            setTimeout(() => {
              // 在动画完成后才标记为已访问
              if (stageKey) {
                visitedStages.add(stageKey);
              }
              onAnimationComplete?.();
            }, 600); // 等待最后一个动画完成（transition duration: 0.6s）
          }
          return newSet;
        });
      }, cumulativeDelay);
      timers.push(itemTimer);
    });

    return () => {
      timers.forEach((timer) => clearTimeout(timer));
    };
    // 依赖项保持不变，但内部逻辑现在可以正确处理items的更新
  }, [
    items,
    guideTextDelay,
    staggerDelay,
    stageKey,
    onAnimationComplete,
    guideText,
  ]);

  return (
    <div className={cn("simple-delayed-content space-y-4", className)}>
      <AnimatePresence>
        {isGuideTextVisible && guideText != null && guideText.trim() !== "" && (
          <motion.div
            key="guide-text"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <GuideText text={guideText} />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {items.map((item, index) =>
          // ✨ 核心改动：通过检查索引是否存在来判断是否显示
          visibleItemIndices.has(index) ? (
            <motion.div
              key={index + stageKey}
              layout
              initial={{
                opacity: 0,
                boxShadow:
                  "0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -4px rgba(0, 0, 0, 0.05)",
                borderRadius: "0.5rem",
              }}
              animate={{
                opacity: 1,
                boxShadow: "none",
                borderRadius: "none",
              }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              {item}
            </motion.div>
          ) : null
        )}
      </AnimatePresence>
    </div>
  );
};

export default SimpleDelayedContent;
