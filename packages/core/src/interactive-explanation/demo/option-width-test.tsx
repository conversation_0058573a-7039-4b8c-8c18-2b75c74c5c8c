"use client";

import React from 'react';
import { ContentCard } from '../view/components/content-card';

/**
 * 选项宽度一致性测试页面
 */
export default function OptionWidthTest() {
  // 测试用例1：长短不一的选项
  const questionData1 = {
    questionStem: "下列哪个选项是正确的？",
    questionOptionList: [
      { optionKey: "A", optionVal: "短选项" },
      { optionKey: "B", optionVal: "这是一个比较长的选项内容，用来测试宽度一致性" },
      { optionKey: "C", optionVal: "中等长度的选项" },
      { optionKey: "D", optionVal: "另一个很长很长很长很长很长很长的选项内容，看看是否会影响其他选项的宽度" }
    ],
    correctAnswers: ["B"]
  };

  // 测试用例2：包含数学公式的选项
  const questionData2 = {
    questionStem: "计算下列表达式的值：",
    questionOptionList: [
      { optionKey: "A", optionVal: "\\( x = 1 \\)" },
      { optionKey: "B", optionVal: "\\( x = \\frac{1}{2} + \\sqrt{3} \\)" },
      { optionKey: "C", optionVal: "\\( x = 2 \\)" },
      { optionKey: "D", optionVal: "\\( x = \\frac{\\sqrt{2} + \\sqrt{3}}{\\sqrt{5}} \\)" }
    ],
    correctAnswers: ["C"]
  };

  // 测试用例3：极短和极长选项混合
  const questionData3 = {
    questionStem: "选择正确答案：",
    questionOptionList: [
      { optionKey: "A", optionVal: "是" },
      { optionKey: "B", optionVal: "不是" },
      { optionKey: "C", optionVal: "这是一个非常非常非常非常非常非常长的选项，用来测试在极端情况下的宽度控制效果" },
      { optionKey: "D", optionVal: "可能" }
    ],
    correctAnswers: ["A"]
  };

  return (
    <div className="option-width-test p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold mb-8 text-gray-900">
          选项宽度一致性测试
        </h1>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
          <h2 className="text-lg font-semibold text-yellow-800 mb-2">测试目标</h2>
          <p className="text-yellow-700">
            验证所有选项保持一致的宽度，但不占满整行。当前策略：
          </p>
          <ul className="text-yellow-700 mt-2 space-y-1">
            <li>• <code>w-max</code>：宽度基于内容</li>
            <li>• <code>min-w-[240px]</code>：最小宽度240px</li>
            <li>• <code>max-w-[70%]</code>：最大宽度不超过容器的70%</li>
          </ul>
        </div>

        {/* 测试用例1 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例1：长短不一的选项</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <ContentCard
              title="宽度一致性测试"
              mode="question"
              answerConfig={{
                questionData: questionData1,
                onCorrectAnswer: () => console.log('答对了！'),
                onWrongAnswer: (key) => console.log('答错了:', key),
                initialSelectedOption: '',
                initialIsCorrect: null,
                stepId: 'test1'
              }}
            />
          </div>
        </div>

        {/* 测试用例2 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例2：包含数学公式的选项</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <ContentCard
              title="数学公式测试"
              mode="question"
              answerConfig={{
                questionData: questionData2,
                onCorrectAnswer: () => console.log('答对了！'),
                onWrongAnswer: (key) => console.log('答错了:', key),
                initialSelectedOption: '',
                initialIsCorrect: null,
                stepId: 'test2'
              }}
            />
          </div>
        </div>

        {/* 测试用例3 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例3：极端长度对比</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <ContentCard
              title="极端情况测试"
              mode="question"
              answerConfig={{
                questionData: questionData3,
                onCorrectAnswer: () => console.log('答对了！'),
                onWrongAnswer: (key) => console.log('答错了:', key),
                initialSelectedOption: '',
                initialIsCorrect: null,
                stepId: 'test3'
              }}
            />
          </div>
        </div>

        {/* 期望效果说明 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-900">
            期望效果
          </h2>
          <div className="text-green-800 space-y-2">
            <p>✅ <strong>宽度一致：</strong> 所有选项应该有相同的宽度（基于最长选项）</p>
            <p>✅ <strong>不占满整行：</strong> 选项宽度不应该达到容器的100%</p>
            <p>✅ <strong>最小宽度保证：</strong> 即使很短的选项也有合理的最小宽度</p>
            <p>✅ <strong>最大宽度限制：</strong> 很长的选项不会过度拉伸</p>
            <p>✅ <strong>响应式适配：</strong> 在不同屏幕尺寸下都能正常显示</p>
          </div>
        </div>

        {/* 当前实现说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-900">
            当前实现
          </h2>
          <div className="text-blue-800 space-y-2">
            <p><strong>容器：</strong> <code>flex flex-col</code> - 垂直排列</p>
            <p><strong>选项：</strong> <code>w-max min-w-[240px] max-w-[70%]</code></p>
            <p><strong>优势：</strong> 简单、可靠、无需JavaScript计算</p>
            <p><strong>限制：</strong> 无法实现完全一致的宽度（基于最长选项）</p>
          </div>
        </div>
      </div>
    </div>
  );
}
