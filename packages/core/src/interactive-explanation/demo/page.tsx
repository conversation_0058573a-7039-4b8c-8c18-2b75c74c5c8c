"use client";

import React, { useState } from 'react';
import { Button } from '@repo/ui/components/press-button';
import InteractiveExplanation from '../view';
import { aiExplanation, aiExplanationRecord } from '../mock/ai-explanation';
import { AiExplanationRecord } from '@repo/core/types';

export default function InteractiveExplanationDemo() {
  const [isFloatingPanelVisible, setIsFloatingPanelVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<AiExplanationRecord>(aiExplanationRecord);

  const openFloatingPanel = () => {
    setIsFloatingPanelVisible(true);
  };

  const closeFloatingPanel = () => {
    setIsFloatingPanelVisible(false);
  };

  const handleProgressChange = (record: AiExplanationRecord) => {
    setCurrentRecord(record);
    console.log('进度变更:', record);
  };

  return (
    <div className="max-h-screen bg-[#F0EAE6] p-8 relative overflow-auto">
      <div className="max-w-4xl mx-auto ">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          互动讲题浮层组件 Demo
        </h1>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">功能测试</h2>
          <div className="space-y-4">
            <div>
              <Button
                color="orange"
                onClick={openFloatingPanel}
                className="demo-open-floating-panel-btn"
              >
                打开互动讲题浮层
              </Button>
            </div>



            <div>
              <h3 className="text-lg font-medium mb-2">当前记录状态</h3>
              <div className="bg-[#F8F9FA] rounded-lg p-4">
                <div className="demo-record-status space-y-2 text-sm">
                  <p><strong>当前阶段:</strong> {currentRecord.currentProgress}</p>
                  <p><strong>逐步讲解进度:</strong> {currentRecord.stepByStepGuideCurrentProgress}</p>
                  <p><strong>已答题步骤:</strong> {currentRecord.stepByStepGuide.filter(step => step.isAnswer).length} / {currentRecord.stepByStepGuide.length}</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* 互动讲题主组件 */}
      <InteractiveExplanation
        questionId="mock-question-id-12345"
        studySessionId={999}
        isVisible={isFloatingPanelVisible}
        onClose={closeFloatingPanel}
        initialData={aiExplanation}
        initialRecord={currentRecord}
        onProgressChange={handleProgressChange}
      />
    </div>
  );
}
