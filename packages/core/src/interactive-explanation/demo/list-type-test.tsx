"use client";

import React from 'react';
import { ContentCard } from '../view/components/content-card';

/**
 * 列表类型控制测试页面
 * 测试后端返回无序列表格式，前端控制渲染为有序/无序列表
 */
export default function ListTypeTest() {
  // 模拟后端返回的无序列表格式数据
  const backendData1 = `- 第一步：分析问题
- 第二步：制定方案  
- 第三步：实施解决`;

  const backendData2 = `* **解题思路：**
  - 理解题目要求
  - 找出关键信息
* **具体步骤：**
  - 建立数学模型
  - 求解方程
  - 验证结果`;

  const backendData3 = `* **识别目标：** 明确题目要求我们找到哪些向量和等于体对角线向量 \\( \\vec{AC_1} \\)。`;

  return (
    <div className="list-type-test p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold mb-8 text-gray-900">
          列表类型控制测试
        </h1>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
          <h2 className="text-lg font-semibold text-yellow-800 mb-2">测试说明</h2>
          <p className="text-yellow-700">
            后端只返回无序列表格式（- 或 * 开头），前端通过 listType 属性控制渲染样式
          </p>
        </div>

        {/* 测试用例1：简单列表 */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例1：简单步骤列表</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-blue-600">自动检测（默认）</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="auto"</code>
              </div>
              <ContentCard
                title="解题步骤"
                content={backendData1}
                mode="content"
                listType="auto"
              />
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-green-600">强制有序列表</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="ordered"</code>
              </div>
              <ContentCard
                title="解题步骤"
                content={backendData1}
                mode="content"
                listType="ordered"
              />
            </div>
          </div>
        </div>

        {/* 测试用例2：嵌套列表 */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例2：嵌套列表</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-blue-600">无序列表（默认）</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="unordered"</code>
              </div>
              <ContentCard
                title="解题方法"
                content={backendData2}
                mode="content"
                listType="unordered"
              />
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-green-600">强制有序列表</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="ordered"</code>
              </div>
              <ContentCard
                title="解题方法"
                content={backendData2}
                mode="content"
                listType="ordered"
              />
            </div>
          </div>
        </div>

        {/* 测试用例3：带加粗标题 */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">测试用例3：带加粗标题</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-blue-600">自动检测</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="auto"</code>
              </div>
              <ContentCard
                title="题目分析"
                content={backendData3}
                mode="content"
                listType="auto"
              />
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium mb-4 text-green-600">强制有序列表</h3>
              <div className="mb-4">
                <code className="text-sm bg-gray-100 p-2 rounded block">listType="ordered"</code>
              </div>
              <ContentCard
                title="题目分析"
                content={backendData3}
                mode="content"
                listType="ordered"
              />
            </div>
          </div>
        </div>

        {/* 原始数据展示 */}
        <div className="bg-gray-100 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">后端返回的原始数据</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">数据1：</h3>
              <pre className="bg-white p-3 rounded text-sm text-gray-600 whitespace-pre-wrap">{backendData1}</pre>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">数据2：</h3>
              <pre className="bg-white p-3 rounded text-sm text-gray-600 whitespace-pre-wrap">{backendData2}</pre>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">数据3：</h3>
              <pre className="bg-white p-3 rounded text-sm text-gray-600 whitespace-pre-wrap">{backendData3}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
