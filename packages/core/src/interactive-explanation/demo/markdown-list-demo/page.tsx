"use client";

import React from 'react';
import MarkdownListRenderer from '../../view/components/markdown-list-renderer';

/**
 * MarkdownListRenderer 测试组件
 * 用于验证各种Markdown格式的渲染效果
 */
export default function MarkdownListDemo() {
    // 测试用例1：带加粗标题的无序列表
    const testCase1 = `*   **识别目标：** 明确题目要求我们找到哪些向量和等于体对角线向量 \\( \\vec{AC_1} \\)。`;

    // 测试用例2：嵌套无序列表
    const testCase2 = `- **水果**
  - 苹果
  - 橘子`;

    // 测试用例3：复杂嵌套列表
    const testCase3 = `* **第一步：分析题目**
  * 理解题目要求
  * 找出关键信息
* **第二步：建立方程**
  * 设定未知数
  * 列出方程组`;

    // 测试用例4：有序列表
    const testCase4 = `- 第一步：分析问题
- 第二步：制定方案
- 第三步：实施解决`;

    // 测试用例5：普通文本
    const testCase5 = `这是一段普通的文本内容，没有任何列表格式。`;

    // 测试用例6：混合格式
    const testCase6 = `**解题思路：**

* **分析阶段**
  - 读题理解
  - 找出已知条件
* **求解阶段**
  1. 建立方程
  2. 求解方程
  3. 验证结果`;

    const testCases = [
        { title: '带加粗标题的无序列表', content: testCase1 },
        { title: '嵌套无序列表', content: testCase2 },
        { title: '复杂嵌套列表', content: testCase3 },
        { title: '有序列表', content: testCase4, },
        { title: '普通文本', content: testCase5 },
        { title: '混合格式', content: testCase6 },
    ];

    return (
        <div className="markdown-list-demo p-8 bg-gray-50 min-h-screen">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold mb-8 text-gray-900">
                    MarkdownListRenderer 测试
                </h1>

                <div className="space-y-8">
                    {testCases.map((testCase, index) => (
                        <div key={index} className="test-case bg-white rounded-lg shadow-sm p-6">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800">
                                测试用例 {index + 1}: {testCase.title}
                            </h2>

                            {/* 原始内容 */}
                            <div className="mb-4">
                                <h3 className="text-sm font-medium text-gray-600 mb-2">原始内容：</h3>
                                <pre className="bg-gray-100 p-3 rounded text-sm text-gray-700 whitespace-pre-wrap">
                                    {testCase.content}
                                </pre>
                            </div>

                            {/* 渲染结果 */}
                            <div>
                                <h3 className="text-sm font-medium text-gray-600 mb-2">渲染结果：</h3>
                                <div className="border border-gray-200 rounded p-4 bg-[#F7F6F5]">
                                    <MarkdownListRenderer content={testCase.content} />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* 使用说明 */}
                <div className="mt-12 bg-blue-50 rounded-lg p-6">
                    <h2 className="text-xl font-semibold mb-4 text-blue-900">
                        使用说明
                    </h2>
                    <div className="text-blue-800 space-y-2">
                        <p>• <strong>加粗标题：</strong> 使用 **标题** 格式，会被提取为独立的标题行</p>
                        <p>• <strong>无序列表：</strong> 使用 * 或 - 开头，支持嵌套</p>
                        <p>• <strong>有序列表：</strong> 使用 1. 2. 格式</p>
                        <p>• <strong>嵌套支持：</strong> 通过缩进控制层级关系</p>
                        <p>• <strong>自动检测：</strong> 如果不包含列表格式，自动使用普通文本渲染</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

