"use client";

import FinalAnswerIcon from "@repo/core/public/assets/interactive-explanation/final-answer.svg";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent from "../../components/simple-delayed-content";

interface SolutionSummaryData {
  guideText?: string;
  solutionSteps: string;
  finalAnswer: string;
}

interface SolutionSummaryStageProps {
  data: SolutionSummaryData;
  className?: string;
  onAnimationComplete?: () => void;
}

export const SolutionSummaryStage: React.FC<SolutionSummaryStageProps> = ({
  data,
  className,
  onAnimationComplete,
}) => {
  // ✨ 1. Define the UI elements as an array of items for animation.
  const items = [
    <ContentCard
      content={data.solutionSteps}
      listType="ordered"
      className="mb-8"
    />,
    <ContentCard
      title="最终答案"
      content={data.finalAnswer}
      icon={<FinalAnswerIcon />}
    />,
  ];

  // ✨ 2. Render the SimpleDelayedContent component with the prepared data.
  return (
    <SimpleDelayedContent
      guideText={data.guideText}
      items={items}
      stageKey="solution-summary"
      className={cn("solution-summary-stage mt-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default SolutionSummaryStage;
