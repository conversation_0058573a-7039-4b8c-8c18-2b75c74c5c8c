"use client";

import SolutionBreakthroughIcon from "@repo/core/public/assets/interactive-explanation/breakthrough.svg";
import ExamPointIcon from "@repo/core/public/assets/interactive-explanation/exam-point.svg";
import TargetIcon from "@repo/core/public/assets/interactive-explanation/target.svg";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import PText from "../../components/p-text";
import SimpleDelayedContent from "../../components/simple-delayed-content";

interface ExamPointAnalysisData {
  guideText?: string;
  target: string;
  examPoint: string;
  solutionBreakthrough: string;
}

interface ExamPointAnalysisStageProps {
  data: ExamPointAnalysisData;
  className?: string;
  onAnimationComplete?: () => void;
}

export const ExamPointAnalysisStage: React.FC<ExamPointAnalysisStageProps> = ({
  data,
  className,
  onAnimationComplete,
}) => {
  // 🔥 修复：将引导语从 items 中分离，确保与题目分析保持一致的加载时序
  const items = [
    <ContentCard
      listType="ordered"
      title="目标"
      content={data.target}
      icon={<TargetIcon />}
    />,

    <ContentCard
      listType="ordered"
      title="考点"
      content={data.examPoint}
      icon={<ExamPointIcon />}
    />,

    <ContentCard
      title="解题突破口"
      content={data.solutionBreakthrough}
      icon={<SolutionBreakthroughIcon />}
    />,
  ];

  // 🔥 新增：中间引导语组件，在第二个和第三个内容块之间显示
  const intermediateGuideItems = [
    <>
      <PText text="其次，我们需得知核心考点：" className="mb-4 mt-8" />
    </>,
    <>
      <PText
        text="目标和考点都清楚了，我们来找找这道题的解题突破口："
        className="mb-4 mt-8"
      />
    </>,
  ];

  // 🔥 修复：重新组织 items，确保引导语和内容的正确时序
  const organizedItems = [
    // 第一个内容块：目标
    items[0],

    // 第一个中间引导语 + 第二个内容块：考点
    <>
      {intermediateGuideItems[0]}
      {items[1]}
    </>,

    // 第二个中间引导语 + 第三个内容块：解题突破口
    <>
      {intermediateGuideItems[1]}
      {items[2]}
    </>,
  ];

  return (
    <SimpleDelayedContent
      guideText={data.guideText}
      items={organizedItems}
      stageKey="exam-point-analysis"
      className={cn("exam-point-analysis-stage mt-6 gap-y-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default ExamPointAnalysisStage;
