"use client";

import KeyInfoIcon from "@repo/core/public/assets/interactive-explanation/key-info.svg";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../../components/content-card";
import SimpleDelayedContent from "../../components/simple-delayed-content";

interface ProblemAnalysisData {
  guideText?: string;
  analysisItems: string;
  keyInfo: {
    title: string;
    content: string;
    icon?: React.ReactNode;
  };
}

interface ProblemAnalysisStageProps {
  data: ProblemAnalysisData;
  className?: string;
  onAnimationComplete?: () => void;
}

export const ProblemAnalysisStage: React.FC<ProblemAnalysisStageProps> = ({
  data,
  className,
  onAnimationComplete,
}) => {
  // ✨ 1. Define the UI elements as an array of items for animation.
  const items = [
    <ContentCard content={data.analysisItems} className="mb-6" />,
    <ContentCard
      title={data.keyInfo.title}
      content={data.keyInfo.content}
      icon={<KeyInfoIcon />}
    />,
  ];

  // ✨ 2. Render the SimpleDelayedContent component with the prepared data.
  return (
    <SimpleDelayedContent
      guideText={data.guideText}
      items={items}
      stageKey="problem-analysis"
      className={cn("problem-analysis-stage mt-6 gap-y-6", className)}
      onAnimationComplete={onAnimationComplete}
    />
  );
};

export default ProblemAnalysisStage;
