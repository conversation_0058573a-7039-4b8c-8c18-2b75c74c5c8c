"use client";

import Rectangle from "@repo/core/public/assets/interactive-explanation/Rectangle.svg";
import { cn } from "@repo/ui/lib/utils";
import React, { useCallback, useEffect, useRef, useState } from "react";

interface FloatingPanelProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  className?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  /** 内容区域的key，用于强制重新渲染 */
  contentKey?: string | number;
}

const MIN_HEIGHT = 100;
const MAX_HEIGHT_RATIO = 0.95; // 最大高度为屏幕的90%

const getDefaultHeight = () => {
  if (typeof window !== "undefined") {
    return Math.floor(window.innerHeight * MAX_HEIGHT_RATIO);
  }
  return MIN_HEIGHT;
};
const ReopenButton = ({
  onMouseDown,
  onTouchStart,
  isFloating = false,
}: {
  onMouseDown: (e: React.MouseEvent) => void;
  onTouchStart: (e: React.TouchEvent) => void;
  isFloating?: boolean; // 是否是浮动状态（跟随浮层移动）
}) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const [isDragStarted, setIsDragStarted] = useState(false);
  const dragStartPos = useRef<{ x: number; y: number } | null>(null);
  const DRAG_THRESHOLD = 5; // 拖拽阈值，超过这个距离才认为是拖拽

  useEffect(() => {
    const button = buttonRef.current;
    if (!button) return;

    // 鼠标事件处理
    const handleNativeMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // 记录起始位置
      dragStartPos.current = { x: e.clientX, y: e.clientY };
      setIsDragStarted(false);
    };

    const handleNativeMouseMove = (e: MouseEvent) => {
      if (!dragStartPos.current) return;

      const deltaX = Math.abs(e.clientX - dragStartPos.current.x);
      const deltaY = Math.abs(e.clientY - dragStartPos.current.y);

      // 检查是否超过拖拽阈值
      if (
        (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) &&
        !isDragStarted
      ) {
        setIsDragStarted(true);
        // 只有真正拖拽时才调用父组件的处理函数
        const mockEvent = {
          clientY: dragStartPos.current.y,
          preventDefault: () => {},
          stopPropagation: () => {},
        } as unknown as React.MouseEvent;
        onMouseDown(mockEvent);
      }
    };

    const handleNativeMouseUp = () => {
      dragStartPos.current = null;
      setIsDragStarted(false);
    };

    // 触摸事件处理
    const handleNativeTouchStart = (e: TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (e.touches.length > 0) {
        const touch = e.touches[0];
        if (touch) {
          // 记录起始位置
          dragStartPos.current = { x: touch.clientX, y: touch.clientY };
          setIsDragStarted(false);
        }
      }
    };

    const handleNativeTouchMove = (e: TouchEvent) => {
      if (!dragStartPos.current || e.touches.length === 0) return;

      const touch = e.touches[0];
      if (!touch) return;

      const deltaX = Math.abs(touch.clientX - dragStartPos.current.x);
      const deltaY = Math.abs(touch.clientY - dragStartPos.current.y);

      // 检查是否超过拖拽阈值
      if (
        (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) &&
        !isDragStarted
      ) {
        setIsDragStarted(true);
        // 只有真正拖拽时才调用父组件的处理函数
        const mockEvent = {
          touches: [{ clientY: dragStartPos.current.y }],
          preventDefault: () => {},
          stopPropagation: () => {},
        } as unknown as React.TouchEvent;
        onTouchStart(mockEvent);
      }
    };

    const handleNativeTouchEnd = () => {
      dragStartPos.current = null;
      setIsDragStarted(false);
    };

    // 添加事件监听器
    button.addEventListener("mousedown", handleNativeMouseDown);
    document.addEventListener("mousemove", handleNativeMouseMove);
    document.addEventListener("mouseup", handleNativeMouseUp);
    button.addEventListener("touchstart", handleNativeTouchStart, {
      passive: false,
    });
    document.addEventListener("touchmove", handleNativeTouchMove, {
      passive: false,
    });
    document.addEventListener("touchend", handleNativeTouchEnd);

    return () => {
      button.removeEventListener("mousedown", handleNativeMouseDown);
      document.removeEventListener("mousemove", handleNativeMouseMove);
      document.removeEventListener("mouseup", handleNativeMouseUp);
      button.removeEventListener("touchstart", handleNativeTouchStart);
      document.removeEventListener("touchmove", handleNativeTouchMove);
      document.removeEventListener("touchend", handleNativeTouchEnd);
    };
  }, [onTouchStart, onMouseDown, isDragStarted]);

  return (
    <div
      ref={buttonRef}
      className={cn(
        "floating-panel-reopen-btn cursor-ns-resize pt-8",
        isFloating
          ? "relative" // 浮动状态：相对定位，由父容器控制位置
          : "z-500 fixed bottom-0 left-1/2 -translate-x-1/2 transform" // 收起状态：固定定位在底部
      )}
      // 移除React的事件处理器，只使用原生事件
    >
      <Rectangle />

      {/* 按钮内容 */}
      <div className="absolute inset-0 top-14 flex items-center justify-center">
        <div className="h-1 w-8 rounded-full bg-gray-300"></div>
      </div>
    </div>
  );
};

const DragHandle = ({
  onMouseDown,
  onTouchStart,
}: {
  onMouseDown: (e: React.MouseEvent) => void;
  onTouchStart: (e: React.TouchEvent) => void;
}) => {
  const handleRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handle = handleRef.current;
    if (!handle) return;

    // 使用原生事件监听器处理触摸事件，设置passive: false
    const handleNativeTouchStart = (e: TouchEvent) => {
      e.preventDefault(); // 现在可以正常调用preventDefault
      e.stopPropagation();

      if (e.touches.length > 0) {
        const touch = e.touches[0];
        if (touch) {
          // 创建一个简化的事件对象
          const mockEvent = {
            touches: [{ clientY: touch.clientY }],
            preventDefault: () => {},
            stopPropagation: () => {},
          } as unknown as React.TouchEvent;
          onTouchStart(mockEvent);
        }
      }
    };

    handle.addEventListener("touchstart", handleNativeTouchStart, {
      passive: false,
    });

    return () => {
      handle.removeEventListener("touchstart", handleNativeTouchStart);
    };
  }, [onTouchStart]);

  return (
    <div
      ref={handleRef}
      className="floating-panel-drag-handle w-18 absolute left-1/2 top-2 z-50 flex h-8 -translate-x-1/2 -translate-y-1/2 transform cursor-ns-resize items-center justify-center"
      onMouseDown={onMouseDown}
      title="拖拽调整高度"
    >
      <div className="h-1 w-8 rounded-full bg-gray-300"></div>
    </div>
  );
};

// 屏幕高度的2/3
const DEFAULT_HEIGHT = getDefaultHeight();

export const FloatingPanel: React.FC<FloatingPanelProps> = ({
  isVisible,
  onClose,
  children,
  header,
  className,
  footer,
  contentKey,
}) => {
  const panelRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const [height, setHeight] = useState(() => {
    // 默认高度为屏幕高度的2/3
    if (typeof window !== "undefined") {
      return DEFAULT_HEIGHT;
    }
    return MIN_HEIGHT; // 服务端渲染时的默认值
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartY, setDragStartY] = useState(0);
  const [dragStartHeight, setDragStartHeight] = useState(0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isDraggingFromCollapsed, setIsDraggingFromCollapsed] = useState(false);
  const [isFullyCollapsed, setIsFullyCollapsed] = useState(false); // 新增：是否完全收起状态
  const animationFrameRef = useRef<number | null>(null);

  // 高度约束计算 - 允许拖拽到最小高度以下
  const constrainHeight = useCallback((newHeight: number): number => {
    if (typeof window === "undefined") return newHeight;

    const maxHeight = window.innerHeight * MAX_HEIGHT_RATIO;
    // 允许拖拽到0，但不能超过最大高度
    return Math.max(0, Math.min(newHeight, maxHeight));
  }, []);

  // 鼠标/触摸按下开始拖拽 - 只处理拖拽手柄的事件
  const handleDragHandleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStartY(e.clientY);
      setDragStartHeight(height);

      e.preventDefault();
      e.stopPropagation();
    },
    [height]
  );

  // 触摸开始拖拽 - 只处理拖拽手柄的事件
  const handleDragHandleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (e.touches.length > 0) {
        setIsDragging(true);
        setDragStartY(e.touches[0]?.clientY ?? 0);
        setDragStartHeight(height);

        // 注意：不在这里调用preventDefault，因为React的触摸事件是被动的
        e.stopPropagation();
      }
    },
    [height]
  );

  // ReopenButton的拖拽处理 - 只能拖拽，不能点击
  const handleReopenButtonMouseDown = useCallback(
    (e: React.MouseEvent) => {
      // 只有在完全收起状态才能通过ReopenButton拖拽
      if (isFullyCollapsed) {
        setIsDragging(true);
        setDragStartY(e.clientY);
        setDragStartHeight(0); // 从0开始
        setIsDraggingFromCollapsed(true);
        setIsCollapsed(false); // 立即取消收起状态，开始显示浮层
        setIsFullyCollapsed(false); // 取消完全收起状态
      }

      e.preventDefault();
      e.stopPropagation();
    },
    [isFullyCollapsed]
  );

  const handleReopenButtonTouchStart = useCallback(
    (e: React.TouchEvent) => {
      // 只有在完全收起状态才能通过ReopenButton拖拽
      if (isFullyCollapsed && e.touches.length > 0) {
        setIsDragging(true);
        setDragStartY(e.touches[0]?.clientY ?? 0);
        setDragStartHeight(0); // 从0开始
        setIsDraggingFromCollapsed(true);
        setIsCollapsed(false); // 立即取消收起状态，开始显示浮层
        setIsFullyCollapsed(false); // 取消完全收起状态

        e.stopPropagation();
      }
    },
    [isFullyCollapsed]
  );

  // 🔥 优化：使用节流和RAF优化鼠标移动性能
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      // 取消之前的动画帧，避免重复调用
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // 使用requestAnimationFrame确保流畅的动画，并添加节流
      animationFrameRef.current = requestAnimationFrame(() => {
        const deltaY = dragStartY - e.clientY; // 向上拖拽为正值
        const newHeight = dragStartHeight + deltaY;
        const constrainedHeight = constrainHeight(newHeight);

        // 🔥 优化：只有高度变化超过阈值才更新，减少不必要的重渲染
        if (Math.abs(constrainedHeight - height) > 1) {
          setHeight(constrainedHeight);
        }
        animationFrameRef.current = null;
      });
    },
    [isDragging, dragStartY, dragStartHeight, constrainHeight, height]
  );

  // 🔥 优化：使用节流和RAF优化触摸移动性能
  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!isDragging || e.touches.length === 0 || !e.touches[0]) return;

      e.preventDefault(); // 防止页面滚动

      // 取消之前的动画帧，避免重复调用
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // 使用requestAnimationFrame确保流畅的动画，并添加节流
      animationFrameRef.current = requestAnimationFrame(() => {
        const touch = e.touches[0];
        if (!touch) return;

        const deltaY = dragStartY - touch.clientY; // 向上拖拽为正值
        const newHeight = dragStartHeight + deltaY;
        const constrainedHeight = constrainHeight(newHeight);

        // 🔥 优化：只有高度变化超过阈值才更新，减少不必要的重渲染
        if (Math.abs(constrainedHeight - height) > 1) {
          setHeight(constrainedHeight);
        }
        animationFrameRef.current = null;
      });
    },
    [isDragging, dragStartY, dragStartHeight, constrainHeight, height]
  );

  // 鼠标松开结束拖拽
  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);

      // 如果是从收起状态拖拽
      if (isDraggingFromCollapsed) {
        setIsDraggingFromCollapsed(false);

        // 如果拖拽高度不足最小高度，设置为最小高度
        if (height < MIN_HEIGHT) {
          setHeight(MIN_HEIGHT);
        }
      } else {
        // 正常拖拽逻辑：判断是否需要收起
        if (height < MIN_HEIGHT) {
          setIsCollapsed(true);
          setHeight(0); // 完全收起
          // 延迟设置完全收起状态，确保动画完成后再显示ReopenButton
          setTimeout(() => {
            setIsFullyCollapsed(true);
          }, 200); // 等待过渡动画完成
        }
      }

      // 清理动画帧
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    }
  }, [isDragging, height, isDraggingFromCollapsed]);

  // 添加全局鼠标和触摸事件监听
  useEffect(() => {
    if (isDragging) {
      // 鼠标事件
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      // 触摸事件
      document.addEventListener("touchmove", handleTouchMove, {
        passive: false,
      });
      document.addEventListener("touchend", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        document.removeEventListener("touchmove", handleTouchMove);
        document.removeEventListener("touchend", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove]);

  // 防止滚动穿透
  useEffect(() => {
    if (isVisible) {
      document.body.style.overflow = "hidden";
      return () => {
        document.body.style.overflow = "";
      };
    }
  }, [isVisible]);

  // 组件卸载时清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  if (!isVisible) return null;

  return (
    <>
      {/* 遮罩层 */}
      {!isCollapsed && (
        <div className="floating-panel-overlay fixed inset-0 z-40 bg-[rgba(0,0,0,0.2)]" />
      )}

      {/* 只有在完全收起状态或从收起状态拖拽时才显示ReopenButton */}
      {(isDraggingFromCollapsed || isFullyCollapsed) && (
        <div
          className="fixed left-1/2 z-[60] -translate-x-1/2 transform"
          style={{
            bottom: `${height}px`, // 定位到浮层上方
          }}
        >
          <ReopenButton
            onMouseDown={handleReopenButtonMouseDown}
            onTouchStart={handleReopenButtonTouchStart}
            isFloating={true}
          />
        </div>
      )}

      {/* 浮层容器 */}
      <div
        ref={panelRef}
        className={cn(
          "rounded-t-4xl fixed bottom-0 left-0 right-0 z-50 overflow-hidden bg-white",
          "flex flex-col",
          isDragging ? "cursor-grabbing" : "cursor-default",
          className
        )}
        style={{
          height: height,
          maxHeight: `${MAX_HEIGHT_RATIO * 100}vh`,
          // 🔥 优化：更强的硬件加速优化
          transform: "translateZ(0)",
          backfaceVisibility: "hidden",
          perspective: 1000,
          willChange: isDragging ? "height" : "auto",
          // 🔥 优化：拖拽时完全禁用过渡动画，提升性能
          transition: isDragging ? "none" : "height 0.2s ease-out",
          // 🔥 优化：启用GPU合成层
          isolation: "isolate",
        }}
      >
        {/* 头部导航区域 */}
        <div className="floating-panel-header relative flex items-center justify-between">
          <div className="absolute left-[-10%] top-[-2px] z-[-1] h-[6rem] w-[120%]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1000 96"
              fill="none"
            >
              <path
                d="M32 0.5H968C985.397 0.5 999.5 14.603 999.5 32V95.5H0.5V32C0.5 14.603 14.603 0.5 32 0.5Z"
                fill="white"
              />
              <path
                d="M32 0.5H968C985.397 0.5 999.5 14.603 999.5 32V95.5H0.5V32C0.5 14.603 14.603 0.5 32 0.5Z"
                fill="url(#paint0_radial_5975_5332)"
              />
              <path
                d="M32 0.5H968C985.397 0.5 999.5 14.603 999.5 32V95.5H0.5V32C0.5 14.603 14.603 0.5 32 0.5Z"
                fill="url(#paint1_radial_5975_5332)"
              />
              <path
                d="M32 0.5H968C985.397 0.5 999.5 14.603 999.5 32V95.5H0.5V32C0.5 14.603 14.603 0.5 32 0.5Z"
                stroke="white"
              />
              <defs>
                <radialGradient
                  id="paint0_radial_5975_5332"
                  cx="0"
                  cy="0"
                  r="1"
                  gradientUnits="userSpaceOnUse"
                  gradientTransform="translate(385) scale(600.5 80.0723)"
                >
                  <stop stopColor="#F3F4FF" />
                  <stop offset="1" stopColor="#F3F4FF" stopOpacity="0" />
                </radialGradient>
                <radialGradient
                  id="paint1_radial_5975_5332"
                  cx="0"
                  cy="0"
                  r="1"
                  gradientUnits="userSpaceOnUse"
                  gradientTransform="translate(39) scale(243 77.6273)"
                >
                  <stop stopColor="#FFF1F1" />
                  <stop offset="1" stopColor="#FFF1F1" stopOpacity="0" />
                </radialGradient>
              </defs>
            </svg>
          </div>

          {/* 中间拖拽手柄 - 只在非从收起状态拖拽时显示 */}
          {!isDraggingFromCollapsed && (
            <DragHandle
              onMouseDown={handleDragHandleMouseDown}
              onTouchStart={handleDragHandleTouchStart}
            />
          )}

          {/* 关闭按钮 */}
          <button
            onClick={onClose}
            className="floating-panel-close-btn absolute right-6 top-6 z-50 h-9 w-9 p-1"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 36 36"
              fill="none"
            >
              <path
                d="M0 18C0 8.05888 8.05888 0 18 0C27.9411 0 36 8.05888 36 18C36 27.9411 27.9411 36 18 36C8.05888 36 0 27.9411 0 18Z"
                fill="#1F1D1B"
                fillOpacity="0.05"
              />
              <path
                d="M23 13L13 23M13 13L23 23"
                stroke="#33302D"
                strokeOpacity="0.55"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className="header-content flex h-[6rem] flex-1 justify-center">
            {header}
          </div>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div
          key={contentKey}
          ref={contentRef}
          className="floating-panel-content mt-4 flex-1 overflow-y-auto overflow-x-hidden"
        >
          {children}
          {/* 底部按钮区域 - 固定在浮层底部 */}
          <div className="floating-panel-footer flex-shrink-0">{footer}</div>
        </div>
      </div>
    </>
  );
};

export default FloatingPanel;
