"use client";

import { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import React, { memo } from "react";
import { StageType } from "../../types";
import ExamPointAnalysisStage from "../stages/exam-point-analysis-stage";
import ProblemAnalysisStage from "../stages/problem-analysis-stage";
import SolutionSummaryStage from "../stages/solution-summary-stage";
import StepByStepGuideStage from "../stages/step-by-step-guide-stage";

// 基于实际mock数据的类型定义 - 扩展统一类型以支持UI特定字段
interface ProblemAnalysisData {
  guideText?: string;
  analysisItems: string;
  keyInfo: {
    title: string;
    content: string;
    icon: React.ReactNode;
  };
}

interface ExamPointAnalysisData {
  guideText?: string;
  target: string;
  examPoint: string;
  solutionBreakthrough: string;
}

interface SolutionSummaryData {
  guideText?: string;
  solutionSteps: string;
  finalAnswer: string;
}

// 扩展的阶段数据，包含UI特定字段和统一的AiExplanation数据
interface StageData {
  problemAnalysis?: ProblemAnalysisData;
  examPointAnalysis?: ExamPointAnalysisData;
  stepByStepGuide?: AiExplanation["stepByStepGuide"]; // 🔥 使用统一类型
  solutionSummary?: SolutionSummaryData;
}

interface StageContentRendererProps {
  currentStage: StageType;
  data: StageData; // 从外部传入的数据
  className?: string;
  record?: AiExplanationRecord; // 互动记录
  onStepProgress?: (stepIndex: number) => void; // 步骤进度回调
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void; // 答题回调
  onAnimationComplete?: () => void; // 动画完成回调
  // 🔥 修复：新增分别处理问题内容和解析内容的动画完成回调
  onQuestionAnimationComplete?: () => void;
  onAnalysisAnimationComplete?: () => void;
}

export const StageContentRenderer: React.FC<StageContentRendererProps> = memo(
  ({
    currentStage,
    data,
    className,
    record,
    onStepProgress,
    onAnswer,
    onAnimationComplete,
    onQuestionAnimationComplete,
    onAnalysisAnimationComplete,
  }) => {
    const renderStageContent = () => {
      switch (currentStage) {
        case StageType.ProblemAnalysis:
          return data.problemAnalysis ? (
            <ProblemAnalysisStage
              data={data.problemAnalysis}
              className={className}
              onAnimationComplete={onAnimationComplete}
            />
          ) : null;

        case StageType.ExamPointAnalysis:
          return data.examPointAnalysis ? (
            <ExamPointAnalysisStage
              data={data.examPointAnalysis}
              className={className}
              onAnimationComplete={onAnimationComplete}
            />
          ) : null;

        case StageType.StepByStepGuide:
          return data.stepByStepGuide ? (
            <StepByStepGuideStage
              data={data.stepByStepGuide}
              className={className}
              record={record}
              onStepProgress={onStepProgress}
              onAnswer={onAnswer}
              onQuestionAnimationComplete={onQuestionAnimationComplete}
              onAnalysisAnimationComplete={onAnalysisAnimationComplete}
              onAnimationComplete={onAnimationComplete}
            />
          ) : null;

        case StageType.SolutionSummary:
          return data.solutionSummary ? (
            <SolutionSummaryStage
              data={data.solutionSummary}
              className={className}
              onAnimationComplete={onAnimationComplete}
            />
          ) : null;

        default:
          return (
            <div className="unknown-stage p-8 text-center">
              <div className="rounded-lg border border-[#FFEAA7] bg-[#FFF3CD] p-6">
                <h3 className="mb-2 text-lg font-semibold text-[#856404]">
                  未知阶段
                </h3>
                <p className="text-[#856404]">
                  当前阶段 &ldquo;{currentStage}&rdquo; 暂未支持
                </p>
              </div>
            </div>
          );
      }
    };

    return (
      <div className="stage-content-renderer px-6 pb-6">
        {renderStageContent()}
      </div>
    );
  }
);

StageContentRenderer.displayName = "StageContentRenderer";

export default StageContentRenderer;
