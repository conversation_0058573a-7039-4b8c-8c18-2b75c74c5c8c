"use client";

import { signal } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { AiExplanationRecord } from "@repo/core/types";
import Button from "@repo/ui/components/press-button";
import { AnimatePresence, motion } from "framer-motion";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useInteractiveExplanationTracking } from "../hooks/use-interactive-explanation-tracking"; // 埋点Hook
import { aiExplanation } from "../mock/ai-explanation";
import { useReportInteractiveExplanation } from "../model";
import { StageType } from "../types";
import StageContentRenderer from "./components/stage-content-renderer";
import StageNavigation from "./components/stage-navigation";
import { FloatingPanel } from "./floating-panel";

// 基于 signal 的记录管理, 切换题目不会清理记录数据，实现本地缓存互动解题记录
const recordsSignal = signal<Record<string, AiExplanationRecord>>({});
interface InteractiveExplanationProps {
  /** 是否显示浮层 */ isVisible: boolean /** 关闭浮层回调 */;
  onClose: () => void /** 初始数据（可选，默认使用mock数据） */;
  initialData?: typeof aiExplanation /** 初始记录（可选，默认使用mock数据） */;
  initialRecord?: AiExplanationRecord /** 进度变更回调 */;
  onProgressChange?: (record: AiExplanationRecord) => void /** 答题回调 */;
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void /** 题目ID */;
  questionId: string /** 学习会话ID */;
  studySessionId?: number;
  /** 课程ID */
  lessonId?: number /** 是否是预览模式 */;
  preview?: boolean;
  studyType?: StudyType;
}

/**
 * 互动讲题主组件
 * 整合aiExplanation数据和aiExplanationRecord记录
 */
export const InteractiveExplanation: React.FC<InteractiveExplanationProps> = ({
  isVisible,
  onClose,
  initialData = aiExplanation,
  initialRecord,
  onProgressChange,
  onAnswer,
  questionId,
  studySessionId = 0,
  preview = false,
  studyType = StudyType.AI_COURSE,
}) => {
  // 创建默认记录结构（不包含guideText）
  const createDefaultRecord = useCallback((): AiExplanationRecord => {
    const stepCount = initialData.stepByStepGuide.steps.length;
    return {
      currentProgress: 0,
      examPointAnalysisGuideText: undefined,
      problemAnalysisGuideText: undefined,
      solutionSummaryGuideText: undefined,
      stepByStepGuide: Array.from({ length: stepCount }, () => ({
        guideText: undefined,
        isAnswer: false,
      })),
      stepByStepGuideCurrentProgress: 0,
      version: 1,
    };
  }, [initialData.stepByStepGuide.steps.length]);

  // 当前记录状态
  const currentRecord =
    recordsSignal.value[questionId] ?? initialRecord ?? createDefaultRecord(); // 内容区域的key，用于强制重新渲染到顶部

  const contentKey = `${questionId}-${currentRecord.currentProgress}-${currentRecord.stepByStepGuideCurrentProgress}`; // 进度上报hook
  // 🔥 修复：分别跟踪问题内容和解析内容的动画状态
  const [questionAnimationComplete, setQuestionAnimationComplete] =
    useState(false);
  const [analysisAnimationComplete, setAnalysisAnimationComplete] =
    useState(false);
  useEffect(() => {
    console.log("🚀 isVisible11111:", isVisible);
  }, [isVisible]);
  const { reportInteractiveExplanation } = useReportInteractiveExplanation(); // 上报进度的函数

  const reportProgress = useCallback(
    async (updatedRecord: AiExplanationRecord) => {
      if (!studySessionId) {
        return;
      }
      const payload = {
        questionId,
        studySessionId,
        version: updatedRecord.version,
        record: {
          stepByStepGuideCurrentProgress:
            updatedRecord.stepByStepGuideCurrentProgress,
          currentProgress: updatedRecord.currentProgress,
          stepByStepGuide: updatedRecord.stepByStepGuide.map((step) => ({
            isAnswer: step.isAnswer,
          })),
        },
      };

      console.log("🚀 准备上报进度:", payload);

      try {
        const result = await reportInteractiveExplanation(payload);
        console.log("✅ 进度上报成功:", result);
      } catch (error) {
        console.error("❌ 进度上报失败:", error);
      }
    },
    [reportInteractiveExplanation, questionId, studySessionId]
  ); // 当前阶段映射

  const stageMapping = useMemo(
    () => [
      StageType.ProblemAnalysis,
      StageType.ExamPointAnalysis,
      StageType.StepByStepGuide,
      StageType.SolutionSummary,
    ],
    []
  );

  // 自动化埋点Hook
  const { trackEnter, trackExit, trackJump } =
    useInteractiveExplanationTracking({
      questionId,
      studySessionId,
      currentRecord,
      stageMapping,
      preview,
      studyType,
    });

  useEffect(() => {
    trackEnter();
  }, []);

  const currentStage =
    stageMapping[currentRecord.currentProgress ?? 0] ??
    StageType.ProblemAnalysis; // 构造符合组件接口的数据结构

  const stageData = {
    problemAnalysis: {
      guideText: currentRecord.problemAnalysisGuideText,
      analysisItems: `${initialData.problemAnalysis.questionRequirement}`,
      keyInfo: {
        title: "关键信息",
        content: initialData.problemAnalysis.essentialInfo,
        icon: (
          <div className="interactive-explanation-key-info-icon flex h-[30px] w-[30px] items-center justify-center rounded bg-white">
            <div className="flex h-[24px] w-[22px] items-center justify-center rounded bg-[#FFC55A]">
              <div className="h-[12px] w-[13px] rounded bg-[#FFF2F2]"></div>
            </div>
          </div>
        ),
      },
    },
    examPointAnalysis: {
      guideText: currentRecord.examPointAnalysisGuideText,
      target: initialData.examPointAnalysis.target,
      examPoint: initialData.examPointAnalysis.examPoint,
      solutionBreakthrough: initialData.examPointAnalysis.solutionBreakthrough,
    },
    stepByStepGuide: initialData.stepByStepGuide,
    solutionSummary: {
      guideText: currentRecord.solutionSummaryGuideText,
      solutionSteps: initialData.solutionSummary.solutionIdea,
      finalAnswer: initialData.solutionSummary.finalAnswer,
    },
  }; // 阶段切换处理

  const handleStageChange = useCallback(
    (stage: StageType) => {
      const stageIndex = stageMapping.indexOf(stage);
      if (stageIndex !== -1 && stageIndex !== currentRecord.currentProgress) {
        const newRecord = {
          ...currentRecord,
          currentProgress: stageIndex,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("navigation", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress(newRecord);
      }
    },
    [
      currentRecord,
      stageMapping,
      onProgressChange,
      reportProgress,
      questionId,
      trackJump,
    ]
  ); // 步骤进度处理（逐步讲解阶段）

  const handleStepProgress = useCallback(
    (stepIndex: number) => {
      if (currentRecord.currentProgress === 2) {
        // 逐步讲解阶段
        const newRecord = {
          ...currentRecord,
          stepByStepGuideCurrentProgress: stepIndex,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("navigation", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress(newRecord);
      }
    },
    [currentRecord, onProgressChange, reportProgress, questionId, trackJump]
  ); // 答题处理

  const handleAnswer = useCallback(
    (stepIndex: number, isCorrect: boolean) => {
      if (currentRecord.currentProgress === 2) {
        // 逐步讲解阶段
        const newStepByStepGuide = [...currentRecord.stepByStepGuide];
        if (newStepByStepGuide[stepIndex]) {
          newStepByStepGuide[stepIndex] = {
            ...newStepByStepGuide[stepIndex],
            isAnswer: true,
          };
        }

        const newRecord = {
          ...currentRecord,
          stepByStepGuide: newStepByStepGuide,
        };
        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        onProgressChange?.(newRecord);
        onAnswer?.(stepIndex, isCorrect);

        // 答题正确后重置解析动画状态，等待解析内容动画完成
        if (isCorrect) {
          setAnalysisAnimationComplete(false);
          reportProgress(newRecord);
        }
      }
    },
    [
      currentRecord,
      onProgressChange,
      onAnswer,
      reportProgress,
      questionId,
      setAnalysisAnimationComplete,
    ]
  ); // 下一步处理

  const handleNext = useCallback(() => {
    // 如果当前在逐步讲解阶段，优先控制二级导航
    if (currentRecord.currentProgress === 2) {
      // 逐步讲解阶段
      const totalSteps = initialData.stepByStepGuide.steps.length;
      const currentStepIndex = currentRecord.stepByStepGuideCurrentProgress; // 如果还有下一步，则进入下一步

      if (currentStepIndex < totalSteps - 1) {
        const newRecord = {
          ...currentRecord,
          stepByStepGuideCurrentProgress: currentStepIndex + 1,
        };

        recordsSignal.value = {
          ...recordsSignal.value,
          [questionId]: newRecord,
        };
        trackJump("next_button", newRecord);
        onProgressChange?.(newRecord); // 上报进度
        reportProgress(newRecord);
        return;
      } // 如果已经是最后一步，则跳转到下一个一级阶段
    }

    console.log(
      "🚀 当前阶段:",
      currentRecord.currentProgress,
      stageMapping.length
    ); // 其他阶段或逐步讲解已完成，切换到下一个一级阶段

    if (currentRecord.currentProgress < stageMapping.length - 1) {
      const newRecord = {
        ...currentRecord,
        currentProgress: currentRecord.currentProgress + 1,
      };

      recordsSignal.value = {
        ...recordsSignal.value,
        [questionId]: newRecord,
      };
      trackJump("next_button", newRecord);
      onProgressChange?.(newRecord); // 上报进度
      reportProgress(newRecord);
    }
  }, [
    currentRecord,
    stageMapping.length,
    onProgressChange,
    initialData.stepByStepGuide.steps.length,
    reportProgress,
    questionId,
    trackJump,
  ]); // 监听外部数据变化

  useEffect(() => {
    if (!recordsSignal.value[questionId]) {
      recordsSignal.value = {
        ...recordsSignal.value,
        [questionId]: initialRecord ?? createDefaultRecord(),
      };
    }
  }, [initialRecord, questionId, createDefaultRecord]); // 动画加载状态管理

  // 🔥 修复：分别处理问题内容和解析内容的动画完成
  const handleQuestionAnimationComplete = useCallback(() => {
    console.log("🚀 问题内容动画完成");
    setQuestionAnimationComplete(true);
  }, []);

  const handleAnalysisAnimationComplete = useCallback(() => {
    console.log("🚀 解析内容动画完成");
    setAnalysisAnimationComplete(true);
  }, []);

  useEffect(() => {
    // 当阶段或步骤变化时，重置所有动画完成状态
    setQuestionAnimationComplete(false);
    setAnalysisAnimationComplete(false);
  }, [
    currentRecord.currentProgress,
    currentRecord.stepByStepGuideCurrentProgress,
  ]);

  const showNextButton = useMemo(() => {
    // 只有在最后一个阶段（思路总结）才不显示按钮
    if (
      currentRecord.currentProgress &&
      currentRecord.currentProgress >= stageMapping.length - 1
    ) {
      return false;
    }

    // 🔥 zen 大师修复：根据不同阶段检查相应的动画完成状态
    if (currentRecord.currentProgress === 2) {
      // 逐步讲解阶段
      const currentStepIndex = currentRecord.stepByStepGuideCurrentProgress;
      const stepRecord = currentRecord.stepByStepGuide[currentStepIndex];
      const isStepCompleted = stepRecord?.isAnswer === true;

      // 对于未完成的步骤，必须先完成问题内容动画
      if (!isStepCompleted && !questionAnimationComplete) {
        return false;
      }

      // 如果还没答题，不显示下一步按钮
      if (!isStepCompleted) {
        return false;
      }

      // 答题后，必须等待解析内容动画完成
      if (!analysisAnimationComplete) {
        return false;
      }
    } else {
      // 其他阶段，只需要问题内容动画完成
      if (!questionAnimationComplete) {
        return false;
      }
    }

    return true;
  }, [
    currentRecord,
    stageMapping.length,
    questionAnimationComplete,
    analysisAnimationComplete,
  ]);

  return (
    <FloatingPanel
      isVisible={isVisible}
      onClose={() => {
        trackExit();
        onClose();
      }}
      className="interactive-explanation-panel"
      contentKey={contentKey}
      header={
        <StageNavigation
          currentStage={currentStage}
          onStageChange={handleStageChange}
          stepCount={initialData.stepByStepGuide.steps.length}
          className="interactive-explanation-navigation"
        />
      }
      footer={
        <AnimatePresence>
          {showNextButton && (
            <motion.div
              className="flex justify-end bg-white p-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ delay: 0.6, duration: 0.4, ease: "easeOut" }}
            >
              <Button
                color="orange"
                className="floating-panel-next-btn interactive-explanation-next-button"
                onClick={handleNext} // Disable logic remains the same
                disabled={
                  currentRecord.currentProgress >= stageMapping.length - 1
                }
              >
                下一步
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      }
    >
      <div className="interactive-explanation-content">
        <StageContentRenderer
          currentStage={currentStage}
          data={stageData}
          record={currentRecord}
          onStepProgress={handleStepProgress}
          onAnswer={handleAnswer}
          onQuestionAnimationComplete={handleQuestionAnimationComplete}
          onAnalysisAnimationComplete={handleAnalysisAnimationComplete}
          onAnimationComplete={handleQuestionAnimationComplete} // 其他阶段使用问题动画完成回调
        />
      </div>
    </FloatingPanel>
  );
};

InteractiveExplanation.displayName = "InteractiveExplanation";

export default InteractiveExplanation;
