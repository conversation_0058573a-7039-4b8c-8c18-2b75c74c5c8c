#!/bin/sh

# 设置环境变量
# export NEXT_PUBLIC_STU_API_HOST=https://demo41-api.test.xiaoluxue.cn
# export NEXT_PUBLIC_API_HOST=https://lesson-kit-api.test.xiaoluxue.cn
# ## 用户中心
# export NEXT_PUBLIC_UCENTER_API_URL=https://ucenter-api.test.xiaoluxue.cn


build() {

    echo "正在构建项目..."
    pnpm build:test || { echo "构建失败"; exit 1; }

    echo "正在复制静态资源..."
    cp -r apps/stu/public apps/stu/dist/standalone/apps/stu
    cp -r apps/stu/dist/static apps/stu/dist/standalone/apps/stu/dist/

    cp -r apps/tch/public apps/tch/dist/standalone/apps/tch
    cp -r apps/tch/dist/static apps/tch/dist/standalone/apps/tch/dist/

    cp -r apps/aipt/public apps/aipt/dist/standalone/apps/aipt
    cp -r apps/aipt/dist/static apps/aipt/dist/standalone/apps/aipt/dist/

    echo "构建完成"
}

# 执行构建命令
build
