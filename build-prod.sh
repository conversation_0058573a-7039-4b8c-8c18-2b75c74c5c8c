#!/bin/sh

# 设置环境变量
# export STU_API_HOST=https://demo41-api.xiaoluxue.cn

# # 教师端
# export NEXT_PUBLIC_API_URL=https://tch.xiaoluxue.cn
# # 用户中心
# export NEXT_PUBLIC_UCENTER_API_URL=https://ucenter-api.xiaoluxue.cn

build() {

    echo "正在构建项目..."
    pnpm build:prod || { echo "构建失败"; exit 1; }

    echo "正在复制静态资源..."
    # 学生端
    cp -r apps/stu/public apps/stu/dist/standalone/apps/stu
    cp -r apps/stu/dist/static apps/stu/dist/standalone/apps/stu/dist/

    # 教师端
    cp -r apps/tch/public apps/tch/dist/standalone/apps/tch
    cp -r apps/tch/dist/static apps/tch/dist/standalone/apps/tch/dist/

    # AIPT
    cp -r apps/aipt/public apps/aipt/dist/standalone/apps/aipt
    cp -r apps/aipt/dist/static apps/aipt/dist/standalone/apps/aipt/dist/

}

# 执行构建命令
build