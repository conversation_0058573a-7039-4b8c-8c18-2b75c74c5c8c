{"name": "question", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "build:dev": "cross-env REACT_APP_ENV=devProd UMI_ENV=devProd max build", "build:local": "cross-env REACT_APP_ENV=localProd UMI_ENV=localProd max build", "build:prod": "cross-env REACT_APP_ENV=prodProd UMI_ENV=prodProd max build", "build:test": "cross-env REACT_APP_ENV=testProd UMI_ENV=testProd max build", "deploy": "npm run build && npm run gh-pages", "deploy:dev": "pnpm build:local && rm -rf /home/<USER>/nginx/html/question/* && mv dist/* /home/<USER>/nginx/html/question && cd /home/<USER>/nginx && docker-compose restart", "dev": "npm run start:dev", "dev:force": "rm -rf node_modules/.cache && npm run dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:old": "npm run lint:js && npm run lint:prettier", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "online": "sh ./deploy.sh", "openapi": "max openapi", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=devProd max dev", "start:local": "cross-env REACT_APP_ENV=local MOCK=none UMI_ENV=localProd max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.8.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@antv/l7": "^2.22.5", "@antv/l7-maps": "^2.22.5", "@antv/l7-react": "^2.4.3", "@emotion/css": "^11.13.5", "@fingerprintjs/fingerprintjs": "^4.6.1", "@remixicon/react": "^4.6.0", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@repo/core": "workspace:*", "@repo/lib": "workspace:*", "@repo/ui": "workspace:*", "@tiptap/core": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/katex": "^0.16.7", "@uiw/react-color": "^2.5.5", "@umijs/route-utils": "^2.2.2", "ahooks": "^3.8.4", "ali-oss": "^6.22.0", "antd": "^5.25.4", "antd-style": "^3.7.1", "await-to-js": "^3.0.0", "axios": "^1.9.0", "better-react-mathjax": "^2.1.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "hammerjs": "^2.0.8", "immer": "^10.1.1", "katex": "^0.16.21", "lodash": "^4.17.21", "mathlive": "^0.105.2", "md5": "^2.3.0", "mitt": "^3.0.1", "numeral": "^2.0.6", "omit.js": "^2.0.2", "prosemirror-model": "^1.25.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.3", "querystring": "^0.2.1", "rc-util": "^5.44.4", "react": "catalog:react19", "react-beautiful-dnd": "^13.1.1", "react-dom": "catalog:react19", "react-error-boundary": "^5.0.0", "react-fittext": "^1.0.0", "react-stickynode": "^5.0.2", "swr": "^2.3.3", "uuid": "^11.1.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/ali-oss": "^6.16.11", "@types/classnames": "^2.3.4", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/lodash.debounce": "^4.0.9", "@types/md5": "^2.3.5", "@types/react": "catalog:react19", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "catalog:react19", "@types/react-helmet": "^6.1.11", "@types/react-stickynode": "^4.0.3", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.4.6", "@umijs/max": "^4.4.6", "cross-env": "^7.0.3", "express": "^4.21.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockjs": "^1.1.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.5.10", "prettier-plugin-two-style-order": "^1.0.1", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^4.9.5", "umi-presets-pro": "^2.0.3"}, "engines": {"node": ">=12.0.0"}}