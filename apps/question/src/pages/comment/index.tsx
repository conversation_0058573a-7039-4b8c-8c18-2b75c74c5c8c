import {
  fetchCommentList,
  modifyCommentStatus,
  queryCommentEnumConstants,
} from '@/services/api/comment';
import { removeNullFields } from '@/utils';
import EnumManager from '@/utils/enumManager';
import {
  FileDoneOutlined,
  FileExcelOutlined,
  FileExclamationOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Dropdown, Flex, MenuProps, message, Modal, Select, Space, Typography } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
const { Link } = Typography;

type CommentEnumKey = keyof API.Comment.CommentEnumConstantData;

const getCommentOps = (record: API.Comment.CommentItem): MenuProps['items'] => {
  const { commentStatus, llmScore } = record;
  const ret: MenuProps['items'] = [];
  if (commentStatus !== 2) {
    // 状态不是问题反馈
    ret.push({
      key: '1',
      label: '转为问题反馈',
      icon: <FileExclamationOutlined />,
    });
  }
  if (llmScore === -1 || commentStatus === 2) {
    ret.push({
      key: '2',
      label: '转为正常',
      icon: <FileDoneOutlined />,
    });
  }
  return [
    ...ret,
    {
      key: '3',
      label: '删除评论',
      icon: <FileExcelOutlined />,
      danger: true,
    },
  ];
};

const Comment: React.FC = () => {
  const [modal, contextHolder] = Modal.useModal();
  const [messageApi, messageContextHolder] = message.useMessage();
  const [commentEnumConstants, setCommentEnumConstants] =
    useState<API.Comment.CommentEnumConstantData | null>(null);
  const [llmScore, setLlmScore] = useState<number>(0);
  const [normalModalOpen, setNormalModalOpen] = useState(false);

  const actionRef = useRef<ActionType>(null);
  const [commentItem, setCommentItem] = useState<API.Comment.CommentItem | null>(null);

  const commentEnumManagers = useMemo(() => {
    if (!commentEnumConstants) {
      return null;
    }

    const keys = Object.keys(commentEnumConstants) as Array<CommentEnumKey>;
    const map: Record<CommentEnumKey, EnumManager<any>> = {} as Record<
      CommentEnumKey,
      EnumManager<any>
    >;
    keys.forEach((key) => {
      const enums = commentEnumConstants[key];
      map[key] = new EnumManager(
        enums.map((item) => ({
          ...item,
          label: item.nameZh,
          text: item.nameZh,
        })),
      );
    });
    return map;
  }, [commentEnumConstants]);

  const columns: ProColumns<API.Comment.CommentItem>[] = useMemo(() => {
    if (!commentEnumManagers) {
      return [];
    }

    const { status, llmScore, teacherRecommended, commentScope } = commentEnumManagers;

    return [
      {
        title: '评论ID',
        order: 3,
        dataIndex: 'commentId',
        width: 80,
        fixed: 'left',
        search: false,
      },
      {
        title: '评论内容',
        order: 2,
        dataIndex: 'commentContent',
        ellipsis: true,
        search: true,
        onCell: () => {
          return {
            style: {
              maxWidth: 350,
            },
          };
        },
      },
      {
        title: '上级评论内容',
        width: 200,
        order: 2,
        dataIndex: 'parentContent',
        ellipsis: true,
        search: false,
        onCell: () => {
          return {
            style: {
              maxWidth: 350,
            },
          };
        },
      },
      {
        title: '评论状态',
        order: 6,
        dataIndex: 'commentStatus',
        width: 110,
        valueType: 'select',
        fieldProps: {
          placeholder: '请选择评论状态',
        },
        valueEnum: status?.getProTableValueEnum(),
        search: true,
      },

      {
        title: 'LLM评分',
        order: 5,
        dataIndex: 'llmScore',
        width: 145,
        valueType: 'select',
        fieldProps: {
          placeholder: '请选择',
        },
        valueEnum: llmScore?.getProTableValueEnum(),
        search: true,
      },
      {
        title: '点赞数',
        dataIndex: 'likesCount',
        width: 100,
        search: false,
      },
      {
        title: '二级评论数',
        dataIndex: 'repliesCount',
        width: 100,
        search: false,
      },
      {
        title: '老师推荐',
        order: 4,
        dataIndex: 'isTeacherRecommended',
        width: 90,
        valueType: 'select',
        fieldProps: {
          placeholder: '请选择',
        },
        valueEnum: teacherRecommended?.getProTableValueEnum(),
        search: true,
      },
      {
        title: '评论者ID',
        order: 3,
        dataIndex: 'userId',
        width: 100,
        search: true,
      },
      {
        title: '评论者姓名',
        order: 1,
        dataIndex: 'userName',
        width: 100,
        search: false,
      },
      {
        title: '评论者归属',
        order: 1,
        dataIndex: 'className',
        width: 100,
        search: false,
      },
      {
        title: '发布时间',
        dataIndex: 'publishedAt',
        width: 200,
        search: false,
      },
      {
        title: '发布范围',
        order: 5,
        dataIndex: 'commentScope',
        width: 100,
        valueEnum: commentScope?.getProTableValueEnum(),
        search: false,
      },
      {
        title: '所属课程',
        order: 2,
        dataIndex: 'courseName',
        ellipsis: true,
        search: false,
      },
      {
        title: '所属模块',
        order: 2,
        dataIndex: 'widgetName',
        ellipsis: true,
        search: false,
      },
      {
        title: '操作',
        width: 120,
        fixed: 'right',
        valueType: 'option',
        search: false,
        align: 'center',
        render: (_, record) => (
          <Dropdown
            arrow
            placement="bottomRight"
            trigger={['click']}
            menu={{
              items: getCommentOps(record),
              selectable: false,
              onClick: (e) => {
                onChangeCommentStatus(e.key, record);
              },
            }}
          >
            <div>
              <Link>
                <Space>
                  更换状态
                  <MoreOutlined />
                </Space>
              </Link>
            </div>
          </Dropdown>
        ),
      },
    ];
  }, [commentEnumManagers]);

  const llmScoreOptions = useMemo(() => {
    if (!commentEnumManagers || !Array.isArray(commentEnumManagers?.llmScore?.enums)) {
      return [];
    }
    return commentEnumManagers.llmScore.enums;
  }, [commentEnumManagers]);

  useEffect(() => {
    queryCommentEnumConstants()
      .then((res) => {
        if (res.code === 0) {
          setCommentEnumConstants(res.data);
          return;
        }
        setCommentEnumConstants(null);
      })
      .catch((err) => {
        console.error('获取枚举常量失败:', err);
        setCommentEnumConstants(null);
      });
  }, []);

  const modifyStatusHandler = async (payload: API.Comment.CommentModifyStatusRequestParams) => {
    try {
      const res = await modifyCommentStatus(payload);
      if (res.code === 0) {
        messageApi.success('修改成功');
        actionRef.current?.reload();
        return;
      }
      messageApi.error('修改操作失败');
    } catch (e) {
      console.error(e);
    }
  };

  const onChangeCommentStatus = (key: string, record: API.Comment.CommentItem) => {
    if (key === '1') {
      convertToQuestionFeedback(record.commentId);
      return;
    }
    if (key === '2') {
      setNormalModalOpen(true);
      setLlmScore(record.llmScore);
      setCommentItem(record);
      return;
    }
    if (key === '3') {
      deleteComment(record.commentId);
      return;
    }
  };

  const convertToQuestionFeedback = (commentId: number) => {
    const confirm = modal.confirm({
      title: '转为问题反馈',
      content: '确定后会将评论状态修改为“问题反馈”',
      onOk: async () => {
        await modifyStatusHandler({ commentId, status: 2 });
        confirm.destroy();
      },
    });
  };

  const convertToNormal = async () => {
    if (!commentItem) {
      return;
    }
    const { commentId } = commentItem;
    const payload = {
      commentId,
      status: 3,
      llmScore: llmScore,
    };
    await modifyStatusHandler(payload);
    setNormalModalOpen(false);
    setLlmScore(0);
    setCommentItem(null);
  };

  const deleteComment = async (commentId: number) => {
    const confirm = modal.confirm({
      title: '删除评论',
      content: '确定后将评论状态修改为“已删除”，逻辑删除该评论。',
      onOk: async () => {
        await modifyStatusHandler({ commentId, status: -1 });
        confirm.destroy();
      },
    });
  };

  return (
    <>
      <PageContainer>
        <ProTable<API.Comment.CommentItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          scroll={{ x: 'max-content' }}
          request={async (params = {}) => {
            console.log('查询参数: ', params);
            const {
              current,
              pageSize,
              userId,
              commentContent,
              commentStatus,
              llmScore,
              isTeacherRecommended,
            } = params;

            let payload: API.Comment.CommentListRequestParams = {
              page: current!,
              pageSize: pageSize!,
              userId: +userId,
              content: commentContent,
              status: +commentStatus,
              llmScore: +llmScore,
              isTeacherRecommended: +isTeacherRecommended,
            };

            payload = removeNullFields(payload) as API.Comment.CommentListRequestParams;
            console.log('评论列表 payload: ', payload);

            try {
              const res = await fetchCommentList(payload);

              return {
                data: res.data.list || [],
                success: res.code === 0,
                total: res.data.pageInfo.total || 0,
              };
            } catch (error) {
              console.error('获取列表失败:', error);
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          }}
          rowKey="commentId"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
            span: {
              xs: 24,
              sm: 12,
              md: 8,
              lg: 8,
              xl: 8,
              xxl: 6,
            },
          }}
          options={{
            reload: true,
            density: true,
            setting: true,
          }}
          form={{
            colon: false,
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          tableLayout="fixed"
          dateFormatter="string"
          headerTitle="评论列表"
        />
      </PageContainer>

      <Modal
        title="转为正常评论"
        open={normalModalOpen}
        onOk={convertToNormal}
        onCancel={() => setNormalModalOpen(false)}
      >
        <Flex gap={20} align="center" style={{ padding: '40px 20px' }}>
          <Typography.Text strong>LLM评分: </Typography.Text>
          <Select
            className="flex-1"
            defaultValue={commentItem?.llmScore ?? 0}
            style={{ width: 120 }}
            onChange={setLlmScore}
            options={llmScoreOptions}
          />
        </Flex>
      </Modal>

      {contextHolder}
      {messageContextHolder}
    </>
  );
};

export default Comment;
