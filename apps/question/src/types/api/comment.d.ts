declare namespace API {
  namespace Comment {
    /**
     * 审核任务列表项
     */
    interface CommentItem {
      commentId: number; // 评论ID
      commentContent: string; // 评论内容
      parentContent: string; // 上级评论内容 (若当前为二级评论)
      commentStatus: number; // 评论状态 1=仅自己可见, 2=问题反馈, 3=正常, -1=删除
      llmScore: number; // LLM评分 -1=问题反馈, 0=违规, 1=无实质信息的评论, 2=轻度学习相关, 3=高质量学习评论
      likesCount: number; // 点赞数
      repliesCount: number; // 二级评论数
      isTeacherRecommended: boolean; // isTeacherRecommended
      userId: number; // 评论者ID
      userName: string; // 评论者姓名
      className: string; // 评论者归属 ??
      commentScope: number; // 发布范围 1=本班, 2=年级, 3=本校, 4=公开
      courseName: string; // 所属课程 ??
      widgetName: string; // 所属模块 ??
      publishedAt: string; // 发布时间（字符串）??
      // isLiked: boolean; // 是否已点赞??
    }

    interface CommentListReference {
      comments: CommentItem[];
      total: number;
    }

    interface CommentListRequestParams {
      userId?: number; // 评论者ID
      content?: string; // 评论者姓名
      status?: number; // 评论状态
      llmScore?: number; // LLM评分
      isTeacherRecommended?: number; // 老师推荐
      page: number; // 页码
      pageSize: number; // 每页条数
    }

    interface CommentModifyStatusRequestParams {
      commentId: number;
      /**
       * 评论状态，0：已删除、1：仅自己可见、2：问题反馈、3：正常
       */
      status: number;
      /**
       * LLM打分，范围0到3
       */
      llmScore?: number;
    }

    interface CommentEnumConstantData {
      /**
       * LLM打分，范围0到3
       */
      llmScore: EnumConstantItem<0 | 1 | 2 | 3>[];
      /**
       * 评论状态
       */
      status: EnumConstantItem<0 | 1 | 2 | 3>[];
      /**
       * 老师推荐
       */
      teacherRecommended: EnumConstantItem<0 | 1>[];
      /**
       * 评论范围
       */
      commentScope: EnumConstantItem<1 | 2 | 3 | 4>[];
    }
  }
}
