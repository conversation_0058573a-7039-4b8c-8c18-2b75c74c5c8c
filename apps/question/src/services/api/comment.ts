import { request } from '@umijs/max';

export const fetchCommentList = (data: API.Comment.CommentListRequestParams) => {
  console.log('data: ', data);
  // return new Promise<API.Response<API.PageData<API.Comment.CommentItem>>>((resolve) => {
  //   setTimeout(() => {
  //     resolve(commentList as unknown as API.Response<API.PageData<API.Comment.CommentItem>>);
  //   }, 1000);
  // });
  return request<API.Response<API.CommentPageData<API.Comment.CommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/comments/list`,
    {
      method: 'POST',
      data,
    },
  );
};

export const queryCommentEnumConstants = () => {
  // return new Promise<API.Response<API.Comment.CommentEnumConstantData>>((resolve) => {
  //   setTimeout(() => {
  //     resolve(enums as unknown as API.Response<API.Comment.CommentEnumConstantData>);
  //   }, 1000);
  // });
  return request<API.Response<API.Comment.CommentEnumConstantData>>(
    `${COMMENT_API_HOST}/api/v1/admin/comment/constants`,
    {
      method: 'GET',
    },
  );
};

export const modifyCommentStatus = (data: API.Comment.CommentModifyStatusRequestParams) => {
  // return new Promise<API.Response<API.Comment.CommentEnumConstantData>>((resolve) => {
  //   setTimeout(() => {
  //     resolve(modify as unknown as API.Response<API.Comment.CommentEnumConstantData>);
  //   }, 1000);
  // });
  return request<API.Response<API.PageData<API.Comment.CommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/comment/status`,
    {
      method: 'POST',
      data,
    },
  );
};
