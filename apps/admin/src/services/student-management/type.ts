// 学生信息接口
export interface StudentInfo {
  userNumber: string;
  userName: string;
  userIsTest: number; // 0-正式 1-测试
  userGrade: number;
  userClass: string;
  userStatus: number; // 1-付费 2-试用 3-停用
  userID: number;
  userAccount: string;
  userPassword: string;
  gradeName: string;
  className: string;
}

// 学生列表查询参数
export interface StudentListParams {
  schoolId: number;
  gradeId?: number;
  classId?: number;
  keyword?: string;
  userIsTest?: number;
  userStatus?: number;
  page?: number;
  pageSize?: number;
}

// 学生列表响应
export interface StudentListResponse {
  total: number;
  list: StudentInfo[];
}

// 学校学生统计信息
export interface SchoolStudentStats {
  totalCount: number;
  paidCount: number;
  trialCount: number;
  stoppedCount: number;
}

// 学生添加参数
export interface AddStudentParams {
  schoolId: number;
  gradeId: number;
  classId: number;
  userName: string;
  userNumber?: string;
  userIsTest?: number;
}

// 学生编辑参数
export interface EditStudentParams {
  userID: number;
  userName?: string;
  userNumber?: string;
  userIsTest?: number;
  userStatus?: number;
}

// 学生转班参数
export interface TransferStudentParams {
  studentIDs: string;
  gradeID: number;
  fromClassID: number;
  toClassID: number;
  schoolID: number;
}

// 批量导入学生参数
export interface BatchImportStudentsParams {
  schoolId: number;
  gradeId: number;
  classId: number;
  students: {
    userName: string;
    userNumber?: string;
    userIsTest: number;
  }[];
}
