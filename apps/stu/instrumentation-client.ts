// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { getAppInfo, getStudentUserInfo } from "@repo/core/utils/stu/device";
import * as Sentry from "@sentry/nextjs";

const getEnvironment = () => {
  const url = process.env.NEXT_PUBLIC_API_HOST || "";
  if (url.includes(".test")) return "test";
  if (url.includes(".dev")) return "dev";
  return "production";
};

if (process.env.NODE_ENV !== "development") {
  Sentry.init({
    dsn: "https://<EMAIL>/2",
    environment: getEnvironment(),
    beforeSend(event) {
      // 互动组件的事件直接丢弃
      if (
        event.exception?.values?.some((exception) =>
          exception.stacktrace?.frames?.some((frame) => {
            const fname = frame.filename || frame.abs_path || "";
            return fname.includes("lesson_widget/interactive/");
          })
        )
      ) {
        return null;
      }
      return event;
    },
    beforeSendSpan(span) {
      // console.log("========span====", span);
      let url = span.description;
      if (url && (/^data:/.test(url) || /base64/.test(url))) {
        span.description = "[请求了一个base64数据]";
      }
      if (span.data["http.url"]) {
        url = span.data["http.url"] as string;
        if (url && (/^data:/.test(url) || /base64/.test(url))) {
          span.data["http.url"] = "[请求了一个base64数据]";
        }
      }
      if (span.data["url"]) {
        url = span.data["url"] as string;
        if (url && (/^data:/.test(url) || /base64/.test(url))) {
          span.data["url"] = "[请求了一个base64数据]";
        }
      }

      return span;
    },

    beforeSendTransaction(transaction) {
      // console.log("========transaction====", transaction);
      const url = transaction.request?.url;
      if (url && (/^data:/.test(url) || /base64/.test(url))) {
        transaction.request!.url = "[请求了一个base64数据]";
      }
      return transaction;
    },

    beforeBreadcrumb(breadcrumb) {
      // console.log("======breadcrumb======", breadcrumb);
      if (breadcrumb.data && breadcrumb.data.url) {
        const url = breadcrumb.data.url;
        if (/^data:/.test(url) || /base64/.test(url)) {
          breadcrumb.data.url = "[请求了一个base64数据]";
        }
      }
      return breadcrumb;
    },

    // Add optional integrations for additional features
    integrations: [
      Sentry.globalHandlersIntegration(),
      Sentry.browserTracingIntegration(),
      // 降低性能分析的采样率
      Sentry.browserProfilingIntegration(),
      // 优化Session Replay配置以减少数据量
      Sentry.replayIntegration({
        maskAllInputs: true, // 遮蔽输入内容减少数据量
        maskAllText: false, // 遮蔽文本内容减少数据量
        blockAllMedia: true, // 阻止媒体文件录制
      }),
    ],
    tracePropagationTargets: [/^https:\/\/(.*)(xiaoluxue\.cn|xiaoluxue\.com)/],

    // 大幅降低采样率以减少数据量
    profileSessionSampleRate: 0.1,
    profilesSampleRate: 1.0, // 从1.0降到0.1
    tracesSampleRate: 1.0, // 从1.0降到0.1

    // Session Replay采样率 - 大幅降低以减少数据量
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 0.1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
  });

  const user = getStudentUserInfo();
  if (user) {
    const {
      userId: id,
      userName: username,
      userAccount: _,
      userPhone: __,
      userPhoto: ___,
      ...rest
    } = user;
    Sentry.setUser({
      id,
      username,
      ...rest,
    });
  }
  const appInfo = getAppInfo();
  if (appInfo) {
    Sentry.setTag("versionName", appInfo.versionName);
  }
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
