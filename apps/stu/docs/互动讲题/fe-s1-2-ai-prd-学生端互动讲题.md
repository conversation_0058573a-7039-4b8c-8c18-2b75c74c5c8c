# 前端产品需求文档 - 学生端互动讲题

## 1. 背景与目标

### 1.1 需求背景

学生在做题时经常会遇到不会做的题目，通常会先通过看解析来尝试解决问题。但目前产品内的练习题只配有文字解析，且解析质量参差不齐，存在省略关键步骤、讲得不够清楚的问题。这会导致学生即使反复阅读，也很难真正理解解题思路。过去的产品有尝试为题目配置视频解析，虽可以提升讲解的细致度，但视频天然存在制作成本高、覆盖有限，且缺乏互动，所以实际使用率和效果都不理想。

为了帮助学生更好地解决题目、掌握解题方法，我们探索了一种既能兼顾成本，又能提升学习效果的新方案。通过更清晰的分步讲解和有针对性的互动，让学生在学习过程中能够主动参与、及时获得反馈，真正解决做题时遇到的问题，提升整体学习体验。

### 1.2 项目收益

- **提升理解与参与**：通过设置分析题目条件、分析考点，然后把解题过程拆成几个简单的步骤，每一步都会有问题引导学生思考，学生可以自己尝试推理，遇到不会的地方还能得到提示。通过这种学生不再只是被动地看讲解，而是一步步参与进来，慢慢理清解题思路，最后能自己把题做出来。
- **降低成本与增强效果**：互动讲题主要通过模型 + prompt 自动生成内容，制作和维护的成本比视频低很多，更新也更灵活。分步互动的形式也更直观，学生学起来更主动，体验和效果都会比单纯看文字解析或视频更好。

### 1.3 覆盖用户

- X 年级的用户 AI 课的场景

### 1.4 方案简述

- **入口形式**：在 AI 课场景下的部分题目的解析下通过配置相应的内容，提供"逐步讲解"入口，学生点击该入口即可进入互动讲题模式。
- **分步讲解流程**：互动讲题划分为四个环节：**题目分析、考点分析、逐步讲解、思路总结**。每个环节专注不同的解题步骤，学生可以上下滑动查看内容，也能通过导航快速跳转，随时回顾前面的步骤，加深理解。
- **互动环节：**在逐步讲解的每一步，系统都会给出引导和互动问题。学生作答后，系统会立刻反馈：答对了进入下一步并显示解析，答错会有提示，比如摇动错误选项并允许重试再往下走。学生不再只是被动看讲解，而是能主动参与、边思考边学会解题方法。
- **使用体验：**整个互动讲题过程以浮层叠加在练习界面，下方弹出且可拖动，可随时回看题目。每一步都有清晰提示，内容逐步展开，互动反馈及时，帮助学生集中注意力，提升学习体验。

### 1.5 未来规划

通过互动讲题，**分阶段**实现帮助学生的目标：

- **阶段一：讲好题（当前版本）** – 专注于题目的分步讲解，引导学生分析题干和考点，理解每个解题步骤，确保学生能够通过互动讲题学会这道题。此阶段的核心目标是验证互动讲题模式对提升学生理解的效果。
- **阶段二（精准诊断）：**借助互动环节精准识别学生在每一步的知识盲点。
- **阶段三（补弱练习）：**基于诊断结果推送针对性练习材料，实现高效弥补。

## 2. 核心功能

### 2.1 核心功能

#### **核心功能点 1:** 逐步讲解入口与浮层控制

##### **核心功能点 1.1:** 入口展示与触发

###### 界面布局与核心 UI 元素

- **布局参考：** [入口示意图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Whenbui9KoCfJFx305Zck0GTnec.png)
- **布局规则：** 入口位于题目解析下方，采用按钮形式展示。仅在满足配置条件的题目中显示，通过服务端配置支持按学科、学校进行灰度控制。
- **核心 UI 元素清单：**
  - 逐步讲解按钮（主要入口）
  - 引导文案："解析没看懂？一步步学明白"
  - 按钮文字："逐步讲解"

###### 功能需求与交互原则

- **功能描述：** 在 AI 课中练习组件的题目解析页面，针对已配置互动讲题内容的题目显示"逐步讲解"入口。入口仅在满足配置条件时显示，通过服务端配置支持按学科、学校进行灰度控制。
- **交互模式：** 点击触发模式，用户点击"逐步讲解"按钮后立即响应，无需二次确认。
- **反馈原则：** 点击后立即在页面底部弹出互动讲题浮层，提供视觉过渡动画，确保用户感知到功能切换。

**优先级：** 高
**依赖关系：** 依赖于题目配置和服务端灰度控制

##### **核心功能点 1.2:** 浮层控制与拖动

###### 界面布局与核心 UI 元素

- **布局参考：** [浮层展示图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_O1oYbhbVzouVjRxOCmCcDW0xnVc.png)
- **布局规则：** 浮层从页面底部弹出，初始覆盖约 2/3 屏幕高度。支持上下拖动调整，最高可覆盖全屏，最低可拖动到 200px（确保基本内容可见）。
- **核心 UI 元素清单：**
  - 浮层容器（可拖动区域）
  - 拖动手柄（顶部热区）
  - 关闭按钮（右上角 X 图标）
  - 内容展示区域

###### 功能需求与交互原则

- **功能描述：** 提供可拖动的浮层界面，支持高度调整以适应不同学习需求。学生可以根据需要调整浮层高度，在查看讲解内容和回顾原题之间灵活切换。
- **交互模式：** 拖动调整模式，通过顶部热区进行上下拖动，支持边界限制和回弹效果。
- **反馈原则：** 拖动过程中提供实时视觉反馈，到达边界时提供触觉反馈（震动）和视觉提示。

**优先级：** 高
**依赖关系：** 无

##### 用户场景与故事 (针对此核心功能点)

###### 场景 1：学生查看解析后进入互动讲题

作为**学生**，我需要**在看不懂文字解析时进入互动讲题模式**以解决**理解困难的问题**，从而**通过分步引导掌握解题方法**。

**前端界面与交互关键步骤：**

1. **查看题目解析：** 学生完成题目作答后查看标准解析

   - **界面变化：** 显示题目答案和文字解析内容
   - **即时反馈：** 解析内容正常加载，如果配置了互动讲题则显示入口

2. **发现逐步讲解入口：** 学生在解析下方看到互动讲题入口

   - **界面变化：** 显示引导文案"解析没看懂？一步步学明白"和"逐步讲解"按钮
   - **即时反馈：** 按钮呈现可点击状态，提供视觉引导

3. **点击进入互动讲题：** 学生点击"逐步讲解"按钮

   - **界面变化：** 页面底部弹出互动讲题浮层，覆盖约 2/3 屏幕高度
   - **即时反馈：** 平滑的弹出动画，浮层内容开始加载

4. **调整浮层高度：** 学生根据需要拖动浮层调整高度

   - **界面变化：** 浮层高度实时调整，内容区域相应变化
   - **即时反馈：** 拖动过程中的视觉反馈，边界处的限制提示

**场景细节补充：**

- **前置条件：** 学生已完成题目作答并查看解析，且该题目已配置互动讲题内容
- **期望结果：** 学生成功进入互动讲题模式，可以开始分步学习解题过程
- **异常与边界情况：**
  - 网络异常导致浮层内容加载失败时，显示重试提示
  - 题目未配置互动讲题内容时，不显示入口
  - ⚠️ 浮层拖动到极限位置时需要有边界反馈和回弹效果

**优先级：** 高
**依赖关系：** 无

**Mermaid 图示 (逐步讲解入口流程):**

```mermaid
flowchart TD
    A[学生查看题目解析] --> B{是否配置互动讲题}
    B -- 是 --> C[显示逐步讲解入口]
    B -- 否 --> D[仅显示标准解析]
    C --> E[学生点击逐步讲解按钮]
    E --> F[底部弹出互动讲题浮层]
    F --> G[学生可拖动调整浮层高度]
    G --> H[开始互动讲题流程]

    style C fill:#e8f5e8
    style F fill:#f3e5f5
```

---

#### **核心功能点 2:** 互动讲题环节

##### **核心功能点 2.1:** 导航系统

###### 界面布局与核心 UI 元素

- **布局参考：** [导航示意图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RamzbMzwZoM0dLxiU7ncSRl1n0c.png)
- **布局规则：** 顶部横向导航条，展示四个主要环节。选中"逐步讲解"时展开二级导航，显示具体步骤。
- **核心 UI 元素清单：**
  - 一级导航：题目分析、考点分析、逐步讲解(x 步)、思路总结
  - 二级导航：逐步讲解的具体步骤列表
  - 进度指示器：当前环节高亮显示
  - 关闭按钮：右上角 X 图标

###### 功能需求与交互原则

- **功能描述：** 提供清晰的导航系统，支持环节间跳转和进度追踪。一级导航展示四个主要环节，二级导航在选中"逐步讲解"时展开，显示具体步骤数量和名称。
- **交互模式：** 点击跳转模式，支持前后跳转，未完成的环节显示未作答状态。
- **反馈原则：** 点击后立即跳转到对应环节，提供视觉过渡效果，当前位置始终高亮显示。

**优先级：** 高
**依赖关系：** 依赖于内容生成的步骤数量

##### **核心功能点 2.2:** 题目分析环节

###### 界面布局与核心 UI 元素

- **布局参考：** [题目分析界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RamzbMzwZoM0dLxiU7ncSRl1n0c.png)
- **布局规则：** 上下滚动布局，包含虚拟导师引导、条件信息卡片和关键信息总结。采用卡片式设计，信息层次清晰。
- **核心 UI 元素清单：**
  - 虚拟导师形象（卡通小鹿助手）
  - 引导语对话气泡
  - 条件信息卡片（有序列表）
  - 关键信息卡片
  - 下一步按钮（吸底）

###### 功能需求与交互原则

- **功能描述：** 引导学生梳理题目中的已知条件和关键信息，为后续解题奠定基础。通过结构化展示帮助学生理解题目要素。
- **交互模式：** 阅读浏览模式，支持上下滚动查看内容，通过"下一步"按钮进入下一环节。
- **反馈原则：** 内容逐步展示，引导语随机选择，提供友好的学习氛围。

**引导语选项（随机展示一个）：**

- "我们一起看看题目给了哪些条件！"
- "我们一起挖掘一下题目的线索！"

**优先级：** 高
**依赖关系：** 依赖于题目内容解析

##### **核心功能点 2.3:** 考点分析环节

###### 界面布局与核心 UI 元素

- **布局参考：** [考点分析界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_KxfJbBicjoJKnPx8qcXcoF5InKc.png)
- **布局规则：** 三段式布局，包含目标、考点和解题突破口三个主要部分。每部分都有明确的标题和转场词。
- **核心 UI 元素清单：**
  - 虚拟导师引导语
  - 目标卡片（有序列表）
  - 考点卡片（无序列表）
  - 解题突破口卡片
  - 下一步按钮（吸底）

###### 功能需求与交互原则

- **功能描述：** 帮助学生明确解题目标、理解核心考点、找到解题突破口。通过结构化分析建立解题框架。
- **交互模式：** 阅读理解模式，支持内容浏览和环节跳转。
- **反馈原则：** 内容层次清晰，逻辑递进，为学生提供完整的解题思路框架。

**引导语选项（随机展示一个）：**

- "让我们来看看这道题想考什么？"

**内容结构：**

- **Part 1 - 目标：** "首先我们需要明确目标："
- **Part 2 - 考点：** "其次我们得知道核心的考点："
- **Part 3 - 解题突破口：** "目标和考点都清楚了，我们来找找这道题的解题突破口："

**优先级：** 高
**依赖关系：** 依赖于题目分析结果

##### **核心功能点 2.4:** 逐步讲解环节

###### 界面布局与核心 UI 元素

- **布局参考：** [逐步讲解界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LsJhbHeCFoxwXexFFtKchFIcnNc.png)
- **布局规则：** 每个步骤包含引导语、"请你想一想"选择题、解析展示和"一起算一算"计算过程。采用渐进式展示，完成互动后显示后续内容。
- **核心 UI 元素清单：**
  - 步骤引导语
  - "请你想一想"模块（选择题）
  - 选项按钮（支持选择和错误反馈）
  - 解析展示区域
  - "一起算一算"计算模块
  - 数学公式渲染区域
  - 下一步按钮

###### 功能需求与交互原则

- **功能描述：** 通过分步互动引导学生逐步理解解题过程。每个步骤都包含思考、互动、解析和计算四个环节，确保学生主动参与学习过程。
- **交互模式：** 互动问答模式，学生需要回答问题才能查看解析和进入下一步。
- **反馈原则：** 答对显示正确提示和解析，答错时选项摇动并允许重新选择，提供即时反馈和鼓励。

**引导语选项（根据进度随机展示）：**

- "一步一步搞清楚！" + 对应步数（如第一步）
- "准备啦！开始" + 对应步数（如第一步）
- "已经完成一半了！"（当进度为 40%-70%时）
- "非常接近真相了！"（70%-90%时）
- "只差最后一击！"（最后一步）

**交互细节：**

- 用户回答正确后，展示正确答案、解析和"一起算一算"环节
- 用户首次回答错误时，错误选项摇动，允许二次选择
- 数学公式使用 LaTeX 格式正确渲染
- 计算过程分步展示，逻辑清晰

**优先级：** 高
**依赖关系：** 依赖于 AI 生成的互动内容

##### **核心功能点 2.5:** 思路总结环节

###### 界面布局与核心 UI 元素

- **布局参考：** [思路总结界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_D1ftbyHvPoh0dqxQIkScppqQnRd.png)
- **布局规则：** 总结式布局，包含解题思路回顾和最终答案展示。采用清晰的层次结构，便于学生理解和记忆。
- **核心 UI 元素清单：**
  - 虚拟导师总结引导
  - 解题思路回顾（分步骤）
  - 最终答案展示区域
  - 完成标识

###### 功能需求与交互原则

- **功能描述：** 帮助学生回顾整个解题过程，巩固解题思路，展示最终答案。通过系统性总结加深学生对解题方法的理解。
- **交互模式：** 阅读总结模式，支持内容回顾和完成确认。
- **反馈原则：** 提供完整的解题思路梳理，突出关键步骤和方法，给予学生成就感。

**引导语选项（随机展示一个）：**

- "我们来总结一下这道题的解题思路"
- "让我们回顾一下刚才的解题过程"

**内容结构：**

- **Part 1：** 解题思路详细回顾
- **Part 2：** 最终答案展示

**优先级：** 高
**依赖关系：** 依赖于前面环节的完成

##### 用户场景与故事 (针对此核心功能点)

###### 场景 1：学生完成完整的互动讲题流程

作为**学生**，我需要**通过互动讲题的四个环节逐步理解解题过程**以解决**复杂题目理解困难的问题**，从而**掌握系统的解题方法和思路**。

**前端界面与交互关键步骤：**

1. **进入题目分析：** 学生开始互动讲题，首先查看题目条件分析

   - **界面变化：** 显示虚拟导师引导和结构化的条件信息
   - **即时反馈：** 内容逐步展示，引导语随机出现

2. **浏览考点分析：** 学生了解题目考查的核心知识点

   - **界面变化：** 显示目标、考点和解题突破口三个部分
   - **即时反馈：** 内容层次清晰，逻辑递进展示

3. **参与逐步讲解：** 学生在每个步骤中回答问题并查看解析

   - **界面变化：** 显示选择题、解析内容和计算过程
   - **即时反馈：** 答对显示解析，答错选项摇动并允许重试

4. **查看思路总结：** 学生回顾整个解题过程和最终答案

   - **界面变化：** 显示完整的解题思路梳理和答案
   - **即时反馈：** 提供成就感和学习完成的确认

**场景细节补充：**

- **前置条件：** 学生已进入互动讲题浮层，题目内容已配置完整的互动讲解
- **期望结果：** 学生完整体验互动讲题流程，理解解题思路，掌握解题方法
- **异常与边界情况：**
  - 网络异常导致内容加载失败时，提供重试机制
  - 学生可以随时通过导航跳转到任意环节
  - ⚠️ 数学公式渲染失败时需要提供降级显示方案

**优先级：** 高
**依赖关系：** 依赖于完整的内容配置

###### 场景 2：学生中途退出后继续学习

作为**学生**，我需要**在中途退出后能够继续之前的学习进度**以解决**学习连续性的问题**，从而**不重复已学内容，提高学习效率**。

**前端界面与交互关键步骤：**

1. **检测学习进度：** 系统检测到学生之前有未完成的互动讲题

   - **界面变化：** 显示进度恢复提示和选择选项
   - **即时反馈：** 提供"继续学习"和"重新开始"两个选项

2. **恢复学习进度：** 学生选择继续之前的学习进度

   - **界面变化：** 直接跳转到上次学习的位置，已完成部分标记为完成状态
   - **即时反馈：** 导航显示当前进度，已完成环节呈现完成状态

**场景细节补充：**

- **前置条件：** 学生之前已开始但未完成互动讲题流程
- **期望结果：** 学生能够从上次位置继续学习或选择重新开始
- **异常与边界情况：** ⚠️ 进度数据可能因为缓存清理而丢失，需要提供友好的重新开始引导

**优先级：** 中
**依赖关系：** 依赖于进度保存机制

**Mermaid 图示 (核心功能点主要流程):**

```mermaid
flowchart TD
    A[进入互动讲题] --> B[题目分析]
    B --> C[考点分析]
    C --> D[逐步讲解-第1步]
    D --> E[请你想一想]
    E --> F{回答是否正确}
    F -- 正确 --> G[显示解析和计算]
    F -- 错误 --> H[错误反馈+重试]
    H --> E
    G --> I{是否最后一步}
    I -- 否 --> J[下一步]
    J --> E
    I -- 是 --> K[思路总结]
    K --> L[完成互动讲题]

    style E fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#ffebee
    style K fill:#f3e5f5
```

---

#### **核心功能点 3（这里是后端实现，前端无需关注）:** 内容生产系统

##### **核心功能点 3.1:** 测评阶段

###### 界面布局与核心 UI 元素

- **布局参考：** [测评流程图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_W8e0w0AzmhMQt5bWvGhch1FMnRb.png)
- **布局规则：** 后台管理界面，采用流程式布局展示测评各个环节。包含范围选择、题目获取、内容生成、教研测评和 prompt 优化的完整流程。
- **核心 UI 元素清单：**

  - 测评范围配置面板
  - 题目批量获取界面
  - 内容生成状态显示
  -

  教研测评工具

  - Prompt 调优界面

###### 功能需求与交互原则

- **功能描述：** 支持批量获取指定范围的题目（数学、物理、化学科目，每学科 20 节课、每节课 5 题），调用 Gemini 2.5 pro 模型配合互动讲题 prompt 生成待评测内容。教研人员对生成内容进行测评并返回结果，根据测评结果调优 prompt。
- **交互模式：** 批量处理模式，支持范围选择、批量生成、批量评测的工作流程。
- **反馈原则：** 提供详细的生成进度、评测结果统计和 prompt 优化建议。

**测评范围：**

- **学科：** 数学、物理、化学科目
- **场景：** AI 课中练习组件配置的题目
- **测评数量：** 每学科 20 节课、每节课 5 题

**评测方式：**

- 批量获取选定的题目（含题目 ID 和题目内容-题干、选项、答案、解析）
- 调用模型（Gemini 2.5 pro）+ [互动讲题 prompt](https://wcng60ba718p.feishu.cn/wiki/EeYZw69qni51iVkI836czOp8nJh) 生成待评测内容
- 教研测评并返回测评结果
- 根据测评结果调优 prompt
- 根据调优后的 prompt 生成内容后再次评测

**优先级：** 高
**依赖关系：** 无

##### **核心功能点 3.2:** 生产阶段

###### 界面布局与核心 UI 元素

- **布局参考：** [生产流程图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_ASp9wx5GchcJSGbSNRSc602HnSe.png)
- **布局规则：** 生产管理界面，采用线性流程布局。从确定生产范围到最终上架的六个步骤，每个步骤都有明确的状态指示和操作入口。
- **核心 UI 元素清单：**
  - 生产范围配置
  - 题目获取状态
  - 内容生成进度
  - 教研审核工具
  - 上架发布按钮

###### 功能需求与交互原则

- **功能描述：** 基于最终优化的 prompt，批量生成互动讲题内容。教研人员审核生成内容并进行必要修改，审核通过后由研发人员上架讲题内容。
- **交互模式：** 审核发布模式，支持内容预览、在线编辑、审核流程和发布管理。
- **反馈原则：** 提供清晰的审核状态、修改记录和发布结果反馈。

**生产范围：**

- **学科：** 数学、物理、化学科目
- **场景：** AI 课中练习组件配置的题目
- **生成数量：** 随课程生产节奏生产配套的互动讲题内容

**生产流程：**

- 批量获取选定的题目（含题目 ID 和题目内容-题干、选项、答案、解析）
- 调用模型（Gemini 2.5 pro）+ 最终[互动讲题 prompt](https://wcng60ba718p.feishu.cn/wiki/EeYZw69qni51iVkI836czOp8nJh) 生成待审核内容
- 教研审核并修改内容
- 由研发上架讲题内容

**优先级：** 高
**依赖关系：** 依赖于测评阶段的 prompt 优化结果

##### 用户场景与故事 (针对此核心功能点)

###### 场景 1：教研人员进行内容测评和优化

作为**教研人员**，我需要**对 AI 生成的互动讲题内容进行质量评测**以解决**内容质量不稳定的问题**，从而**确保学生获得高质量的学习内容**。

**前端界面与交互关键步骤：**

1. **选择测评范围：** 教研人员在后台选择需要测评的学科和题目范围

   - **界面变化：** 显示学科选择器和题目数量配置面板
   - **即时反馈：** 实时显示选择的题目总数和预估生成时间

2. **批量生成内容：** 系统调用 AI 模型批量生成互动讲题内容

   - **界面变化：** 显示生成进度条和当前处理的题目信息
   - **即时反馈：** 实时更新生成进度和成功/失败统计

3. **评测生成内容：** 教研人员逐一评测生成的内容质量

   - **界面变化：** 显示题目内容、生成的讲解内容和评测表单
   - **即时反馈：** 提供评分、评语录入和问题标记功能

4. **调优 prompt：** 根据评测结果调整和优化 prompt 参数
   - **界面变化：** 显示 prompt 编辑器和优化建议
   - **即时反馈：** 提供 prompt 版本管理和效果对比

**场景细节补充：**

- **前置条件：** 教研人员具有内容评测权限，AI 模型和 prompt 已配置
- **期望结果：** 获得质量稳定的互动讲题内容生成能力
- **异常与边界情况：** AI 生成失败时提供重试机制，评测数据需要持久化保存

**优先级：** 高
**依赖关系：** 无

**Mermaid 图示 (内容生产流程):**

```mermaid
flowchart TD
    A[确定测评范围] --> B[获取对应AI课题目]
    B --> C[题目ID + 内容]
    C --> D[模型 + prompt生成互动讲题内容]
    D --> E[教研测评生成内容]
    E --> F{测评结果}
    F -- 需要优化 --> G[调优prompt]
    G --> D
    F -- 质量合格 --> H[输出最终prompt]
    H --> I[确定生产范围]
    I --> J[批量生成内容]
    J --> K[教研审核生成内容]
    K --> L[上架题目]

    style D fill:#e3f2fd
    style E fill:#fff3e0
    style H fill:#e8f5e8
```

---

### 2.2 辅助功能

- **进度保存功能:** 自动保存学生的学习进度，支持中途退出后继续学习
  - **功能详述与前端呈现:** 在学生完成每个步骤后自动保存进度到本地存储，重新进入时检测并提供继续学习选项。界面显示当前进度和已完成步骤的标记。
- **内容缓存功能:** 预加载互动讲题内容，提升用户体验

  - **功能详述与前端呈现:** 在用户点击逐步讲解入口时预加载所有环节内容，减少后续步骤的加载等待时间。显示加载状态和进度指示。

- **错误重试功能:** 网络异常时提供重试机制
  - **功能详述与前端呈现:** 检测网络状态和接口响应，异常时显示友好的错误提示和重试按钮。保持用户当前进度不丢失。

### 2.3 非本期功能

- **个性化推荐:** 基于学生答题情况推荐相关题目，技术复杂度较高，建议 V2.0 再考虑
- **语音讲解:** 为互动讲题添加语音播报功能，需要语音合成技术支持，建议 V2.0 再考虑
- **学习报告:** 生成学生的互动讲题学习报告和分析，需要完善的数据分析能力，建议 V2.0 再考虑

## 3. 业务流程图

```mermaid
graph TD
    A[学生进入AI课练习] --> B[完成题目作答]
    B --> C[查看题目解析]
    C --> D{是否配置互动讲题}
    D -- 是 --> E[显示逐步讲解入口]
    D -- 否 --> F[仅显示标准解析]
    E --> G[学生点击逐步讲解]
    G --> H[弹出互动讲题浮层]
    H --> I[题目分析环节]
    I --> J[考点分析环节]
    J --> K[逐步讲解环节]
    K --> L{是否最后一步}
    L -- 否 --> M[下一步讲解]
    M --> K
    L -- 是 --> N[思路总结环节]
    N --> O[完成互动讲题]
    O --> P[返回练习界面]

    style H fill:#f3e5f5
    style K fill:#fff3e0
    style O fill:#e8f5e8
```

## 4. 验收标准

### 4.1 功能验收

- **逐步讲解入口：**

  - **用户场景：** 当学生在 AI 课练习中完成题目作答并查看解析时
  - **界面与交互：**
    - 已配置互动讲题的题目在解析下方显示完整引导文案"解析没看懂？一步步学明白"和"逐步讲解"入口
    - 点击入口后页面底部弹出互动讲题浮层，覆盖约 2/3 屏幕高度
    - 浮层支持拖动调整高度，最高可覆盖全屏，最低可拖动到 200px
  - **期望结果：** 学生能够顺利进入互动讲题模式，浮层展示正常且交互流畅

- **互动讲题环节：**

  - **用户场景：** 当学生在互动讲题浮层中进行学习时
  - **界面与交互：**
    - 导航栏正确显示四个环节和当前进度，支持"逐步讲解(x 步)"的二级导航展开
    - 每个环节的内容正确加载和显示，引导语随机展示
    - "请你想一想"模块的选择题交互正常，答对显示解析，答错选项摇动并提供重试
    - 数学公式正确渲染，计算过程清晰展示
  - **期望结果：** 学生能够完整体验互动讲题流程，理解解题思路

- **内容生产系统：**
  - **用户场景：** 当教研人员进行内容测评和生产时
  - **界面与交互：**
    - 测评界面能够正确显示生成的内容和评测工具
    - 生产界面支持批量处理和审核流程
    - 所有操作都有明确的状态反馈和结果提示
  - **期望结果：** 教研人员能够高效完成内容质量控制和发布流程

## 5. 待澄清问题清单 🤔🤔🤔

- 🤔 **进度保存的持久化策略：** 学生中途退出后的进度保存是使用本地存储还是服务端存储？保存多长时间？是否需要跨设备同步？
  - 答：基于接口上报进度
- 🤔 **错误重试的次数限制：** 当学生在"请你想一想"环节答错时，是否有重试次数限制？超过限制后如何处理？
  - 答：可无限重试
- 🤔 **内容加载失败的降级策略：** 当互动讲题内容加载失败时，是否回退到标准解析？还是显示错误页面？
- 🤔 **虚拟导师引导语的随机策略：** 引导语的随机展示是基于什么规则？是否需要避免短时间内重复？
- 🤔 **数学公式渲染的兼容性：** 不同设备和浏览器对数学公式的渲染支持情况如何？是否需要降级方案？
- 🤔 **教研审核的具体流程：** 教研人员审核内容时的具体操作流程和权限控制需要进一步明确
- 🤔 **灰度发布的控制粒度：** 按学科、学校进行灰度的具体控制策略和切换机制需要详细设计
- 🤔 **浮层拖动的边界处理：** 拖动到极限位置时的具体反馈机制和回弹效果需要明确参数

## 6. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始 PRD 的详细条目)               | 对应优化后需求点/章节 (在本 PRD 中)       | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | UI/UX 明确性 | 备注                                                     |
| ------------------------------------------------ | ----------------------------------------- | ------ | ------ | ------ | -------- | -------- | ------------ | -------------------------------------------------------- |
| 逐步讲解入口：在解析下方提供入口，点击后弹出浮层 | 章节 2.1-核心功能点 1 / 章节 4.1 功能验收 | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 已完整覆盖入口展示、触发和浮层控制，明确了最小高度 200px |
| 讲题环节：四个环节的互动讲题内容展示与交互       | 章节 2.1-核心功能点 2 / 章节 4.1 功能验收 | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 详细描述了导航、各环节内容和交互方式，补充了二级导航机制 |
| 题目分析：引导语+条件信息展示                    | 章节 2.1-核心功能点 2.2                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 包含虚拟导师引导和结构化条件展示，补充了完整引导语选项   |
| 考点分析：目标+考点+解题突破口                   | 章节 2.1-核心功能点 2.3                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 三段式布局和内容结构已明确，补充了转场词                 |
| 逐步讲解：分步骤的互动问答+解析+计算             | 章节 2.1-核心功能点 2.4                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 详细描述了互动问答机制和反馈原则，补充了完整引导语体系   |
| 思路总结：解题思路回顾+最终答案                  | 章节 2.1-核心功能点 2.5                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 总结式布局和内容展示已明确，补充了引导语选项             |
| 冷启动内容生产：测评+批量生产                    | 章节 2.1-核心功能点 3                     | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 详细描述了测评和生产两个阶段的完整流程                   |
| 浮层拖动：支持上下拖动调整高度                   | 章节 2.1-核心功能点 1.2                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 明确了最小高度 200px，补充了边界反馈机制                 |
| 导航跳转：支持环节间跳转                         | 章节 2.1-核心功能点 2.1                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 一级二级导航机制已明确，补充了未作答状态显示             |
| 错误反馈：答错时摇动+重试                        | 章节 2.1-核心功能点 2.4                   | ✅     | ✅     | ✅     | ⚠️       | ✅       | ✅           | 摇动反馈机制已明确，重试次数限制需要澄清                 |
| 引导语随机展示：各环节多个引导语选项             | 章节 2.1-核心功能点 2.2-2.5               | ✅     | ✅     | ✅     | ⚠️       | ✅       | ✅           | 补充了完整的引导语选项，随机策略需要澄清                 |
| 入口文案：解析没看懂？一步步学明白               | 章节 2.1-核心功能点 1.1                   | ✅     | ✅     | ✅     | ✅       | ✅       | ✅           | 已补充完整的入口文案                                     |
| 数据埋点需求：效果追踪和数据埋点                 | 章节 7.1 数据需求                         | ✅     | ✅     | ✅     | ⚠️       | ✅       | ⚠️           | 已补充数据需求章节，具体埋点方案需要进一步细化           |

- **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
- **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
- **一致性：** 优化后需求之间是否有冲突或重复。
- **可验证性：** 优化后需求是否有明确的验收标准（对应 4. 验收标准）。
- **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
- **UI/UX 明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅ 表示通过；❌ 表示未通过；⚠️ 表示描述正确，但 UI/UX 细节、验收标准或图示等仍需与 PM 进一步沟通完善；N/A 表示不适用。

## 7. 数据需求

### 7.1 效果追踪

描述计划监控数据效果的方式及所需数据字段：

- **学习完成率：** 统计学生完成完整互动讲题流程的比例
- **环节停留时间：** 记录学生在各个环节的学习时长
- **互动正确率：** 统计"请你想一想"环节的答题正确率
- **重试次数：** 记录学生在错误后的重试行为
- **浮层使用行为：** 统计浮层拖动、关闭等操作频次

### 7.2 数据埋点

| **页面/模块** | **动作**         | **埋点名称**               | **参数**                                 | **备注**         |
| ------------- | ---------------- | -------------------------- | ---------------------------------------- | ---------------- |
| 解析页面      | 点击逐步讲解入口 | interactive_tutorial_enter | question_id, subject, grade              | 记录入口点击行为 |
| 互动讲题浮层  | 浮层拖动         | tutorial_layer_drag        | drag_height, direction                   | 记录浮层使用行为 |
| 题目分析      | 进入环节         | tutorial_section_enter     | section_name, question_id                | 记录环节访问     |
| 逐步讲解      | 回答问题         | tutorial_question_answer   | step_id, answer, is_correct, retry_count | 记录互动答题     |
| 思路总结      | 完成学习         | tutorial_complete          | question_id, total_time, completion_rate | 记录学习完成     |

## 8. 附录

### 8.1 原型图/设计稿链接

- [Figma 设计稿](https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=4498-10550)

### 8.2 术语表

- **互动讲题:** 通过"题目分步 → 步步互动"的方式，从题目分析、考点分析开始，逐步推进讲解，学生在每一步通过互动获取思路、参与推导，直到掌握解法
- **虚拟导师:** 以卡通形象呈现的 AI 助手，通过对话气泡为学生提供引导和鼓励
- **逐步讲解:** 将复杂题目的解题过程拆分为多个简单步骤，每步都包含引导、互动、解析和计算过程

### 8.3 参考资料

- [原始需求文档：PRD - 学生端 V1.0 - 互动讲题.md]
- [关联需求：PRD - AI 课中 - 练习组件 1.0](https://wcng60ba718p.feishu.cn/wiki/Hl9wwh4aDiUpEhkilL8c9bWCntf)
- [互动讲题 prompt 文档](https://wcng60ba718p.feishu.cn/wiki/EeYZw69qni51iVkI836czOp8nJh)
- [互动讲题冷启动记录表格](https://wcng60ba718p.feishu.cn/wiki/Z8y8wGIDmiii5kk0qymcJIJknZf?table=tblqNCHKOZwrxC90&view=vew39QWDWk)
