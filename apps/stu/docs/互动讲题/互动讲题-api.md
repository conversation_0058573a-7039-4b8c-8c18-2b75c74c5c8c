---
title: study-api
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# study-api

Base URLs:

# Authentication

# 练习

## POST 上报互动解题记录

POST /api/v1/study_session/ai_explantion_record/report

> Body 请求参数

```json
{
  "questionId": "string",
  "studySessionId": 0,
  "record": {
    "current_progress": 0,
    "stepByStepGuide": [
      {
        "isAnswer": true
      }
    ]
  }
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» questionId|body|string| 是 | 题目id|none|
|» studySessionId|body|integer| 是 | 课中会话id|none|
|» record|body|object| 是 ||none|
|»» current_progress|body|integer| 是 ||当前tab|
|»» stepByStepGuide|body|[object]| 是 ||none|
|»»» isAnswer|body|boolean| 否 ||是否作答|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

