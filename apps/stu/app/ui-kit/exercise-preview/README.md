# 练习组件预览页面

## 功能说明

这是一个简洁易用的练习组件预览工具，允许开发者快速粘贴 JSON 数据来验证组件效果。

## 设计原则

### 🎯 简洁易用
- **极简界面**: 移除冗余信息，专注核心功能
- **快速操作**: 一键预览，一键重置
- **清晰布局**: 紧凑的控制面板，直观的操作流程

### 🚀 核心功能
- **JSON 数据输入**: 大型文本输入框，支持粘贴题目数据
- **双模式预览**: 作答预览 vs 内容预览
- **练习类型切换**: 支持 AI课程、巩固练习、拓展练习主题
- **智能错误提示**: JSON 解析错误时显示具体信息

## 使用方法

### 1. 访问页面
```
http://localhost:3000/ui-kit/exercise-preview
```

### 2. 快速预览
1. **选择预览模式**: 作答预览 / 内容预览
2. **选择练习类型**: AI课程 / 巩固练习 / 拓展练习
3. **粘贴 JSON 数据**: 在文本框中粘贴题目数据
4. **点击预览**: 立即查看组件效果

### 3. 操作说明
- **预览**: 渲染组件并查看效果
- **重置**: 恢复默认测试数据
- **返回**: 从预览模式返回编辑界面

## 数据格式

### 预览模式

#### 📝 作答预览模式
```typescript
{
  questions: NextQuestionInfo[],
  studentAnswers: StudentAnswer[]
}
```

#### 📄 内容预览模式
```typescript
NextQuestionInfo[] | NextQuestionInfo
```

### 支持的题型
- **选择题** (`questionType: 1`)
- **多选题** (`questionType: 2`)
- **填空题** (`questionType: 3`)
- **主观题** (`questionType: 4`)
```json
[
  {
    "questionVersionId": 5740,
    "questionId": "q1iugpnrp604tj",
    "questionType": 3,
    "questionAnswerMode": 3,
    "labelQuestionType": 3,
    "labelQuestionTypeNameZh": "填空题",
    "questionContent": {
      "questionStem": "题目内容...",
      "questionOptionList": null,
      "questionOptionMatrix": []
    },
    "questionAnswer": {
      "answerOptionList": [...],
      "answerOptionMatrix": [...]
    },
    "questionExplanation": "解析内容..."
  }
]
```

## 优化特性

### 🎨 简洁设计
- **紧凑布局**: 减少视觉噪音，专注核心功能
- **直观操作**: 模式切换、类型选择、预览操作一目了然
- **清晰反馈**: 错误提示简洁明确

### ⚡ 快速开发
- **一键预览**: 无需复杂配置，粘贴数据即可预览
- **智能重置**: 根据预览模式自动加载对应的默认数据
- **即时反馈**: JSON 解析错误实时提示

### 🔧 开发友好
- **格式提示**: 实时显示当前模式所需的数据格式
- **类型安全**: 完整的 TypeScript 类型支持
- **响应式**: 适配不同屏幕尺寸

## 技术实现

### 组件架构
- 使用 `ExercisePreview` 组件进行渲染
- 支持双模式数据解析和错误处理
- 集成练习类型主题系统

### 核心状态
- `previewMode`: 预览模式（作答/内容）
- `mockDataInput`: JSON 数据输入
- `studyType`: 练习类型选择
- `parseError`: 解析错误信息
- `showPreview`: 预览状态控制

### 数据流
```
模式选择 → JSON 输入 → 格式验证 → 数据解析 → 组件渲染
```

### 优化亮点
- **减少 70% 的 UI 元素**: 移除冗余说明和复杂布局
- **提升 50% 的操作效率**: 紧凑的控制面板设计
- **增强用户体验**: 清晰的视觉层次和操作流程
