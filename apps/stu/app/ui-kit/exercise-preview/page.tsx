"use client";

import IframePreview from "@repo/core/components/iframe-preview";
import { StudyType } from "@repo/core/enums";
import { StudyTypeThemeProvider } from "@repo/core/exercise";
import { NextQuestionInfo, StudentAnswer } from "@repo/core/exercise/model";
import { But<PERSON> } from "@repo/ui/components/press-button";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useMemo, useState } from "react";
import { DEFAULT_ANSWERED_DATA, DEFAULT_CONTENT_DATA } from "./data";

// 预览模式枚举
enum PreviewMode {
  ANSWERED = "answered", // 作答后预览（包含学生答题结果）
  CONTENT_ONLY = "content-only", // 纯内容预览（只看题目内容渲染）
}

// 内部组件，使用 useSearchParams
function ExercisePreviewContent() {
  const searchParams = useSearchParams();
  const questionId = searchParams.get("questionId");
  const isProd = searchParams.get("isProd") === "true";
  const isTestUrl = window.location.href.includes(".test");
  const isDevUrl = window.location.href.includes(".dev");

  const hostMap = {
    development: "https://question-api.dev.xiaoluxue.cn",
    production: "https://question-api.xiaoluxue.cn",
    test: "https://question-api.test.xiaoluxue.cn",
  };
  let prefix = hostMap.development;
  if (isProd || (!isTestUrl && !isDevUrl)) {
    prefix = hostMap.production;
  } else if (isTestUrl) {
    prefix = hostMap.test;
  } else if (isDevUrl) {
    prefix = hostMap.development;
  }

  const [studyType, setStudyType] = useState<StudyType>(
    StudyType.EXPAND_EXERCISE
  );
  const [parseError, setParseError] = useState<string>("");
  const [showPreview, setShowPreview] = useState(false);
  const [previewMode, setPreviewMode] = useState<PreviewMode>(
    PreviewMode.CONTENT_ONLY
  );
  const [isLoading, setIsLoading] = useState(false);

  // 根据预览模式获取默认数据
  const getDefaultData = (mode: PreviewMode) => {
    return mode === PreviewMode.ANSWERED
      ? DEFAULT_ANSWERED_DATA
      : DEFAULT_CONTENT_DATA;
  };

  const [mockDataInput, setMockDataInput] = useState(
    getDefaultData(PreviewMode.CONTENT_ONLY)
  );

  // 生成假的用户作答数据
  const generateMockStudentAnswer = (questionData: any): any => {
    const questionId = questionData.questionId;

    return {
      questionId,
      answerContents: [
        {
          index: 0,
          questionId,
          type: 1,
          content: "",
        },
      ],
      answerType: 1,
      answerDuration: 0, // 10-70秒随机答题时间
      answerResult: 1, // 随机答题结果 1-4
      evaluationType: 1,
      answerStats: null,
    };
  };

  // 获取接口数据的函数
  const fetchQuestionData = async (qId: string) => {
    setIsLoading(true);
    setParseError("");

    try {
      const response = await fetch(
        `${prefix}/api/internal/v1/tool/get/aiexplanation/from/multidimensionatable?questionId=${qId}`,
        {
          method: "GET",
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.data) {
        const questionData = Array.isArray(result.data)
          ? result.data
          : [result.data];

        // 生成假的用户作答数据
        const studentAnswers = questionData.map(generateMockStudentAnswer);

        // 构造作答后预览的数据格式
        const answeredData = {
          studySessionId: Math.floor(Math.random() * 1000) + 1,
          studyType: studyType,
          questions: questionData,
          studentAnswers: studentAnswers,
        };

        const dataString = JSON.stringify(answeredData);
        setMockDataInput(dataString);
        setPreviewMode(PreviewMode.ANSWERED); // 设置为作答后预览模式
        setShowPreview(true);
      } else {
        throw new Error("接口返回数据格式错误");
      }
    } catch (error) {
      setParseError(
        `获取数据失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  // 监听 questionId 变化
  useEffect(() => {
    if (questionId) {
      fetchQuestionData(questionId);
    }
  }, [questionId]);

  // 解析 mock 数据
  const questionList = useMemo(() => {
    if (!showPreview) return [];

    try {
      const parsedData = JSON.parse(mockDataInput);
      setParseError("");

      if (previewMode === PreviewMode.ANSWERED) {
        // 作答后预览模式：期望格式 { questions: NextQuestionInfo[], studentAnswers: StudentAnswer[] }
        const answeredData = parsedData as {
          questions: NextQuestionInfo[];
          studentAnswers: StudentAnswer[];
        };
        return answeredData.questions.map((question, index) => ({
          questionInfo: question,
          studentAnswer: answeredData.studentAnswers[index] as
            | StudentAnswer
            | undefined,
        }));
      } else {
        // 纯内容预览模式：期望格式 NextQuestionInfo[] 数组
        const contentData = Array.isArray(parsedData)
          ? parsedData
          : [parsedData];
        return contentData.map((question: NextQuestionInfo) => ({
          questionInfo: question,
          studentAnswer: undefined, // 纯内容预览不包含学生答案
        }));
      }
    } catch (error) {
      setParseError(
        `JSON 解析错误: ${error instanceof Error ? error.message : "未知错误"}`
      );
      return [];
    }
  }, [mockDataInput, showPreview, previewMode]);

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleBack = () => {
    setShowPreview(false);
  };

  const handleReset = () => {
    setMockDataInput(getDefaultData(previewMode));
    setParseError("");
    setShowPreview(false);
  };

  // 处理预览模式切换
  const handlePreviewModeChange = (mode: PreviewMode) => {
    setPreviewMode(mode);
    setMockDataInput(getDefaultData(mode));
    setParseError("");
    setShowPreview(false);
  };

  // 如果有 questionId，直接显示预览或加载状态，不显示初始页面
  if (questionId) {
    if (showPreview && questionList.length > 0) {
      return (
        <IframePreview
          src="/ui-kit/exercise-preview/iframe"
          width={1000}
          height={600}
          title="练习组件预览"
          data={{
            questionList,
            studyType,
            previewMode,
          }}
          onClose={handleBack}
        />
      );
    }

    // 显示加载状态或错误状态
    return (
      <StudyTypeThemeProvider studyType={studyType}>
        <div className="exercise-preview-page min-h-screen bg-gray-50">
          <div className="container mx-auto max-w-4xl p-4">
            {/* 简洁标题 */}
            <div className="page-header mb-6 text-center">
              <h1 className="mb-2 text-2xl font-semibold text-gray-900">
                练习组件预览
              </h1>
              <p className="text-sm text-gray-600">
                正在加载题目: {questionId}
              </p>
              <div className="question-id-info mt-2 text-xs text-blue-600">
                URL 参数检测到 questionId: {questionId}
              </div>
            </div>

            {/* Loading 状态 */}
            {isLoading && (
              <div className="loading-state mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="flex items-center justify-center gap-3">
                  <div className="loading-spinner h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                  <span className="text-sm text-blue-700">
                    正在获取题目数据...
                  </span>
                </div>
              </div>
            )}

            {/* 错误提示 */}
            {parseError && (
              <div className="error-alert mb-4 rounded-lg border border-red-200 bg-red-50 p-4">
                <div className="flex items-start gap-2">
                  <span className="text-red-500">⚠️</span>
                  <div>
                    <div className="text-sm font-medium text-red-800">
                      获取数据失败
                    </div>
                    <div className="mt-1 text-sm text-red-600">
                      {parseError}
                    </div>
                    <button
                      onClick={() => fetchQuestionData(questionId)}
                      className="mt-2 rounded bg-red-100 px-3 py-1 text-sm text-red-700 hover:bg-red-200"
                    >
                      重试
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </StudyTypeThemeProvider>
    );
  }

  // 没有 questionId 时显示正常的预览界面
  if (showPreview && questionList.length > 0) {
    return (
      <IframePreview
        src="/ui-kit/exercise-preview/iframe"
        width={1000}
        height={600}
        title="练习组件预览"
        data={{
          questionList,
          studyType,
          previewMode,
        }}
        onClose={handleBack}
      />
    );
  }

  return (
    <StudyTypeThemeProvider studyType={studyType}>
      <div className="exercise-preview-page min-h-screen bg-gray-50">
        <div className="container mx-auto max-w-4xl p-4">
          {/* 简洁标题 */}
          <div className="page-header mb-6 text-center">
            <h1 className="mb-2 text-2xl font-semibold text-gray-900">
              练习组件预览
            </h1>
            <p className="text-sm text-gray-600">
              粘贴 JSON 数据，快速预览组件效果
            </p>
          </div>

          {/* 核心控制区 */}
          <div className="control-panel mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
            <div className="flex flex-wrap items-center gap-4">
              {/* 预览模式切换 */}
              <div className="mode-toggle flex rounded-lg bg-gray-100 p-1">
                <button
                  className={`rounded-md px-3 py-1.5 text-sm font-medium transition-all ${
                    previewMode === PreviewMode.CONTENT_ONLY
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                  onClick={() =>
                    handlePreviewModeChange(PreviewMode.CONTENT_ONLY)
                  }
                >
                  📄 内容预览
                </button>
                <button
                  className={`rounded-md px-3 py-1.5 text-sm font-medium transition-all ${
                    previewMode === PreviewMode.ANSWERED
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                  onClick={() => handlePreviewModeChange(PreviewMode.ANSWERED)}
                >
                  📝 作答预览
                </button>
              </div>

              {/* 练习类型选择 */}
              <select
                className="rounded-lg border border-gray-300 px-3 py-1.5 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={studyType}
                onChange={(e) =>
                  setStudyType(Number(e.target.value) as StudyType)
                }
              >
                <option value={StudyType.AI_COURSE}>🤖 AI课程</option>
                <option value={StudyType.REINFORCEMENT_EXERCISE}>
                  💪 巩固练习
                </option>
                <option value={StudyType.EXPAND_EXERCISE}>🚀 拓展练习</option>
              </select>

              {/* 操作按钮 */}
              <div className="action-buttons ml-auto flex gap-2">
                <Button
                  size="sm"
                  onClick={handlePreview}
                  disabled={!mockDataInput.trim()}
                  className="px-4 py-1.5 text-sm"
                >
                  预览
                </Button>
                <Button
                  color="gray"
                  size="sm"
                  onClick={handleReset}
                  className="px-4 py-1.5 text-sm"
                >
                  重置
                </Button>
              </div>
            </div>

            {/* 数据格式提示 */}
            <div className="format-hint mt-3 rounded bg-gray-50 p-2 text-xs text-gray-600">
              {previewMode === PreviewMode.ANSWERED
                ? "格式：{ questions: NextQuestionInfo[], studentAnswers: StudentAnswer[] }"
                : "格式：NextQuestionInfo[] 或单个 NextQuestionInfo"}
              <div className="mt-1">
                说明：{" "}
                {previewMode === PreviewMode.ANSWERED &&
                  "StudentAnswer 是学生答题结果的结构；"}
                NextQuestionInfo 是题库中单题的结构；
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {parseError && (
            <div className="error-alert mb-4 rounded-lg border border-red-200 bg-red-50 p-3">
              <div className="flex items-start gap-2">
                <span className="text-red-500">⚠️</span>
                <div>
                  <div className="text-sm font-medium text-red-800">
                    JSON 解析错误
                  </div>
                  <div className="mt-1 text-sm text-red-600">{parseError}</div>
                </div>
              </div>
            </div>
          )}

          {/* 数据输入区 */}
          <div className="data-input rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="input-header border-b border-gray-200 px-4 py-3">
              <div className="text-sm font-medium text-gray-800">JSON 数据</div>
            </div>
            <div className="input-body p-4">
              <textarea
                className="json-textarea h-80 w-full resize-none rounded-lg border border-gray-300 bg-gray-50 p-3 font-mono text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={mockDataInput}
                onChange={(e) => setMockDataInput(e.target.value)}
                placeholder="粘贴题目 JSON 数据..."
              />
            </div>
          </div>
        </div>
      </div>
    </StudyTypeThemeProvider>
  );
}

// 主导出组件，用 Suspense 包裹
export default function ExerciseMockTestPage() {
  return (
    <Suspense
      fallback={
        <div className="exercise-preview-loading min-h-screen bg-gray-50">
          <div className="container mx-auto max-w-4xl p-4">
            <div className="page-header mb-6 text-center">
              <h1 className="mb-2 text-2xl font-semibold text-gray-900">
                练习组件预览
              </h1>
              <p className="text-sm text-gray-600">正在加载页面...</p>
            </div>
            <div className="loading-state mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
              <div className="flex items-center justify-center gap-3">
                <div className="loading-spinner h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                <span className="text-sm text-blue-700">正在初始化...</span>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <ExercisePreviewContent />
    </Suspense>
  );
}
