/**
 * 评论模块 - Zod Schema 定义（后端数据校验）
 */

import { z } from "zod";

// ==================== API 响应数据 Schema ====================

/**
 * 文字类型引用坐标信息 Schema
 */
export const ApiTextReferenceCoordinateSchema = z.object({
  /** 行ID */
  lineId: z.string(),
  /** 纹理ID */
  textureId: z.string(),
  /** 开始位置 */
  start: z.string(),
  /** 结束位置 */
  end: z.string(),
  /** 类型：文字 */
  type: z.literal("text"),
});

/**
 * 图片类型引用坐标信息 Schema
 */
export const ApiPicReferenceCoordinateSchema = z.object({
  /** 行ID */
  lineId: z.string(),
  /** 类型：图片 */
  type: z.literal("pic"),
});

/**
 * 引用坐标信息 Schema（支持文字和图片两种类型）
 */
export const ApiReferenceCoordinateSchema = z.union([
  ApiTextReferenceCoordinateSchema,
  ApiPicReferenceCoordinateSchema,
]);

/**
 * 引用位置信息 Schema
 * 根据最新API文档，这是一个包含data数组的对象
 */
export const ApiReferencePositionSchema = z.object({
  /** 定位数据数组 */
  data: z.array(ApiReferenceCoordinateSchema),
});

/**
 * 评论信息 API 响应 Schema
 */
export const ApiCommentSchema = z.object({
  /** 评论ID */
  commentId: z.number().min(1),
  /** 根评论ID */
  rootCommentId: z.number(),
  /** 用户ID */
  userId: z.number().min(1),
  /** 对象ID */
  ObjID: z.number(),
  /** 对象类型 */
  objType: z.number(),
  /** 引用ID */
  referenceId: z.number(),
  /** 班级ID */
  classId: z.number(),
  /** 学校ID */
  schoolId: z.number(),
  /** 班级名称 */
  className: z.string(),
  /** 学校名称 */
  schoolName: z.string(),
  /** 学科ID */
  subjectId: z.number(),
  /** 用户名 */
  userName: z.string(),
  /** 用户头像 */
  userAvatar: z.string(),
  /** 父评论内容 */
  parentContent: z.string(),
  /** 课程名称 */
  courseName: z.string(),
  /** 组件名称 */
  widgetName: z.string(),
  /** 评论内容 */
  commentContent: z.string(),
  /** 评论范围(1=本班, 2=年级, 3=本校, 4=公开) */
  commentScope: z.number(),
  /** LLM评分 */
  llmScore: z.number(),
  /** 评论状态 */
  commentStatus: z.number(),
  /** 点赞数 */
  likesCount: z.number().min(0),
  /** 回复数 */
  repliesCount: z.number().min(0),
  /** 是否老师推荐 */
  isTeacherRecommended: z.number(),
  /** 权重分数 */
  weightScore: z.number(),
  /** 发布时间 */
  publishedAt: z.string(),
  /** 创建时间 */
  createdAt: z.number(),
  /** 更新时间 */
  updatedAt: z.number(),
  /** 回复的用户名,一级评论此值为空 */
  replyToUserName: z.string(),
  /** 回复的用户的ID,一级评论此值为0 */
  replyToUserID: z.string(),
  /** 回复的用户的头像地址,一级评论此值为空 */
  replyToUsrAvatar: z.string(),
  /** 是否是我的评论1是,0不是 */
  isMe: z.number(),
  /** 是否已经点赞 */
  isLike: z.number(),
  showName: z.string(),
});

/**
 * 二级评论信息 API 响应 Schema
 */
export const ApiSubCommentSchema = z.object({
  /** 评论ID */
  commentId: z.number(),
  /** 评论内容 */
  commentContent: z.string(),
  /** 父评论内容 */
  parentContent: z.string().nullable(),
  /** 状态 */
  status: z.number(),
  /** LLM评分 */
  llmScore: z.number(),
  /** 点赞数 */
  likeCount: z.number(),
  /** 子评论点赞数 */
  subLikeCount: z.number(),
  /** 老师推荐 */
  teacherRecommended: z.number(),
  /** 用户ID */
  userId: z.number(),
  /** 用户名 */
  userName: z.string(),
  /** 用户班级信息 */
  userClassInfo: z.string(),
  /** 发布时间字符串 */
  publishedTimeStr: z.string(),
  /** 发布范围 */
  publishedRange: z.number(),
  /** 课程 */
  course: z.string(),
  /** 所属模块 */
  belongsToModule: z.string(),
  /** 回复用户的id */
  replyToUserId: z.string(),
  /** 回复用户的名字 */
  replyToUserName: z.string(),
  /** 回复用户的头像地址 */
  replyToUserAvatar: z.string(),
  /** 是否是我自己的评论1是我的评论,0不是 */
  isMe: z.number(),
  /** 是否点赞1点赞,0没点赞 */
  isLike: z.number(),
});

/**
 * 分页信息 API 响应 Schema
 */
export const ApiPageInfoSchema = z.object({
  /** 当前页码 */
  page: z.number().min(1),
  /** 每页数量 */
  pageSize: z.number().min(1),
  /** 总记录数 */
  total: z.number().min(0),
  /** 总页数 */
  totalPages: z.number().min(0).optional(),
  /** 总页数（兼容字段） */
  totalPage: z.number().min(0).optional(),
});

/**
 * 额外信息 API 响应 Schema
 */
export const ApiExtraInfoSchema = z.object({
  /** 总评论数 */
  totalComments: z.number().min(0),
  /** 总点赞数 */
  totalLikes: z.number().min(0),
});

/**
 * 评论列表 API 响应 Schema
 */
export const ApiCommentsListDataSchema = z.object({
  /** 评论列表 */
  list: z.array(ApiCommentSchema),
  /** 分页信息 */
  pageInfo: ApiPageInfoSchema,
  /** 额外信息 */
  extraInfo: ApiExtraInfoSchema.optional(),
});

/**
 * 二级评论列表 API 响应 Schema
 */
export const ApiSubCommentsListDataSchema = z.object({
  /** 二级评论列表 */
  list: z.array(ApiSubCommentSchema),
  /** 分页信息 */
  pageInfo: ApiPageInfoSchema,
  /** 额外信息 */
  extraInfo: ApiExtraInfoSchema,
  /** 基准时间 */
  baseTime: z.string().optional(),
});

/**
 * 引用项 API 响应 Schema
 */
export const ApiReferenceItemSchema = z.object({
  /** 引用内容的主键ID */
  referenceId: z.number(),
  /** 引用类型 */
  referenceType: z.number(),
  /** 引用内容的文案 */
  referenceContent: z.string(),
  /** 引用图片 */
  referenceImage: z.string(),
  /** 引用位置信息 */
  referencePosition: ApiReferencePositionSchema,
  /** 引用位置MD5 */
  referencePositionMd5: z.string(),
  /** 组件下标 */
  widgetIndex: z.number(),
  /** 组件名称 */
  widgetName: z.string(),
  /** 组件类型 */
  widgetType: z.string(),
  /** 一级评论的数量 */
  commentCount: z.number(),
  /** 创建时间 */
  createdAt: z.number(),
  replyed: z.number(),
});

/**
 * 引用列表 API 响应 Schema
 */
export const ApiReferencesListDataSchema = z.object({
  /** 引用列表 */
  list: z.array(ApiReferenceItemSchema),
  /** 分页信息 */
  pageInfo: ApiPageInfoSchema,
});

/**
 * 获取评论列表 API 响应 Schema
 * 注意：外层的 status、code、message 等属性已在 fetcher 中处理，这里只需要验证 data 部分
 */
export const ApiGetCommentsResponseSchema = ApiCommentsListDataSchema;

/**
 * 获取二级评论列表 API 响应 Schema
 * 注意：外层的 status、code、message 等属性已在 fetcher 中处理，这里只需要验证 data 部分
 */
export const ApiGetSubCommentsResponseSchema = ApiSubCommentsListDataSchema;

/**
 * 获取引用列表 API 响应 Schema
 * 注意：外层的 status、code、message 等属性已在 fetcher 中处理，这里只需要验证 data 部分
 */
export const ApiGetReferencesResponseSchema = ApiReferencesListDataSchema;

/**
 * 通用 API 响应 Schema（用于增删改操作）
 * 注意：外层的 code、message 等属性已在 fetcher 中处理，这里只需要验证 data 部分
 */
export const ApiCommonResponseSchema = z.object({});

/**
 * 点赞操作 API 响应 Schema
 */
export const ApiLikeResponseSchema = z.object({
  /** 操作是否成功 */
  success: z.boolean(),
  /** 是否已点赞 */
  isLiked: z.boolean(),
  /** 点赞数 */
  likesCount: z.number(),
  /** 操作消息 */
  message: z.string(),
});

// ==================== API 请求参数 Schema ====================

/**
 * 发布评论请求参数 Schema
 */
export const AddCommentPayloadSchema = z.object({
  /** 课程ID */
  objId: z.number().min(1),
  /** 类型:100代表课程 */
  objType: z.number().min(1),
  /** 学科ID */
  subjectId: z.number(),
  /** 知识点ID */
  knowledgeId: z.number(),
  /** 学科名字 */
  courseName: z.string().optional(),
  /** 用户写的评论内容 */
  commentContent: z.string().min(1),
  /** 发布范围(1=本班, 2=年级, 3=本校, 4=公开) */
  commentScope: z.number().min(1).max(4),
  /** 引用类型:1文本,2图片,3,文本加图片 */
  referenceType: z.number().min(1).max(3),
  /** 划线内容 */
  referenceContent: z.string(),
  /** 图片链接地址,没有就传空 */
  referenceImage: z.string(),
  /** 定位的那个json对象 */
  referencePosition: ApiReferencePositionSchema,
  /** 组件下标 */
  widgetIndex: z.number(),
  /** 组件类型 */
  widgetType: z.string(),
  /** 组件名称 */
  widgetName: z.string(),
  /** 引用内容主键id */
  referenceId: z.number().optional(),
  /** 对定位的json进行md5的加密值 */
  referencePositionMd5: z.string().optional(),
  knowledgeName: z.string(),
  appVersion: z.string(),
  lessonId: z.number(),
  studyContent: z.string().optional(),
});

/**
 * 删除评论请求参数 Schema
 */
export const DeleteCommentPayloadSchema = z.object({
  /** 评论ID */
  commentId: z.number().min(1),
});

/**
 * 回复评论请求参数 Schema
 */
export const ReplyCommentPayloadSchema = z.object({
  /** 评论ID */
  commentId: z.number().min(1),
  /** 回复内容 */
  commentContent: z.string().min(1),
  studySessionId: z.number(),
  studyType: z.number(),
  lessonId: z.number(),
  widgetIndex: z.number(),
  commentScope: z.number(),
});

/**
 * 点赞评论请求参数 Schema
 */
export const LikeCommentPayloadSchema = z.object({
  /** 评论ID */
  commentId: z.number().min(1),
  /** 对象ID,传课程ID */
  objId: z.number().min(1),
  /** 对象类型,目前传100(100课程,200笔记) */
  objType: z.number().min(1),
  studySessionId: z.number(),
  studyType: z.number(),
  lessonId: z.number(),
});

// ==================== 导出类型 ====================

export type ApiTextReferenceCoordinate = z.infer<
  typeof ApiTextReferenceCoordinateSchema
>;
export type ApiPicReferenceCoordinate = z.infer<
  typeof ApiPicReferenceCoordinateSchema
>;
export type ApiReferenceCoordinate = z.infer<
  typeof ApiReferenceCoordinateSchema
>;
export type ApiReferencePosition = z.infer<typeof ApiReferencePositionSchema>;
export type ApiComment = z.infer<typeof ApiCommentSchema>;
export type ApiSubComment = z.infer<typeof ApiSubCommentSchema>;
export type ApiPageInfo = z.infer<typeof ApiPageInfoSchema>;
export type ApiExtraInfo = z.infer<typeof ApiExtraInfoSchema>;
export type ApiCommentsListData = z.infer<typeof ApiCommentsListDataSchema>;
export type ApiSubCommentsListData = z.infer<
  typeof ApiSubCommentsListDataSchema
>;
export type ApiReferenceItem = z.infer<typeof ApiReferenceItemSchema>;
export type ApiReferencesListData = z.infer<typeof ApiReferencesListDataSchema>;
export type ApiGetCommentsResponse = z.infer<
  typeof ApiGetCommentsResponseSchema
>;
export type ApiGetSubCommentsResponse = z.infer<
  typeof ApiGetSubCommentsResponseSchema
>;
export type ApiGetReferencesResponse = z.infer<
  typeof ApiGetReferencesResponseSchema
>;
export type ApiCommonResponse = z.infer<typeof ApiCommonResponseSchema>;
export type ApiLikeResponse = z.infer<typeof ApiLikeResponseSchema>;

// 注意：请求参数类型在 types.ts 中定义，避免重复
