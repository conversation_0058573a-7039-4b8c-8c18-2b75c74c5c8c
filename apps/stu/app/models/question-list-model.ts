import { get } from "@/app/utils/fetcher";
import { StudentAnswer } from "@repo/core/exercise/model/types";
import { CommonQuestionContent } from "@repo/core/types";
import useSWR from "swr";

export interface Request {
  /**
   * 是否只看错题，默认查看全部
   */
  onlyWrong?: boolean;
  /**
   * 题目 id，多个题目用','连接，如果指定题目，则查询指定题目的作答信息
   */
  questionIds?: string;
  /**
   * 学习会话 id
   */
  studySessionId?: number;
  /**
   * 组件序号，仅 AI 课中的练习需要此字段
   */
  widgetIndex?: number;
}

/**
 * StudySessionDetail
 */
export interface QuestionDetailData {
  /**
   * 题目列表，按出题顺序提供
   */
  questions: QuestionInfo[];
  /**
   * 学生答题记录
   */
  studentAnswers: StudentAnswer[];
  /**
   * 学习会话 id
   */
  studySessionId: number;
  /**
   * 学习类型 (1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 7:课中练习, 99:其他)
   */
  studyType: number;

  lessonId?: number;
}

/**
 * QuestionInfo，题目信息
 */
export interface QuestionInfo {
  questionIndex?: number;
  /**
   * 上次作答时间，毫秒
   */
  lastAnswerDuration: number;
  /**
   * 标准答案
   */
  questionAnswer?: null | QuestionAnswer;
  questionAnswerMode: number;
  questionContent: CommonQuestionContent;
  /**
   * 难度等级
   */
  questionDifficult?: number;
  /**
   * 题目解析
   */
  questionExplanation: string;
  /**
   * 学科ID
   */
  questionExtra?: string;
  /**
   * 题目唯一ID
   */
  questionId: string;
  /**
   * 题目内容
   */
  questionTopic?: string;
  /**
   * 题型类型 (1:单选题, 2:多选题, 3:填空题, 4:解答题)
   */
  questionType: number;
}

export interface QuestionAnswer {
  answerOptionList: AnswerOptionList[];
}

export interface AnswerOptionList {
  /**
   * 选项key
   */
  optionKey: string;
  /**
   * 选项值
   */
  optionVal: string;
}

export interface QuestionOptionList {
  /**
   * 选项key
   */
  optionKey: string;
  /**
   * 选项值
   */
  optionVal: string;
}

export interface AnswerStat {
  /**
   * 选项
   */
  optionKey: string;
  /**
   * 选择比例
   */
  rate: string;
}

export interface QuestionItem {
  questionId: string;
  type: string;
  content: string;
  status: "correct" | "partially-correct" | "wrong";
}

// 学习类型常量
export const STUDY_TYPES = {
  AI_LESSON: 1,
  CONSOLIDATION_PRACTICE: 2,
  EXTENSION_PRACTICE: 3,
  NORMAL_HOMEWORK: 4,
  TEST_TASK: 5,
  RESOURCE_TASK: 6,
  IN_CLASS_PRACTICE: 7,
  OTHER: 99,
} as const;

// 题型类型常量
export const QUESTION_TYPES = {
  SINGLE_CHOICE: 1,
  MULTIPLE_CHOICE: 2,
  FILL_BLANK: 3,
  ESSAY: 4,
} as const;

// 题目类型映射表
export const QUESTION_TYPE_LABELS = {
  [QUESTION_TYPES.SINGLE_CHOICE]: "单选题",
  [QUESTION_TYPES.MULTIPLE_CHOICE]: "多选题",
  [QUESTION_TYPES.FILL_BLANK]: "填空题",
  [QUESTION_TYPES.ESSAY]: "解答题",
} as const;

// 将题目类型数字转换为中文名称
function getQuestionTypeLabel(questionType: number): string {
  return (
    QUESTION_TYPE_LABELS[questionType as keyof typeof QUESTION_TYPE_LABELS] ||
    "未知题型"
  );
}

// 判题结果常量
export const ANSWER_RESULTS = {
  NOT_ANSWERED: 0,
  CORRECT: 1,
  WRONG: 2,
  PARTIALLY_CORRECT: 3,
} as const;

// 回答类型常量
export const ANSWER_TYPES = {
  TEXT: 1,
  IMAGE: 2,
  VIDEO: 3,
} as const;

// 判题类型常量
export const EVALUATION_TYPES = {
  SYSTEM: 1,
  SELF: 2,
} as const;

function transformQuestion(
  questions: QuestionInfo[],
  studentAnswers: StudentAnswer[],
  onlyWrong?: boolean
) {
  const questionMap = new Map<string, QuestionInfo>();
  questions.forEach((question) => {
    questionMap.set(question.questionId || "", question);
  });

  const result: QuestionItem[] = [];
  studentAnswers.forEach((answer) => {
    const question = questionMap.get(answer.questionId);
    if (question) {
      const status =
        answer.answerResult === ANSWER_RESULTS.CORRECT ? "correct" : answer.answerResult === ANSWER_RESULTS.PARTIALLY_CORRECT ? "partially-correct" : "wrong";

      // 如果 onlyWrong 为 true，只返回错题
      if (onlyWrong && status === "correct") {
        return;
      }

      result.push({
        questionId: question.questionId || "",
        type: getQuestionTypeLabel(question.questionType || 0),
        content: question.questionContent.questionStem || "",
        status,
      });
    }
  });
  return result;
}

// 获取题目列表的 Hook
export function useQuestionList(request: Request) {
  // consol.log("request", request);

  // 构建缓存键，包含查询参数以确保参数变化时触发重新请求
  const cacheKey = request.studySessionId
    ? [`/api/v1/study_session/answer_detail`, request]
    : null;
  // consol.log("cacheKey", cacheKey);

  const {
    data: res,
    isLoading,
    error,
    mutate,
  } = useSWR(cacheKey, async (key: string | [string, Request]) => {
    // 处理数组形式的缓存键
    const [url, params] = Array.isArray(key) ? key : [key, request];
    const res = await get<QuestionDetailData>(url, { query: params as any });
    // consol.log("res", res);
    // if (res?.code !== 0) throw new Error(res?.message);
    return res;
  });

  return {
    questions: transformQuestion(
      res?.questions || [],
      res?.studentAnswers || [],
      request.onlyWrong
    ),
    data: res,
    isLoading,
    error,
    refresh: mutate,
    lessonId: res?.lessonId,
    studyType: res?.studyType,
  };
}
