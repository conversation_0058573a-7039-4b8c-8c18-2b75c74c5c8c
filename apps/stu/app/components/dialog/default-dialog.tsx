import { FC, ReactNode } from "react";
import { cn } from "@repo/ui/lib/utils";
import ReactDOM from "react-dom";
import Button from "@repo/ui/components/press-button";
import { StudyType } from "@/app/models/exercise";

/**
 * 对话框组件
 * @param title 对话框标题
 * @param children 对话框内容
 * @param open 是否显示对话框
 * @param onClose 关闭对话框的回调函数
 * @param buttons 对话框按钮配置
 * @param buttons.text 按钮文本
 * @param buttons.onClick 按钮点击回调
 * @param buttons.type 按钮类型,'primary'为主按钮,'default'为次按钮
 */

export interface DialogProps {
  title?: string;
  subtitle?: string;
  children?: ReactNode;
  open?: boolean;
  onClose?: () => void;
  buttons?: {
    text: string;
    onClick: () => void;
    color?: "orange" | "red" | "green" | "gray" | "white" | "study-theme";
    disabled?: boolean;
    className?: string;
    studyType?: StudyType;
  }[];
  studyType?: StudyType;
}

export const DialogView: FC<DialogProps> = ({
  title,
  subtitle,
  children,
  open = false,
  onClose,
  buttons = [],
  studyType,
}) => {
  if (!open) return null;

  return ReactDOM.createPortal(
    <div className="dialog-view fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="dialog-backdrop fixed inset-0 bg-[rgba(0,0,0,0.5)]"
        onClick={onClose}
      />
      <div className="dialog-content relative z-10 w-80 rounded-2xl bg-white shadow-lg">
        {title && (
          <div className="dialog-title px-6 pb-2 pt-9 text-center text-lg font-bold text-[#1F232B]">
            {title}
          </div>
        )}

        {subtitle && (
          <div className="dialog-subtitle px-6 pb-4 text-center text-sm text-[#666666]">
            {subtitle}
          </div>
        )}

        {children && (
          <div className="dialog-body px-6 pb-4 text-center text-sm text-[#666666]">
            {children}
          </div>
        )}

        {buttons.length > 0 && (
          <div className="dialog-buttons flex gap-3 px-6 pb-6 pt-5">
            {buttons.map((button, index) => (
              <Button
                key={index}
                studyType={button.studyType || studyType}
                color={button.color || "orange"}
                className={cn("flex-1", button.className)}
                onClick={button.onClick}
                disabled={button.disabled}
                source="dialog"
              >
                {button.text}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};
