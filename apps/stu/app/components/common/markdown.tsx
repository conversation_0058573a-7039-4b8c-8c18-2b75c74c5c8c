"use client";

import { renderFormulasByKatex } from "@repo/core/components/rich-text-view/utils";
import { FC } from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

export type PreviewOptions = {
  supportsHTML?: boolean;
};

export const Markdown: FC<{ content: string }> = ({ content }) => {
  return (
    <div className="prose wrap-anywhere">
      <ReactMarkdown rehypePlugins={[rehypeRaw]}>
        {renderFormulasByKatex(content.replace(/^---?/gm, "- "))}
      </ReactMarkdown>
    </div>
  );
};
