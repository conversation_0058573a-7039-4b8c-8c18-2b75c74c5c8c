import { toast } from "@/app/components/common/toast";
import { useQuestionContext } from "@/app/contexts/question-context";
import { useBridge } from "@/app/hooks/use-bridge";
import { NextQuestionInfo } from "@repo/core/exercise/model";

import {
  CheckAnswerPictureResponse,
  useCheckAnswerPicture,
} from "@/app/models/exercise/exercise-model";
import {
  convertContentUriToFile,
  createPreviewUrl,
  revokePreviewUrl,
} from "@/app/utils/image-utils";
import { upload } from "@/app/utils/oss";
import { parseContent } from "@/app/utils/parseFillBlank";
import { QUESTION_TYPE } from "@repo/core/enums";
import { AnswerResult, UserAnswerData } from "@repo/core/exercise/model/types";
import { RefObject, useCallback, useEffect, useRef, useState } from "react";

const signatureUrl = "/api/v1/common/oss/token";
export interface BlankAnswer {
  id: number;
  value: string;
}

export interface FillBlankViewModel {
  isReview: boolean;
  question: NextQuestionInfo;
  answers: BlankAnswer[];
  activeBlankIndex: number;
  inputMode: InputMode;
  imageFiles: ImageFile[];
  fileInputRef: RefObject<HTMLInputElement | null>;
  setActiveBlankIndex: (index: number) => void;
  handleTextChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleModeChange: (mode: InputMode) => void;
  handleRemoveImage: (id: number) => void;
  handleSubmitWithDialog: (handleSubmitClick: () => void) => void;
  handleRetryUpload: (id: number) => void;
  correctAnswers: string[];
  selfEvaluation: ("right" | "partial" | "wrong" | null)[];
  onSelfEvaluate: (type: "right" | "partial" | "wrong") => void;
  handleContinueReview: () => void;
  handleBlankClick: (index: number) => void;
  openInputDialog: (initialValue: string, index: number) => void;
  closeInputDialog: () => void;
  confirmInputDialog: () => void;
  showInputDialog: boolean;
  inputDialogValue: string;
  setInputDialogValue: (val: string) => void;
  textareaAutoResize: (ref: HTMLTextAreaElement | null) => void;
  handleSelfEvaluate: (type: "right" | "partial" | "wrong") => void;
  triggerNativeImageUpload: () => void;
  isEnglishFillBlank: boolean;
  canSelfEvaluate: (index?: number) => boolean;
  getSelfEvaluationData: () => (0 | 1 | 2 | 3)[];
  isAllSelfEvaluated: () => boolean;
  hasValidAnswers: () => boolean;
  shouldShowSelfEvaluation: () => boolean;
}

export interface ImageFile {
  id: number;
  file: File | null; // 允许为 null，在转换过程中可能暂时为空
  preview: string;
  status?: "pending" | "success" | "error" | "audit-failed";
}

export type InputMode = "keyboard" | "camera";

// 添加重试函数
async function retry<T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) throw error;
    await new Promise((resolve) => setTimeout(resolve, delay));
    return retry(fn, retries - 1, delay);
  }
}

// 添加答案比较函数
function compareAnswers(
  answer: string,
  correctAnswer: AnswerResult | undefined
): "right" | "partial" | "wrong" {
  if (!answer || !correctAnswer) return "wrong";

  const answerVerify = correctAnswer.answerVerify;

  switch (answerVerify) {
    case 1: // 正确
      return "right";
    case 2: // 错误
      return "wrong";
    case 3: // 部分正确
      return "partial";
    default: // 0: 未作答
      return "wrong";
  }
}

export function useFillBlankViewModel(question: NextQuestionInfo) {
  // 获取统一的Context配置
  const {
    questionState,
    setQuestionState,
    updateUserAnswer,
    lastSubmitResult,
    studentAnswer,
    isEnglish,
  } = useQuestionContext();

  const bridge = useBridge();
  const { checkPicture } = useCheckAnswerPicture();

  // 这里根据题目类型计算空白数量，后面需要改成切换不同作答方式
  const blankCount =
    // 如果是填空题，需要解析题干内容来判断有几个空
    question.questionType === QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK
      ? parseContent(question?.questionContent?.questionStem || "").reduce(
          (count, paragraph) =>
            count + paragraph.filter((item) => item === null).length,
          0
        )
      : question.questionType === QUESTION_TYPE.QUESTION_TYPE_QA // 如果是解答题，默认一个空
        ? 1
        : 0;
  // console.log("blankCount", blankCount);
  const [answers, setAnswers] = useState<BlankAnswer[]>(() => {
    const initialAnswers = Array.from({ length: blankCount }, (_, idx) => ({
      id: idx + 1,
      value: "",
    }));
    // 确保至少有一个答案项
    return initialAnswers.length > 0 ? initialAnswers : [{ id: 1, value: "" }];
  });
  const [activeBlankIndex, setActiveBlankIndex] = useState(0);
  const [inputMode, setInputMode] = useState<InputMode>("keyboard");
  const [imageFiles, setImageFiles] = useState<ImageFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [correctAnswers, setCorrectAnswers] = useState<string[]>(
    question.questionAnswer?.answerOptionList.map(
      (option: { optionVal?: string }) => option.optionVal || ""
    ) ?? []
  );
  // 新增：每个空的自评状态
  const [selfEvaluation, setSelfEvaluation] = useState<
    ("right" | "partial" | "wrong" | null)[]
  >(Array.from({ length: blankCount }).map(() => null));

  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [submitDialogType, setSubmitDialogType] = useState<
    "allEmpty" | "partEmpty" | "photoEmpty" | null
  >(null);

  const [showInputDialog, setShowInputDialog] = useState(false);
  const [inputDialogValue, setInputDialogValue] = useState("");
  const [inputDialogIndex, setInputDialogIndex] = useState<number | null>(null);

  const [keyboardAnswersCache, setKeyboardAnswersCache] = useState<
    BlankAnswer[]
  >(() =>
    Array.from({ length: blankCount }).map((_, idx) => ({
      id: idx + 1,
      value: "",
    }))
  );

  const [isEnglishFillBlank] = useState(isEnglish);

  useEffect(() => {
    return () => {
      imageFiles.forEach((img) => {
        if (img.preview) {
          revokePreviewUrl(img.preview);
        }
      });
    };
  }, [imageFiles]);

  // 监听题目变化，重置所有状态
  useEffect(() => {
    // 重新计算空白数量
    let newBlankCount = parseContent(
      question.questionContent.questionStem || ""
    ).reduce(
      (count, paragraph) =>
        count + paragraph.filter((item) => item === null).length,
      0
    );

    // 如果是简答题且没有找到空白，设置为1
    if (
      (question.questionType === QUESTION_TYPE.QUESTION_TYPE_QA ||
        question.questionType ===
          QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK) &&
      newBlankCount === 0
    ) {
      newBlankCount = 1;
    }

    console.log(
      "214 questionState",
      questionState,
      question.questionType,
      studentAnswer
    );
    const firstAnswerContent = studentAnswer?.answerContents[0];
    if (
      (studentAnswer?.answerContents.length || 0) > 0 &&
      firstAnswerContent?.content &&
      Array.isArray(firstAnswerContent.content)
    ) {
      setAnswers(
        firstAnswerContent.content.map((item: string, index: number) => ({
          id: index + 1,
          value: item,
        }))
      );
    } else {
      setAnswers(
        Array.from({ length: newBlankCount }).map((_, idx) => ({
          id: idx + 1,
          value: "",
        }))
      );
    }

    setActiveBlankIndex(0);
    const content = studentAnswer?.answerContents[0]?.content;
    if (
      content &&
      content.length > 0 &&
      studentAnswer?.answerContents[0]?.type === 2
    ) {
      setInputMode("camera");
      setImageFiles(
        content.map((item: any) => ({
          id: 0,
          file: new File([], ""),
          preview: item,
          status: "success",
        }))
      );
    } else {
      setInputMode("keyboard");
      setImageFiles([]);
    }
    setCorrectAnswers(
      question.questionAnswer?.answerOptionList.map(
        (option: { optionVal?: string }) => option.optionVal || ""
      ) ?? []
    );
    if (studentAnswer?.answerContents[0]?.answerResult !== undefined) {
      // 情况2: 如果没有具体的自评数据，但有整体答题结果，为所有空格设置相同的状态
      // 这种情况通常出现在系统自动判题或者老版本数据中
      const overallResult = studentAnswer.answerContents[0].answerResult;
      let evaluationResult: "right" | "partial" | "wrong" | null = null;

      switch (overallResult) {
        case 1: // 正确
          evaluationResult = "right";
          break;
        case 2: // 错误
          evaluationResult = "wrong";
          break;
        case 3: // 部分正确
          evaluationResult = "partial";
          break;
        case 0: // 未作答
        default:
          evaluationResult = "wrong";
          break;
      }

      // 为所有空格设置相同的评价结果
      setSelfEvaluation(
        Array.from({ length: newBlankCount }).map(() => evaluationResult)
      );
    } else {
      setSelfEvaluation(Array.from({ length: newBlankCount }).map(() => null));
    }
    setShowSubmitDialog(false);
    setSubmitDialogType(null);
    setShowInputDialog(false);
    setInputDialogValue("");
    setInputDialogIndex(null);
    setKeyboardAnswersCache(
      Array.from({ length: newBlankCount }).map((_, idx) => ({
        id: idx + 1,
        value: "",
      }))
    );
  }, [question.questionId]);

  // 更新自评状态
  useEffect(() => {
    // 🎯 修改：自评结果完全按照用户选择来判断，只有为空的时候才默认为"我答错了"
    let newSelfEvaluation: ("right" | "partial" | "wrong" | null)[];
    console.log(questionState);
    if (questionState === "submitted") {
      if (inputMode === "keyboard") {
        // 英语填空题且有答案结果时，使用答案结果进行自评，系统判题
        if (isEnglishFillBlank && lastSubmitResult?.answerResult) {
          const newSelfEvaluation = answers.map((answer, index) => {
            const correctAnswer = lastSubmitResult.answerResult?.[index];
            return compareAnswers(answer.value, correctAnswer);
          });
          // console.log("✅ 英语填空题，使用答案结果设置自评:", newSelfEvaluation);
          setSelfEvaluation(newSelfEvaluation);
          return;
        }
      }
    }
    if (questionState === "awaiting_self_evaluation") {
      if (inputMode === "keyboard") {
        // 键盘模式：只有空的答案才设置为"wrong"，有内容的保持为null（等待用户自评）
        newSelfEvaluation = answers.map((answer, index) => {
          if (!answer.value.trim()) {
            return "wrong"; // 只有为空的时候才默认为"我答错了"
          }
          return selfEvaluation[index] || null; // 有内容的保持为null，等待用户主动选择自评结果
        });
        // console.log("🎯 最终设置的自评状态:", newSelfEvaluation);
        setSelfEvaluation(newSelfEvaluation);
      }
      return;
    }
    // 只在已提交或放弃作答状态下处理自评
    if (questionState !== "giving_up") {
      // console.log("❌ 不在自评状态，跳过自评设置");
      return;
    }

    // console.log("answers", answers, selfEvaluation);

    if (inputMode === "keyboard") {
      // 键盘模式：只有空的答案才设置为"wrong"，有内容的保持为null（等待用户自评）
      newSelfEvaluation = answers.map((answer, index) => {
        if (!answer.value.trim()) {
          return "wrong"; // 只有为空的时候才默认为"我答错了"
        }
        return selfEvaluation[index] || null; // 有内容的保持为null，等待用户主动选择自评结果
      });
      // console.log("✅ 键盘模式，设置自评状态:", newSelfEvaluation);
    } else if (inputMode === "camera") {
      // 拍照模式：基于blankCount和imageFiles设置自评状态
      const successImages = imageFiles.filter(
        (img) => img.status === "success"
      );
      console.log("selfEvaluation", selfEvaluation, successImages);
      if (successImages.length > 0) {
        newSelfEvaluation = selfEvaluation.map((selfEvaluation) => {
          return selfEvaluation || null; // 有内容的保持为null，等待用户主动选择自评结果
        });
      } else {
        newSelfEvaluation = selfEvaluation.map((selfEvaluation) => {
          return "wrong"; // 没有图片时默认为"我答错了"
        });
      }
    } else {
      // 默认情况：基于blankCount设置
      const targetLength = Math.max(blankCount, 1); // 至少为1
      newSelfEvaluation = Array.from({ length: targetLength }).map(
        () => null // 默认保持为null，等待用户主动选择自评结果
      );
    }

    // console.log("🎯 最终设置的自评状态:", newSelfEvaluation);
    setSelfEvaluation(newSelfEvaluation);
  }, [
    lastSubmitResult,
    answers,
    questionState,
    inputMode,
    imageFiles,
    isEnglishFillBlank,
    blankCount, // 🔧 添加blankCount依赖
    activeBlankIndex, // 🔧 添加activeBlankIndex依赖
  ]);

  function updateStatus(
    id: number,
    status: "pending" | "success" | "error" | "audit-failed"
  ) {
    setImageFiles((prev) =>
      prev.map((img) => (img.id === id ? { ...img, status } : img))
    );
  }

  // 优化图片上传函数
  async function handleImageUpload(img: ImageFile) {
    try {
      updateStatus(img.id, "pending");
      const { url, error } = await retry(() =>
        upload({
          file: img.file as File,
          signature: {
            url: signatureUrl,
            params: {},
          },
        })
      );

      if (url) {
        try {
          const pictureCheckResult = (await checkPicture({
            pictureUrls: [url],
          })) as CheckAnswerPictureResponse;

          if (pictureCheckResult.isPass) {
            updateStatus(img.id, "success");
            return url;
          } else {
            // 识别内容未通过，移除图片并弹窗提示
            setImageFiles((prev) =>
              prev.filter((image) => image.id !== img.id)
            );
            toast.show("请上传和题目相关的内容", { duration: 3000 });
            throw new Error("图片审核未通过");
          }
        } catch (error) {
          // console.error("图片审核失败:", error);
          // 如果是审核未通过的错误，不需要额外处理，图片已经被移除
          if (error instanceof Error && error.message === "图片审核未通过") {
            throw error;
          }
          // 其他错误，设置为错误状态
          updateStatus(img.id, "error");
          throw error;
        }
      } else {
        throw new Error(error || "上传失败");
      }
    } catch (error) {
      // console.error("图片上传失败:", error);
      updateStatus(img.id, "error");
      // toast.show("图片上传失败，请重试");
      throw error;
    }
  }

  // 优化重试上传函数
  const handleRetryUpload = useCallback(
    async (id: number) => {
      const img = imageFiles.find((img) => img.id === id);
      if (!img) return;

      try {
        await handleImageUpload(img);
      } catch (error) {
        // 错误已在 handleImageUpload 中处理
      }
    },
    [imageFiles]
  );

  // 优化移除图片函数
  const handleRemoveImage = useCallback((id: number) => {
    setImageFiles((prev) => {
      const fileToRemove = prev.find((img) => img.id === id);
      if (fileToRemove) {
        revokePreviewUrl(fileToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  }, []);

  function handleTextChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    // 用户主动输入内容时，如果当前是放弃作答状态，回退到答题状态
    if (questionState === "giving_up" || questionState === "uncertain") {
      setQuestionState("answering");
    }

    const newAnswers = [...answers];
    if (newAnswers[activeBlankIndex]) {
      newAnswers[activeBlankIndex].value = e.target.value;
      setAnswers(newAnswers);

      // 🔧 修复：同步到Context中的答案状态
      if (newAnswers.length === 1 && newAnswers[0]) {
        // 只有一个空时，当作简答题处理
        updateUserAnswer({ subjectiveAnswer: [newAnswers[0].value] });
      } else {
        // 多个空时，当作填空题处理
        const subjectiveAnswer = newAnswers.map((answer) => answer.value);
        updateUserAnswer({ subjectiveAnswer });
      }
    }
  }

  function handleModeChange(mode: InputMode) {
    if (mode === inputMode) return;

    // 🔧 修复：同步inputMode到QuestionContext
    updateUserAnswer({ inputMode: mode });

    if (mode === "camera") {
      // 切到拍照，缓存当前 answers 并清空 answers
      setKeyboardAnswersCache(answers);
      setAnswers(
        Array.from({ length: blankCount }).map((_, idx) => ({
          id: idx + 1,
          value: "",
        }))
      );
      // 🔧 修复：拍照模式下，确保activeBlankIndex不为-1，以便自评状态能正确显示
      if (activeBlankIndex === -1 && blankCount > 0) {
        setActiveBlankIndex(0);
      }
    } else if (mode === "keyboard") {
      setActiveBlankIndex(0);
      // 切回键盘，恢复缓存
      setAnswers(keyboardAnswersCache);
    }
    setInputMode(mode);
  }

  function handleSubmitWithDialog(
    handleSubmitClick: (userAnswerData?: UserAnswerData) => void
  ) {
    if (inputMode === "camera") {
      if (imageFiles.filter((img) => img.status === "success").length > 0) {
        // 有图片，直接提交
        handleSubmitClick({
          imgFiles: imageFiles
            .map((img) => img.preview)
            .filter(Boolean) as string[],
        });
        return;
      } else {
        setSubmitDialogType("photoEmpty");
        setShowSubmitDialog(true);
        return;
      }
    }
    // console.log("answers", answers);
    // 文字作答
    const emptyCount = answers.filter((a) => !a.value.trim()).length;
    if (emptyCount === answers.length) {
      setSubmitDialogType("allEmpty");
      setShowSubmitDialog(true);
      return;
    } else if (emptyCount > 0) {
      setSubmitDialogType("partEmpty");
      setShowSubmitDialog(true);
      return;
    } else {
      // 全部有内容，直接提交
      handleSubmitClick();
      return;
    }
  }

  function handleContinueSubmit() {
    setShowSubmitDialog(false);
  }

  function handleCancelSubmit() {
    setShowSubmitDialog(false);
  }

  function handleBlankClick(idx: number) {
    // if (questionState !== "submitted" && questionState !== "giving_up") {
    //   setInputMode("keyboard");
    // }
    setActiveBlankIndex(idx);
  }

  // 新增：自评按钮点击
  const onSelfEvaluate = useCallback(
    (type: "right" | "partial" | "wrong") => {
      setSelfEvaluation((prev) => {
        const next = [...prev];
        next[activeBlankIndex] = type;
        return next;
      });
    },
    [activeBlankIndex]
  );

  // 🆕 改造的自评处理函数 - 只更新本地状态，不调用API
  const handleSelfEvaluate = useCallback(
    (type: "right" | "partial" | "wrong") => {
      try {
        console.log("handleSelfEvaluate", type);
        // 🔧 检查当前空是否有内容，没有内容不允许自评（但允许"我答错了"）
        // const isAllEmpty = answers.every((answer) => !answer.value.trim());
        // if (isAllEmpty && type !== "wrong") {
        //   return;
        // }
        if (inputMode === "keyboard") {
          // 键盘模式：检查当前空的文字内容
          const currentAnswer = answers[activeBlankIndex];
          if (
            (!currentAnswer || !currentAnswer.value.trim()) &&
            type !== "wrong"
          ) {
            toast.show("请先填写答案再进行自评");
            return;
          }
        } else if (inputMode === "camera") {
          // 拍照模式：检查是否有成功上传的图片
          const successImages = imageFiles.filter(
            (img) => img.status === "success"
          );
          // if (successImages.length === 0) {
          //   toast.show("请先上传图片再进行自评");
          //   return;
          // }
        }

        // 🆕 只更新本地状态，不调用API
        onSelfEvaluate(type);

        // 🆕 只有键盘模式才自动跳到下一个空
        // if (inputMode === "keyboard") {
        const hasUnevaluated = selfEvaluation.some(
          (v, i) => i !== activeBlankIndex && v === null
        );

        if (hasUnevaluated) {
          setTimeout(() => {
            if (activeBlankIndex < selfEvaluation.length - 1) {
              setActiveBlankIndex(activeBlankIndex + 1);
            } else {
              const firstUnEvaluated = selfEvaluation.findIndex(
                (v) => v === null
              );
              if (firstUnEvaluated !== -1) {
                setActiveBlankIndex(firstUnEvaluated);
              }
            }
          }, 500);
        }
        // }
      } catch (error) {
        // console.error("自评状态更新失败:", error);
      }
    },
    [
      inputMode,
      activeBlankIndex,
      answers,
      imageFiles,
      onSelfEvaluate,
      selfEvaluation,
    ]
  );

  // 解析状态下"继续"按钮，跳到下一个空
  function handleContinueReview() {
    setActiveBlankIndex((prev) => {
      if (prev < answers.length - 1) {
        return prev + 1;
      }
      return prev;
    });
  }

  function openInputDialog(initialValue: string, index: number) {
    setInputDialogValue(initialValue);
    setInputDialogIndex(index);
    setShowInputDialog(true);
  }

  function closeInputDialog() {
    setShowInputDialog(false);
    // setInputDialogIndex(null);
  }

  function confirmInputDialog() {
    // 用户确认输入内容时，如果当前是放弃作答状态，回退到答题状态
    if (questionState === "giving_up" || questionState === "uncertain") {
      setQuestionState("answering");
    }

    if (inputDialogIndex !== null) {
      setAnswers((prev) => {
        if (prev.length === 0) {
          prev = [{ id: 1, value: "" }];
        }
        const next: BlankAnswer[] = [...prev];
        const blankToUpdate = next[inputDialogIndex];
        if (blankToUpdate) {
          next[inputDialogIndex] = {
            ...blankToUpdate,
            value: inputDialogValue,
          };
        }

        // 🔧 修复：同步到Context中的答案状态
        if (next.length === 1 && next[0]) {
          // 只有一个空时，当作简答题处理，但仍然使用数组格式
          updateUserAnswer({ subjectiveAnswer: [next[0].value] });
        } else {
          // 多个空时，当作填空题处理
          const subjectiveAnswer = next.map(
            (answer: BlankAnswer) => answer.value
          );
          updateUserAnswer({ subjectiveAnswer });
        }

        return next;
      });
    }
    setShowInputDialog(false);
    setInputDialogIndex(null);
  }

  function textareaAutoResize(ref: HTMLTextAreaElement | null) {
    if (!ref) return;
    const maxRows = 3;
    const lineHeight = 32;
    const maxHeight = maxRows * lineHeight;
    ref.style.height = "auto";
    const newHeight = Math.min(ref.scrollHeight, maxHeight);
    ref.style.height = `${newHeight}px`;
  }

  async function triggerNativeImageUpload() {
    try {
      // 第一步：调用 bridge 方法获取图片
      const res = await bridge.callAsync<{ imageUri: string }>(
        "takePhotoAndCrop"
      );

      if (!res.imageUri) {
        toast.show("获取图片失败");
        return;
      }

      // 第二步：立即创建 loading 状态的图片项
      const imageId = Date.now();
      const fileName = `image-${imageId}.jpg`;

      // 创建预览 URL
      const previewUrl = createPreviewUrl(res.imageUri);

      // 立即添加到图片列表，状态为 pending
      setImageFiles((prev) => [
        ...prev,
        {
          id: imageId,
          file: null as any, // 临时设置为 null，稍后更新
          preview: previewUrl,
          status: "pending", // 立即设置为 loading 状态
        },
      ]);

      // 第三步：转换图片格式并上传
      try {
        // 将 content:// URI 或 base64 转换为 File 对象
        const file = await convertContentUriToFile(res.imageUri, fileName);

        // 更新图片列表中的 file 对象
        setImageFiles((prev) =>
          prev.map((img) => (img.id === imageId ? { ...img, file } : img))
        );

        // 第四步：上传图片
        const { url, error } = await upload({
          file,
          signature: {
            url: signatureUrl,
            params: {},
          },
        });

        if (url) {
          // 第五步：图片审核
          try {
            const pictureCheckResult = (await checkPicture({
              pictureUrls: [url],
            })) as CheckAnswerPictureResponse;

            if (pictureCheckResult.isPass) {
              // 更新图片状态
              setImageFiles((prev) =>
                prev.map((img) =>
                  img.id === imageId
                    ? {
                        ...img,
                        preview: url, // 更新为上传后的 URL
                        status: "success",
                      }
                    : img
                )
              );
            } else {
              // 识别内容未通过，移除图片并弹窗提示
              setImageFiles((prev) => prev.filter((img) => img.id !== imageId));
              toast.show("请上传和题目相关的内容", { duration: 3000 });
            }
          } catch (error) {
            console.error("图片审核失败:", error);
            // 审核失败，移除图片
            setImageFiles((prev) => prev.filter((img) => img.id !== imageId));
          }
        } else {
          // 上传失败
          setImageFiles((prev) =>
            prev.map((img) =>
              img.id === imageId ? { ...img, status: "error" } : img
            )
          );
          toast.show(error || "图片上传失败");
        }
      } catch (error) {
        console.error("图片处理失败:", error);
        // 处理异常，设置为错误状态
        setImageFiles((prev) =>
          prev.map((img) =>
            img.id === imageId ? { ...img, status: "error" } : img
          )
        );
        toast.show("图片处理失败，请重试");
      }
    } catch (error) {
      // console.error("调用拍照接口失败:", error);
      // toast.show("拍照失败，请重试");
    }
  }

  function canSelfEvaluate(index?: number): boolean {
    const targetIndex = index ?? activeBlankIndex;

    if (inputMode === "keyboard") {
      // 键盘模式：检查指定空的文字内容
      const targetAnswer = answers[targetIndex];
      return Boolean(targetAnswer && targetAnswer.value.trim().length > 0);
    } else if (inputMode === "camera") {
      // 拍照模式：检查是否有成功上传的图片
      const successImages = imageFiles.filter(
        (img) => img.status === "success"
      );
      return successImages.length > 0;
    }

    return false;
  }

  // 🆕 获取自评数据用于二次提交
  function getSelfEvaluationData(): (0 | 1 | 2 | 3)[] {
    return selfEvaluation.map((evaluation): 0 | 1 | 2 | 3 => {
      switch (evaluation) {
        case "right":
          return 1; // 正确
        case "partial":
          return 3; // 部分正确
        case "wrong":
          return 2; // 错误
        default:
          return 0; // 未作答
      }
    });
  }

  // 🆕 检查是否所有空都已自评
  function isAllSelfEvaluated(): boolean {
    return selfEvaluation.every((evaluation) => evaluation !== null);
  }

  // 🆕 检查是否有有效答案（非空答案）
  function hasValidAnswers(): boolean {
    if (inputMode === "keyboard") {
      // 键盘模式：检查是否有非空文字答案
      return answers.some((answer) => answer.value.trim().length > 0);
    } else if (inputMode === "camera") {
      // 拍照模式：检查是否有成功上传的图片
      const successImages = imageFiles.filter(
        (img) => img.status === "success"
      );
      return successImages.length > 0;
    }
    return false;
  }

  // 🆕 检查是否需要显示自评按钮（有有效答案时才需要自评）
  function shouldShowSelfEvaluation(): boolean {
    return hasValidAnswers();
  }

  return {
    question,
    answers,
    setAnswers,
    activeBlankIndex,
    setActiveBlankIndex,
    inputMode,
    setInputMode,
    imageFiles,
    setImageFiles,
    fileInputRef,
    handleTextChange,
    handleRemoveImage,
    handleModeChange,
    handleSubmitWithDialog,
    handleBlankClick,
    handleRetryUpload,
    correctAnswers,
    setCorrectAnswers,
    selfEvaluation,
    setSelfEvaluation,
    onSelfEvaluate,
    handleContinueReview,
    showSubmitDialog,
    submitDialogType,
    handleContinueSubmit,
    handleCancelSubmit,
    showInputDialog,
    inputDialogValue,
    setInputDialogValue,
    openInputDialog,
    closeInputDialog,
    confirmInputDialog,
    inputDialogIndex,
    textareaAutoResize,
    handleSelfEvaluate,
    triggerNativeImageUpload,
    isEnglishFillBlank,
    canSelfEvaluate,
    getSelfEvaluationData,
    isAllSelfEvaluated,
    hasValidAnswers,
    shouldShowSelfEvaluation,
  };
}
