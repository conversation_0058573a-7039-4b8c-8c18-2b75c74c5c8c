import commonFetcher, { fetchFile, fetchStream } from "@repo/lib/utils/fetcher";
import * as Sentry from "@sentry/nextjs";
import { getNetworkHeaderParams } from "./device";

const apiHost = process.env.NEXT_PUBLIC_API_HOST;
console.info("STU:API_HOST", apiHost);

const fetcher = async <T>(
  url: string,
  prefix: string = "/study-api",
  init?: RequestInit,
  isStream?: boolean
) => {
  const isCdnUrl = url.includes("https://") && url.includes("static.");

  if (isCdnUrl) {
    return await fetchFile<T>(url);
  }

  const headerParams = getNetworkHeaderParams() ?? {
    // const headerParams = {
    // DEBUG用
    Authorization: `Bearer ${process.env.NEXT_PUBLIC_TEST_TOKEN}`,
    organizationId: `${process.env.NEXT_PUBLIC_ORGANIZATION_ID}`,
    userTypeId: "1",
  };

  if (isStream) {
    return fetchStream(`${apiHost}${prefix}${url}`, {
      ...init,
      headers: {
        ...init?.headers,
        ...headerParams,
      },
    }).catch((err) => {
      Sentry.captureException(err, {
        level: "warning",
        extra: {
          请求路径: url,
          请求体: init?.body,
          响应trace_id: err.traceId,
          HTTP状态: err.httpCode ?? "没有httpStatus",
        },
      });
      // return Promise.reject(err);
      // toast.error(err?.message?.message || err?.message || "网络请求失败");
      throw err;
    }) as T;
  }

  return commonFetcher<T>(`${apiHost}${prefix}${url}`, {
    ...init,
    headers: {
      ...init?.headers,
      ...headerParams,
    },
  }).catch((err) => {
    Sentry.captureException(err, {
      level: "warning",
      extra: {
        请求路径: url,
        请求体: init?.body,
        响应trace_id: err.traceId,
        HTTP状态: err.httpCode ?? "没有httpStatus",
      },
    });
    // return Promise.reject(err);
    // toast.error(err?.message?.message || err?.message || "网络请求失败");
    throw err;
  });
};

export async function get<T>(
  url: string,
  { query, prefix }: { query?: Record<string, string>; prefix?: string }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  const separator = params ? (url.includes("?") ? "&" : "?") : "";
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(
    `${url}${separator}${params?.toString() || ""}`,
    prefix,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

export async function post<T>(
  url: string,
  { arg, prefix }: { arg?: object; prefix?: string }
) {
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(url, prefix, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: arg ? JSON.stringify(arg) : undefined,
  });
}

export async function deleted<T>(
  url: string,
  { query, prefix }: { query?: Record<string, string>; prefix?: string }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  const separator = params ? (url.includes("?") ? "&" : "?") : "";
  // const isFullUrl = /^https?:\/\//.test(url);
  // const finalUrl = isFullUrl ? url : `${prefix ?? ApiPrefix}${url}`;
  return await fetcher<T>(
    `${url}${separator}${params?.toString() || ""}`,
    prefix,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

export function stream(
  url: string,
  { arg, prefix }: { arg?: object; prefix?: string }
) {
  const controller = new AbortController();
  const signal = controller.signal;

  return [
    fetcher<AsyncGenerator<string, void, unknown>>(
      url,
      prefix,
      {
        method: "POST",
        signal,
        headers: {
          "Content-Type": "application/json",
        },
        body: arg ? JSON.stringify(arg) : undefined,
      },
      true
    ),
    controller,
  ] as const;
}

export default fetcher;
