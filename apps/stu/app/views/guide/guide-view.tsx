import { FC } from "react";

import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import { WidgetViewProps } from "@/types/app/ui";
import Chat from "../chat/chat-view";
import { useCourseViewContext } from "../course/course-view-context";
import { GuideCommentView } from "./guide-comment-view";
import { GuideControllerView } from "./guide-controller-view";
import { GuideMenuView } from "./guide-menu-view";
import { GuidePlayerView } from "./guide-player-view";
import {
  GuideViewContextProvider,
  useGuideViewContext,
} from "./guide-view-context";

export const GuideWidgetView: FC<WidgetViewProps<"guide">> = ({
  totalGuideCount,
  active,
  content,
}) => {
  return (
    <GuideViewContextProvider
      content={content}
      active={active}
      totalGuideCount={totalGuideCount}
    >
      <GuideViewContent />
    </GuideViewContextProvider>
  );
};

// 将内容组件分离，这样可以在Context内部使用hooks
const GuideViewContent: FC = () => {
  const guideContext = useGuideViewContext();
  const { isPlaying, togglePlay, refPlayer, active } = guideContext;
  const { lessonVersion } = useCourseViewContext();
  const chatViewModel = useChatViewModel({
    isPlaying,
    togglePlay,
    refPlayer,
    lessonVersion,
  });

  return (
    <>
      <GuideMenuView chatViewModel={chatViewModel} />
      <GuidePlayerView />
      <GuideControllerView />
      {active && <GuideCommentView />}
      <Chat viewModel={chatViewModel} guideContext={guideContext} />
      {/* <Watcher /> */}
    </>
  );
};
