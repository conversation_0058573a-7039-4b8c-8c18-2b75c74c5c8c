"use client";
import { toast } from "@/app/components/common/toast";
import { useClientContext } from "@/app/providers/client-provider";
import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";
import { Guide } from "@repo/core/guide/guide";
import { Reference } from "@repo/core/types/data/comment";
import { GuideMode, GuideTheme } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { FC, useCallback, useEffect } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { useGuideViewContext } from "./guide-view-context";

const VolcengineVideo = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

interface GuidePlayerViewProps {
  className?: string;
  theme?: GuideTheme;
}

export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  className,
  theme,
}) => {
  const { studentUserInfo, screen } = useClientContext();

  const {
    showSubtitle,
    playRate,
    refPlayer,
    refVolcenginePlayer,
    title,
    totalGuideCount,
    index,
    data,
    guideMode,
    setFollowMode,
    seekTo,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    togglePlay,
    trackEventWithLessonId,
    refContainer,
    commentRef,
    ranges,
    referenceList,
    onClickReference,
    goto,
    active,
    durationInFrames,
  } = useGuideViewContext();

  const showLastProgressToast = useSignal(false);
  const isComment = useSignal(false);

  const longPressHandlers = useLongPress(
    (e) => {
      // todo)): 这里传的太麻烦，我先用dom直接弄了
      const [...doms] = document.querySelectorAll("[data-name=line-container]");
      if (doms.some((dom) => dom.contains(e.target as HTMLElement))) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
        trackEventWithLessonId("doc_fast_forward_longpress");
      },
      detect: LongPressEventType.Touch,
    }
  );

  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay, trackEventWithLessonId]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  const handleLineClick = (frame: number) => {
    seekTo(frame);
    setFollowMode();
    trackEventWithLessonId("doc_learn_from_here_click");
  };

  const handleScrollNext = (index: number) => {
    goto(index);
  };

  useEffect(() => {
    if (!active) return;
    if (guideMode.value === GuideMode.free) return;
    if (showLastProgressToast.value === true) return;
    if (progress.frame > 0) {
      toast.show("已从上次进度开始学习");
    }
    showLastProgressToast.value = true;
  }, [progress, showLastProgressToast, active, guideMode.value]);

  if (!data) {
    return <div>无数据</div>;
  }
  const { avatar } = data;
  const lineIdInRange = ranges[0]?.lineId;

  return (
    <div
      {...doubleTapHandlers}
      data-name="guide-player"
      className="relative h-screen w-full"
      {...longPressHandlers()}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ height: screen.height }}
        component={Guide}
        inputProps={{
          client: "stu",
          guideMode: guideMode.value,
          title,
          index,
          totalGuideCount,
          data,
          theme,
          showSubtitle: showSubtitle.value,
          onLineClick: handleLineClick,
          refContainer,
          commentRef,
          lineIdInRange,
          referenceList: referenceList as Reference[],
          onClickReference,
          onScrollFlip: handleScrollNext,
        }}
        initialFrame={progress.frame}
        durationInFrames={durationInFrames}
        fps={avatar.fps}
        playbackRate={playRate.value}
        allowFullscreen={false}
        compositionWidth={screen.width}
        compositionHeight={screen.height}
        // compositionWidth={1000}
        // compositionHeight={600}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
      {active && (
        <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
          <VolcengineVideo
            ref={refVolcenginePlayer}
            className="relative flex h-full w-full flex-col items-center justify-end"
            src={avatar.url}
            startTime={progress.frame / avatar.fps}
            playRate={playRate.value}
            userId={studentUserInfo?.userId}
            tag="文稿组件"
          />
        </div>
      )}
    </div>
  );
};
