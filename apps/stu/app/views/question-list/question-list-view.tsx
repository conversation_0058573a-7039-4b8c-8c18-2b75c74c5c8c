import { getStatusBarHeight } from "@/app/utils/device";
import { useQuestionListViewModel } from "@/app/viewmodels/question-list";
import IconArrowRightSvg from "@/public/icons/ic_arrow_right.svg";
import IconCheckSvg from "@/public/icons/ic_right.svg";
import NoDataIconSvg from "@/public/icons/question-list-no-data.svg";
import { BackButton, FormatMath } from "@repo/core/exercise/components";
import PartiallyCorrectIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_half_right.svg";
import CorrectIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_right.svg";
import WrongIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_wrong.svg";
import { useCallback, useRef } from "react";

export interface QuestionItem {
  id: number;
  type: string;
  content: string;
  status: "correct" | "wrong";
}

interface QuestionListProps {
  onBack: () => void;
}

interface OnlyWrongProps {
  onlyWrong: boolean;
  onChange: (checked: boolean) => void;
}

export const OnlyWrongCheckbox: React.FC<OnlyWrongProps> = ({
  onlyWrong,
  onChange,
}) => (
  <div className="relative flex items-center justify-end pl-4">
    <input
      type="checkbox"
      name="question-list"
      id="wrong"
      checked={onlyWrong}
      onChange={(e) => onChange(e.target.checked)}
      className="absolute left-0 top-1/2 z-20 -translate-y-1/2 cursor-pointer opacity-0"
    />
    {!onlyWrong ? (
      <span
        className="mr-1 inline-block h-4 w-4 rounded-full border-[1px] border-[rgba(31,35,43,0.75)]"
        onClick={() => onChange(!onlyWrong)}
      ></span>
    ) : (
      <IconCheckSvg onClick={() => onChange(!onlyWrong)} />
    )}
    <label
      htmlFor="wrong"
      className="ml-1 cursor-pointer select-none text-[13px]"
    >
      仅看错题
    </label>
  </div>
);

export const QuestionList: React.FC<QuestionListProps> = ({ onBack }) => {
  const {
    questions,
    isLoading,
    error,
    analysisId,
    handleViewAnalysis,
    handleCloseAnalysis,
    onlyWrong,
    handleOnlyWrongChange,
  } = useQuestionListViewModel();
  const statusBarHeight = useRef(getStatusBarHeight());

  const handleBack = useCallback(() => {
    if (onBack) {
      onBack();
    }
  }, [onBack]);

  return (
    <div
      className="question-list relative flex h-screen flex-col bg-[var(--study-background)]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="relative flex justify-between px-8 py-3">
        <div className="flex h-10 w-[9rem] items-center">
          <BackButton onClick={handleBack} />
          <span className="ml-3 text-[17px] font-medium">题目列表</span>
        </div>
        <OnlyWrongCheckbox
          onlyWrong={onlyWrong}
          onChange={handleOnlyWrongChange}
        />
      </div>
      <div className="flex-1 overflow-auto">
        <div className="mx-20 space-y-4">
          {isLoading && questions.length === 0 ? (
            <div className="mx-10 mb-5 mt-5 flex h-[calc(100vh-140px)] flex-col items-center justify-center rounded-xl border-[1px] border-[rgba(51,46,41,0.06)] bg-white p-4 text-center">
              <span className="mt-4 text-[15px] text-gray-500">加载中...</span>
            </div>
          ) : questions.length > 0 ? (
            <>
              {questions.map((q, index) => (
                <div
                  key={q.questionId}
                  className="mb-4 rounded-lg border border-[rgba(51,46,41,0.06)] bg-white"
                >
                  <div className="mb-2 flex items-start p-6">
                    <span className="mr-2 text-gray-500">{index + 1}</span>
                    <span className="mr-2 flex-shrink-0 rounded bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
                      {q.type}
                    </span>
                    <span className="relative -top-1 line-clamp-2 text-[17px] font-medium text-[#1F232B]">
                      <FormatMath htmlContent={q.content} questionId={""} />
                    </span>
                  </div>
                  <p className="mx-10 h-[1px] border-t-[1px] border-[#332E290F]"></p>
                  <div className="flex items-center p-6">
                    {q.status === "correct" ? (
                      <span className="mr-4 flex items-center font-bold text-green-600">
                        <CorrectIconSvg className="mr-2 h-5 w-5" />
                        回答正确
                      </span>
                    ) : q.status === "partially-correct" ? (
                      <span className="mr-4 flex items-center font-bold text-yellow-600">
                        <PartiallyCorrectIconSvg className="mr-2 h-5 w-5" />
                        回答部分正确
                      </span>
                    ) : (
                      <span className="mr-4 flex items-center font-bold text-red-600">
                        <WrongIconSvg className="mr-2 h-5 w-5" />
                        回答错误
                      </span>
                    )}
                    <button
                      className="ml-auto flex items-center text-sm text-[rgba(51,48,45,0.85)] hover:underline"
                      onClick={() => handleViewAnalysis(q.questionId)}
                    >
                      查看解析
                      <IconArrowRightSvg className="ml-1" />
                    </button>
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="mx-10 mb-5 mt-5 flex h-[calc(100vh-140px)] flex-col items-center justify-center rounded-xl border-[1px] border-[rgba(51,46,41,0.06)] bg-white p-4 text-center">
              <div className="flex flex-col items-center">
                <NoDataIconSvg />
                <span className="mt-1 text-[15px]">{"暂无题目"}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
