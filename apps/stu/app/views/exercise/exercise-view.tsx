"use client";

import { toast } from "@/app/components/common/toast";
import { StudyType } from "@repo/core/enums";
import { BackButton, TimerDisplay } from "@repo/core/exercise/components";
import { ProgressBar } from "@repo/core/exercise/components/ProgressBar";
import { ApiGetNextQuestionData } from "@repo/core/exercise/model";
import { StudyTypeThemeProvider } from "@repo/core/exercise/theme";
import { isObjectiveQuestionType } from "@repo/core/exercise/utils/question/question-viewmodel-utils";

import { useAskQuestion } from "@/app/models/exercise/exercise-model";
import { listenDeviceBackAction } from "@repo/core/utils/stu/device";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo } from "react";
import {
  QuestionContextProvider,
  useQuestionContext,
} from "../../contexts/question-context";
import { useExitViewModel } from "../../viewmodels/exercise/exit-viewmodel";
import { useCourseViewContext } from "../course/course-view-context";
import { ExerciseConfirmDialog } from "./components/exercise-dialog";
import { ExerciseHeaderActionView } from "./components/header-action-view";
import { QuestionView } from "./question-view";
import { TransitionView } from "./transition-view";

function ExerciseViewContent() {
  // 🎯 从Context获取题目相关状态
  const {
    currentQuestion,
    questionState,
    timerControl,
    // 🔥 新增：使用 useProgressBar hook 的状态
    progressBarProps,
    studyType,
    studySessionId,
    onBack,
    getTimeSpent, // 🔥 新增：获取当前答题时长
    widgetIndex,
    transitionState,
    nextTransition,
  } = useQuestionContext();

  const { currentIndex } = useCourseViewContext();

  // 🔥 直接调用退出ViewModel，无状态耦合（必须在条件渲染之前调用）
  const exitViewModel = useExitViewModel();

  const { askData, askQuestion } = useAskQuestion({
    studySessionId: studySessionId || 0,
  });

  // 🔥 确保 askQuestion 只调用一次
  useEffect(() => {
    askQuestion();
  }, [askQuestion]);

  const handleBack = useCallback(() => {
    // 如果是AI课程，直接退出
    if (studyType === StudyType.AI_COURSE) {
      if (onBack) {
        onBack();
      }
      return;
    }

    // 其他类型通过ViewModel处理
    exitViewModel.handleExitRequest(
      studyType,
      studySessionId,
      currentQuestion?.questionId,
      onBack
    );
  }, [
    exitViewModel,
    studyType,
    studySessionId,
    currentQuestion?.questionId,
    onBack,
  ]);

  // 🔧 包装ViewModel的退出确认方法，添加onBack回调和答题进度
  const handleConfirmExitWithCallback = useCallback(async () => {
    const questionId = currentQuestion?.questionId;
    const answerDuration = getTimeSpent();

    await exitViewModel.handleConfirmExit(
      studyType,
      studySessionId,
      onBack,
      questionId,
      answerDuration,
      widgetIndex
    );
  }, [
    exitViewModel,
    studyType,
    studySessionId,
    onBack,
    currentQuestion?.questionId,
    getTimeSpent,
    widgetIndex,
  ]);

  useEffect(() => {
    const unlisten = listenDeviceBackAction((result) => {
      if (result.code === 0 && result.data?.event === "backPressed") {
        // 打开弹窗
        handleBack();
      }
    });

    return () => {
      unlisten();
    };
  }, [handleBack]);

  const showAskButton = useMemo(() => {
    if (!askData?.isShow) {
      return false;
    }
    // 对于客观题第一次答错时不显示问一问按钮
    if (questionState === "first_attempt_incorrect" && currentQuestion) {
      const isObjective = isObjectiveQuestionType(
        currentQuestion.questionType || undefined
      );
      if (isObjective) {
        return false;
      }
    }

    return questionState === "submitted" || questionState === "giving_up";
  }, [questionState, currentQuestion, askData?.isShow]);

  // 🔥 移除会改变布局的加载状态，让子组件自己处理
  // 保持组件结构一致，避免布局跳跃

  return (
    <>
      <div
        className="exercise-page relative flex h-screen flex-col gap-y-0"
        style={{ backgroundColor: "var(--study-background)" }}
      >
        <div className="pt-8.5 relative flex px-8 py-1">
          <div className="flex h-10 items-center">
            {/* 🔥 长按wrapper - 只处理长按，不影响BackButton原有点击 */}
            <div
              className="debug-back-wrapper relative"
              // // TODO: debugger 上线删除
              // {...useLongPress({
              //   onLongPress: toggleVConsole,
              // })}
            >
              <BackButton
                onClick={handleBack} // 保持原有点击功能
              />
            </div>
            <TimerDisplay
              timerControl={timerControl}
              className="ml-1 leading-6"
            />
          </div>
          {/* 进度条绝对居中定位 */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 translate-y-1/2">
            <ProgressBar
              {...progressBarProps}
              className="progress-bar-exercise"
            />
          </div>
          <div className="relative ml-auto flex justify-end gap-1">
            {/* 在已提交答案或首次提交错误时显示"猜你想问"入口 */}
            <ExerciseHeaderActionView
              widgetIndex={currentIndex?.value || 0}
              questionId={currentQuestion?.questionId || ""}
              showAskButton={showAskButton}
              showGuideButton={studyType === StudyType.AI_COURSE}
            />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <QuestionView />
        </div>

        {/* 🔥 简化：TransitionView作为平级组件，只负责基础反馈 */}
        <TransitionView
          currentTransition={transitionState.currentTransition}
          nextTransition={nextTransition}
        />
      </div>

      <ExerciseConfirmDialog
        config={{
          isVisible: exitViewModel.showExitConfirm,
          type: "exitConfirm",
          studyType: studyType,
          onConfirm: handleConfirmExitWithCallback,
          onCancel: () =>
            exitViewModel.handleCancelExit(
              studyType,
              studySessionId,
              currentQuestion?.questionId
            ),
          isLoading: exitViewModel.isExiting,
        }}
      />
    </>
  );
}

interface ExerciseViewProps {
  widgetIndex?: number;
  studySessionId?: number;
  studyType?: StudyType;
  onComplete?: (totalTimeSpent?: number) => void;
  onBack?: () => void;

  // 可选参数（直接模式时使用）
  questionData?: ApiGetNextQuestionData;
  // AI课程激活状态
  activeInCourse?: boolean;
}

export function ExerciseView({
  widgetIndex,
  studySessionId,
  studyType,
  questionData,
  onComplete,
  onBack,
  activeInCourse,
}: ExerciseViewProps) {
  const searchParams = useSearchParams();
  const _studyType =
    studyType || (searchParams.get("studyType") as unknown as StudyType);
  const _studySessionId =
    studySessionId ||
    parseInt(searchParams.get("studySessionId") as unknown as string);

  // consol.log(` _studyType`, _studyType);
  // consol.log(` _studySessionId`, _studySessionId);
  // // consol.log("url", window.location.href);
  if (!_studyType || !_studySessionId) {
    toast.error("studyType and studySessionId are required");
    // throw new Error("studyType and studySessionId are required");
  }

  return (
    <StudyTypeThemeProvider studyType={_studyType}>
      <QuestionContextProvider
        firstQuestionData={questionData}
        widgetIndex={widgetIndex}
        onBack={onBack}
        onComplete={onComplete}
        studyType={_studyType}
        studySessionId={_studySessionId}
        activeInCourse={activeInCourse}
      >
        <ExerciseViewContent />
      </QuestionContextProvider>
    </StudyTypeThemeProvider>
  );
}
