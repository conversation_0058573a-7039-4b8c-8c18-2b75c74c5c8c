"use client";

import { useFillBlankViewModel } from "@/app/viewmodels/exercise/fill-blank-question-viewmodel";
import {
  FeedbackType,
  QUESTION_TYPE,
  questionTypeEnumManager,
} from "@repo/core/enums";
import {
  QuestionContentComponent,
  QuestionExplanation,
} from "@repo/core/exercise/components";
import type {
  NextQuestionInfo,
  QuestionState,
} from "@repo/core/exercise/model";
import { AnswerMask } from "@repo/core/exercise/preview/view/question-view";
import { isSubjectiveQuestionType } from "@repo/core/exercise/utils/question/question-viewmodel-utils";
import { useScrollDetection } from "@repo/core/hooks/useScrollDetection";
import { cn } from "@repo/ui/lib/utils";
import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { useQuestionContext } from "../../contexts/question-context";
import { useQuestionSubmissionViewModel } from "../../viewmodels/exercise/question-submission-viewmodel";
import { ChoiceQuestionView } from "./components/choice-question-view";
import { ExerciseConfirmDialog } from "./components/exercise-dialog";
import { AnswerAreaView } from "./components/fill-blank-answer-area-view";
import { FillBlankQuestionContentView } from "./components/fill-blank-question-content-view";
import { QuestionActionButtons } from "./components/question-action-buttons";

// 🔧 题型渲染器 - 使用函数式方法替代复杂的类型映射
const renderQuestionByType = (
  questionType: QUESTION_TYPE,
  questionState: QuestionState,
  fillBlankViewModel: ReturnType<typeof useFillBlankViewModel>
): React.ReactNode => {
  const isReviewMode =
    questionState === "submitted" ||
    questionState === "giving_up" ||
    questionState === "awaiting_self_evaluation";

  switch (questionType) {
    case QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE:
    case QUESTION_TYPE.QUESTION_TYPE_MULTIPLE_CHOICE:
      return <ChoiceQuestionView questionState={questionState} />;

    case QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK:
      return (
        <AnswerAreaView
          viewModel={{
            ...fillBlankViewModel,
            isReview: isReviewMode,
          }}
          type="fill-blank"
        />
      );

    case QUESTION_TYPE.QUESTION_TYPE_QA:
      return (
        <AnswerAreaView
          viewModel={{
            ...fillBlankViewModel,
            isReview: isReviewMode,
          }}
          type="solution"
        />
      );

    case QUESTION_TYPE.QUESTION_TYPE_JUDGMENT:
      return (
        <div className="unsupported-question-type flex flex-col items-center justify-center p-8 text-center">
          <div className="text-text-2 mb-2 text-lg font-medium">
            判断题暂不支持
          </div>
          <div className="text-text-4 text-sm">该题型正在开发中，敬请期待</div>
        </div>
      );

    case QUESTION_TYPE.QUESTION_TYPE_CLOZE:
      return (
        <div className="unsupported-question-type flex flex-col items-center justify-center p-8 text-center">
          <div className="text-text-2 mb-2 text-lg font-medium">
            完型填空题暂不支持
          </div>
          <div className="text-text-4 text-sm">该题型正在开发中，敬请期待</div>
        </div>
      );

    case QUESTION_TYPE.QUESTION_TYPE_PARENT_CHILD:
      return (
        <div className="unsupported-question-type flex flex-col items-center justify-center p-8 text-center">
          <div className="text-text-2 mb-2 text-lg font-medium">
            综合题暂不支持
          </div>
          <div className="text-text-4 text-sm">该题型正在开发中，敬请期待</div>
        </div>
      );

    default: {
      const typeName =
        questionTypeEnumManager.getLabelByValue(questionType) ||
        `未知题型(${questionType})`;
      return (
        <div className="unsupported-question-type flex flex-col items-center justify-center p-8 text-center">
          <div className="text-text-2 mb-2 text-lg font-medium">
            当前题型不支持
          </div>
          <div className="text-text-4 text-sm">题型：{typeName}</div>
        </div>
      );
    }
  }
};

export const QuestionView: React.FC = () => {
  // 🎯 从统一Context获取所有数据和方法（包括转场功能）
  const {
    currentQuestion,
    questionState,
    lastSubmitResult,
    handleContinue,
    handleUncertainClick,
    handleGiveUpClick,
    handleSubmitClick,
    userAnswerData,
    isSubmitting,
    // 🔥 转场功能
    transitionState,
    executeTransitionSequence,
    clearTransitionRecords,
    studyType,
    studySessionId,
    // 🆕 自评状态管理
    setSelfEvaluationSubmitted,
    // 🆕 下一题状态
    hasNextQuestion,
    isProgressBarAnimating,
  } = useQuestionContext();

  // 添加过渡状态管理，减少页面闪烁
  const previousQuestionRef = useRef<NextQuestionInfo | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 多选题确认弹窗状态 - 将由QuestionActionButtons组件管理
  // const [showMultipleChoiceConfirm, setShowMultipleChoiceConfirm] =
  //   useState(false);

  // 🔧 新增：滚动位置保持
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);

  // ✅ 使用提交ViewModel
  const submissionViewModel = useQuestionSubmissionViewModel();

  // 🆕 滚动检测功能 - 监听底部滚动状态
  const { sentinels, hasOverflow, scrollState } = useScrollDetection(
    scrollContainerRef,
    [{ direction: "bottom", threshold: 0.1 }]
  );

  // 使用 useMemo 来稳定题目数据，避免不必要的重新渲染
  const stableQuestion = useMemo(() => {
    if (!currentQuestion) return null;

    // 如果题目ID发生变化，标记为过渡状态
    if (
      previousQuestionRef.current &&
      previousQuestionRef.current.questionId !== currentQuestion.questionId
    ) {
      setIsTransitioning(true);
      // 短暂延迟后取消过渡状态，让新题目平滑显示
      setTimeout(() => setIsTransitioning(false), 50);

      // 🔥 题目切换时清理已处理的转场记录
      clearTransitionRecords();
    }

    previousQuestionRef.current = currentQuestion;
    return currentQuestion;
  }, [currentQuestion, clearTransitionRecords]);

  const questionType = useMemo(() => {
    return (
      stableQuestion?.questionType || QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE
    );
  }, [stableQuestion]);

  const isShowMask = useMemo(() => {
    if (
      questionState === "answering" &&
      (questionType === QUESTION_TYPE.QUESTION_TYPE_FILL_IN_THE_BLANK ||
        questionType === QUESTION_TYPE.QUESTION_TYPE_QA)
    ) {
      return false;
    }
    return true;
  }, [questionState, questionType]);

  // ✅ 使用ViewModel处理业务逻辑 - 符合MVVM架构（必须在顶层调用）
  // 为了遵循 React Hooks 规则，总是调用 hook，但传入安全的默认值
  const fillBlankViewModel = useFillBlankViewModel(
    currentQuestion || {
      questionContent: {
        questionStem: "",
      },
      questionId: "",
      questionType: QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
    }
  );

  // 🔧 新增：滚动位置保持逻辑
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // 保存当前滚动位置
    const handleScroll = () => {
      scrollPositionRef.current = container.scrollTop;
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, []);

  // 🔧 新增：在组件更新后恢复滚动位置
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container && scrollPositionRef.current > 0) {
      // 使用 requestAnimationFrame 确保 DOM 更新完成后再恢复滚动位置
      requestAnimationFrame(() => {
        container.scrollTop = scrollPositionRef.current;
      });
    }
  });

  // 🔥 简化的转场处理逻辑 - 检查是否有下一题，没有则跳过动画直接完成
  const handleContinueWithTransitions = async () => {
    if (transitionState.isPlayingTransitions) {
      return;
    }

    const currentQuestionId = stableQuestion?.questionId;
    if (!currentQuestionId) {
      handleContinue();
      return;
    }

    // 🔧 检查是否有下一题
    const hasNextQuestion = lastSubmitResult?.hasNextQuestion ?? false;

    if (!hasNextQuestion) {
      handleContinue(); // 这会调用 onComplete 回调
      return;
    }

    // 获取转场数据
    const groupInfo = lastSubmitResult?.nextQuestionGroupInfo;
    const specialFeedbacks =
      lastSubmitResult?.specialFeedbacks?.filter(
        (item) =>
          item.type === FeedbackType.DifficultyDown ||
          item.type === FeedbackType.DifficultyUp
      ) || [];
    // 执行转场序列（只有在有下一题时才播放动画）
    await executeTransitionSequence({
      groupInfo: groupInfo || undefined,
      specialFeedbacks: specialFeedbacks,
    });

    // 转场播放完成或无需转场，切换题目
    handleContinue();
  };

  // ✅ 新的提交处理函数 - 使用提交ViewModel
  const handleSubmitButtonClick = () => {
    if (!stableQuestion) return;

    // 🆕 在awaiting_self_evaluation状态下，进行自评二次提交
    if (questionState === "awaiting_self_evaluation") {
      // 收集自评数据
      const selfEvaluations = fillBlankViewModel.getSelfEvaluationData();

      // 检查是否所有必要的空都已自评
      if (!fillBlankViewModel.isAllSelfEvaluated()) {
        console.warn("[QuestionView] 还有空未完成自评");
        return;
      }

      // 🆕 设置自评已提交状态，锁定自评按钮
      setSelfEvaluationSubmitted(true);

      // 进行二次提交，带上自评数据
      handleSubmitClick(userAnswerData, selfEvaluations);
      return;
    }

    // 常规提交逻辑
    const result = submissionViewModel.beforeSubmitCheck({
      question: stableQuestion,
      userAnswerData: userAnswerData,
      questionState: questionState,
      actualSubmitHandler: async (data) => {
        // 🆕 检查是否为主观题且没有有效答案，如果是则跳过自评
        const shouldSkipSelfEval = !fillBlankViewModel.hasValidAnswers();
        await handleSubmitClick(data, undefined, shouldSkipSelfEval);
        if (shouldSkipSelfEval && isSubjectiveQuestionType(questionType)) {
          // 构建空答案的自评数据：每个空都标记为 wrong (2)
          const emptyEvaluations = fillBlankViewModel.answers.map(
            () => 2 as const
          );
          await handleSubmitClick(data, emptyEvaluations, false); // 不跳过自评，提交自评数据
        }
      },
      additionalContext: {
        subjectiveAnswer:
          fillBlankViewModel.answers.length > 1
            ? fillBlankViewModel.answers.map((a) => a.value)
            : undefined, // 单空时不传递subjectiveAnswer，依赖userAnswerData.subjectiveAnswer
        imageFiles: fillBlankViewModel.imageFiles
          .filter((img) => img.status === "success" && img.file !== null)
          .map((img) => img.file as File),
        inputMode: fillBlankViewModel.inputMode,
      },
    });

    // 如果需要特殊处理（非自评填空题），使用填空题组件的对话框
    if (
      result.needsSpecialHandling &&
      result.specialHandlingType === "fillBlankDialog"
    ) {
      fillBlankViewModel.handleSubmitWithDialog(() =>
        handleSubmitClick(userAnswerData)
      );
    }
  };

  // 🔥 优化：保持布局结构一致，避免闪烁
  if (!stableQuestion) {
    // 保持相同的布局结构，但不显示内容，避免布局跳跃
    return (
      <div
        className={cn(
          "question-view font-resource-han-rounded relative flex h-full flex-col overflow-hidden opacity-0 transition-opacity duration-200"
        )}
      >
        {/* 保持结构但不显示内容 */}
      </div>
    );
  }

  // ✅ 使用Context中的答案完成度判断 - 已在ViewModel中实现具体的题型判断逻辑
  // TODO: 等后续错题本上线了以后再打开
  // const inWrongQuestionBank = stableQuestion?.isInWrongQuestionBank ?? false;

  return (
    <div
      className={cn(
        "question-view font-resource-han-rounded relative flex h-full flex-col overflow-hidden transition-opacity duration-200",
        isTransitioning ? "opacity-50" : "opacity-100"
      )}
    >
      <div className="question-content-wrapper flex flex-1 overflow-hidden pl-8">
        <QuestionContentComponent
          fillBlankQuestionContentView={
            <FillBlankQuestionContentView
              viewModel={{
                ...fillBlankViewModel,
                isReview:
                  questionState === "submitted" ||
                  questionState === "giving_up" ||
                  questionState === "awaiting_self_evaluation",
              }}
            />
          }
          questionId={stableQuestion.questionId}
          className="w-[50%] pr-5 pt-3"
          content={stableQuestion.questionContent.questionStem}
          type={questionType}
          questionSourceInfo={stableQuestion.questionTag}
          questionTags={stableQuestion.questionTags || []}
        />
        {/* 右侧答题区域 */}
        <div className="answer-area-wrapper flex w-[50%] flex-col rounded-xl">
          {/* Answer Area，给作答区域内部留出 pl-3 的间距, 方便呈现选择题的动画*/}
          <div
            ref={scrollContainerRef}
            className="scroll-container w-full flex-1 overflow-y-auto overflow-x-hidden pl-3 pr-8 pt-3"
          >
            {/* 答题区域 - 🔧 使用题型渲染器 */}
            {renderQuestionByType(
              questionType,
              questionState,
              fillBlankViewModel
            )}

            {/* Explanation Area - 基于 questionState 显示 */}
            {(questionState === "submitted" ||
              questionState === "giving_up" ||
              questionState === "awaiting_self_evaluation") &&
              stableQuestion.questionExplanation && (
                <div className="explanation-area mt-6 pb-4">
                  <QuestionExplanation
                    studyType={studyType}
                    questionId={stableQuestion.questionId}
                    explanation={stableQuestion.questionExplanation}
                    className="mt-3"
                    aiExplanation={stableQuestion.aiExplanation}
                    aiExplanationRecord={stableQuestion.aiExplanationRecord}
                    studySessionId={studySessionId}
                  />
                </div>
              )}

            {/* 🆕 滚动检测哨兵元素 */}
            {isShowMask && sentinels.bottom}
          </div>
          {/* 底部操作区域 - 使用新的QuestionActionButtons组件 */}
          <div className="action-buttons-wrapper z-1 sticky bottom-0 flex items-center justify-end gap-x-5 pb-9 pr-8 pt-[0.38rem]">
            {/* TODO: 等后续错题本上线了以后再打开 - 错题本按钮 */}
            {/* <Button
              size="lg"
              color="white"
              onClick={() =>
                handleToggleWrongQuestionBank(stableQuestion.questionId)
              }
              className={cn("w-auto px-4 py-2 text-sm")}
            >
              {inWrongQuestionBank ? "已加错题本" : "加入错题本"}
            </Button> */}

            <QuestionActionButtons
              isProgressBarAnimating={isProgressBarAnimating}
              questionState={questionState}
              isAnswerComplete={
                questionState === "awaiting_self_evaluation"
                  ? fillBlankViewModel.isAllSelfEvaluated()
                  : submissionViewModel.canSubmit(
                      stableQuestion,
                      userAnswerData,
                      questionState
                    )
              }
              studyType={studyType}
              isSubmitting={isSubmitting}
              onSubmit={handleSubmitButtonClick}
              onContinue={handleContinueWithTransitions}
              onUncertainClick={handleUncertainClick}
              onGiveUpClick={handleGiveUpClick}
              isPlayingTransitions={transitionState.isPlayingTransitions}
              shouldShowSelfEvaluation={fillBlankViewModel.shouldShowSelfEvaluation()}
              hasNextQuestion={hasNextQuestion}
            />
          </div>
          {/* 🆕 底部渐变遮罩层 - 只在有滚动且未滚动到底部时显示 */}
          {isShowMask && (
            <AnswerMask
              className="h-full w-full"
              bgColor={"var(--study-background)"}
              hasOverflow={hasOverflow}
              scrollState={scrollState}
            />
          )}

          {/* 确认对话框 */}
          <ExerciseConfirmDialog
            config={submissionViewModel.confirmationDialog}
          />
        </div>
      </div>
    </div>
  );
};

// 🔥 性能优化：使用 memo 避免不必要的重渲染
export const MemoizedQuestionView = memo(QuestionView);
