"use client";

import { FeedbackType } from "@repo/core/enums";
import type {
  GroupTransitionFeedbackData,
  SpecialFeedback,
} from "@repo/core/exercise/model/types";
import React, { useCallback, useEffect, useState } from "react";
import { CarelessFeedbackModal } from "./careless-feedback";
import { ContinuousCorrectFeedbackModal } from "./continuous-correct-feedback";
import { DifficultyDownFeedbackModal } from "./difficulty-down-feedback";
import { DifficultyUpFeedbackModal } from "./difficulty-up-feedback";
import { QuestionGroupTransition } from "./question-group-transition";
import { ResumeExerciseTransition } from "./resume-exercise-transition";

interface FeedbackModalProps {
  isOpen: boolean;
  feedbackConten?: SpecialFeedback; // 多个反馈内容列表
  groupInfo?: GroupTransitionFeedbackData;
  onClose?: () => void;
  autoCloseDelay?: number;
}

/**
 * 反馈弹层组件
 * 支持单个反馈或多个反馈按顺序自动播放
 */
export const FeedbackModal: React.FC<FeedbackModalProps> = ({
  autoCloseDelay = 2000,
  isOpen,
  feedbackConten,
  groupInfo,
  onClose,
}) => {
  const [isCurrentFeedbackOpen, setIsCurrentFeedbackOpen] = useState(false);

  // 确定要播放的反馈列表
  const groupInfoData = groupInfo || undefined;
  const currentFeedback = feedbackConten || undefined;

  // 处理当前反馈关闭
  const handleCurrentFeedbackClose = useCallback(() => {
    setIsCurrentFeedbackOpen(false);

    // 如果还有下一个反馈，播放下一个
    onClose?.();
  }, [onClose]);

  // 监听 isOpen 变化，重置状态
  useEffect(() => {
    if (isOpen && (currentFeedback || groupInfoData)) {
      console.log("groupInfoData", groupInfoData);

      setIsCurrentFeedbackOpen(true);
    } else {
      setIsCurrentFeedbackOpen(false);
    }
  }, [isOpen, currentFeedback, handleCurrentFeedbackClose, groupInfoData]);

  // 如果没有反馈内容或弹窗未打开，不渲染
  if (!isOpen || (!currentFeedback && !groupInfoData)) return null;

  // 渲染单个反馈组件的函数

  // 处理GroupTransitionFeedbackData类型
  if (groupInfoData) {
    return (
      <QuestionGroupTransition
        autoCloseDelay={autoCloseDelay}
        isOpen={isCurrentFeedbackOpen}
        onClose={handleCurrentFeedbackClose}
        groupName={groupInfoData.groupName || "新题组"}
        currentGroupCount={groupInfoData.groupCount}
        currentGroupIndex={groupInfoData.groupIndex}
        previousGroupName={groupInfoData.preGroupName}
      />
    );
  }

  // 处理FeedbackData类型
  const feedbackData = currentFeedback as SpecialFeedback;
  const feedbackType = feedbackData.type;

  const feedbackParams = {
    autoCloseDelay: autoCloseDelay,
    onClose: handleCurrentFeedbackClose,
    mainText: feedbackData.title || "",
    subText: feedbackData.content || "",
    isOpen: isCurrentFeedbackOpen,
  };

  const feedbackTypeMap: Record<FeedbackType, React.ReactNode> = {
    [FeedbackType.AnswerCarelessly]: (
      <CarelessFeedbackModal {...feedbackParams} />
    ),
    [FeedbackType.ContinuousCorrect]: (
      <ContinuousCorrectFeedbackModal {...feedbackParams} />
    ),
    [FeedbackType.DifficultyUp]: (
      <DifficultyUpFeedbackModal {...feedbackParams} />
    ),
    [FeedbackType.DifficultyDown]: (
      <DifficultyDownFeedbackModal {...feedbackParams} />
    ),
    [FeedbackType.Resume]: (
      <ResumeExerciseTransition {...feedbackParams} autoCloseDelay={1300} />
    ),
    [FeedbackType.GiveUp]: <></>,
  };

  return feedbackType ? feedbackTypeMap[feedbackType] : null;
};

// 导出恢复练习转场动画组件供外部直接使用
export { ResumeExerciseTransition };
