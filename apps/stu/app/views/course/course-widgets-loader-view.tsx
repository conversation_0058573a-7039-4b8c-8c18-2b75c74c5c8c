"use client";

// import ScrollCorner from "@/public/images/corner.svg";
import { DialogView } from "@/app/components/dialog/default-dialog";
import { useCourseWidgetModel } from "@/app/models/course-widget-model";

import { Loading } from "@/app/components/common/loading";
import IcPageError from "@/public/icons/page-error.svg";
import { CourseWidgetSummaryWithoutStatus } from "@/types/app/course";
import { useComputed } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import * as Sentry from "@sentry/nextjs";
import {
  ComponentProps,
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { ExerciseInCourseView } from "../exercise-in-course";
import { GuideWidgetView } from "../guide/guide-view";
import { InteractiveView } from "../interactive/interactive-view";
import { VideoWidgetView } from "../video/video-view";
import { useCourseViewContext } from "./course-view-context";
import { WidgetFlipAnimation } from "./widget-flip-animation";

const WidgetLoader: FC<
  {
    summary: CourseWidgetSummaryWithoutStatus;
  } & ComponentProps<"div">
> = ({ summary }) => {
  const {
    total,
    next,
    reportCostTime,
    knowledgeId,
    lessonId,
    nextQuestionParams,
    exerciseCompletedRecord,
    refWidgets,
    currentIndex,
  } = useCourseViewContext();
  const ref = useRef<HTMLDivElement | null>(null);
  const { name, index } = summary;
  const isActive = useComputed(() => {
    return index === currentIndex.value;
  });

  const shouldLoad = useComputed(() => {
    return (
      index === currentIndex.value ||
      index === currentIndex.value + 1 ||
      index === currentIndex.value - 1
    );
  });

  const { data, isLoading, error, refresh } = useCourseWidgetModel({
    knowledgeId,
    lessonId,
    summary: shouldLoad.value ? summary : undefined,
    nextQuestionParams,
  });

  useEffect(() => {
    if (ref.current) {
      const arr = [...refWidgets.value];
      arr[index] = ref.current;
      refWidgets.value = arr;
    }
  }, [ref, index, refWidgets, isLoading]);

  const handleComplete = useCallback(
    (totalTimeSpent?: number) => {
      if (totalTimeSpent) {
        reportCostTime(totalTimeSpent);
      }

      if (!exerciseCompletedRecord.value?.[data?.index || 0]) {
        const newExerciseCompleted = {
          ...exerciseCompletedRecord.value,
          [data?.index || 0]: true,
        };
        exerciseCompletedRecord.value = newExerciseCompleted;
      }

      console.log("handleComplete", {
        data,
        exerciseCompleted: exerciseCompletedRecord.value,
      });

      next();
    },
    [next, reportCostTime, data, exerciseCompletedRecord]
  );

  useEffect(() => {
    if (error) {
      Sentry.captureException(error, { level: "fatal" });
    }
  }, [error]);

  const widget = useMemo(() => {
    if (isLoading || !data) {
      return <Loading />;
    }

    if (error) {
      return (
        <div className="flex h-full w-full items-center justify-center bg-sky-50">
          <div className="flex w-full flex-col items-center justify-center gap-2 p-4">
            <IcPageError className="size-20" />

            <div className="justify-start self-stretch text-center font-['Resource_Han_Rounded_SC'] text-xl font-bold leading-normal text-zinc-800/70">
              出错啦
            </div>

            <div
              className="inline-flex h-11 items-center justify-center gap-1 rounded-xl bg-white px-4 outline-1 outline-offset-[-1px] outline-zinc-800/10"
              onClick={refresh}
            >
              <div className="h-4 w-16 justify-end text-center text-base font-medium leading-tight text-zinc-800/90">
                刷新
              </div>
            </div>

            <div className="justify-start self-stretch text-center font-['Resource_Han_Rounded_SC'] text-base font-normal leading-tight text-zinc-800/40">
              请尝试刷新页面
            </div>
          </div>
        </div>
      );
    }

    const { type } = data;
    if (type === "guide") {
      return (
        <GuideWidgetView
          content={data}
          active={isActive.value}
          totalGuideCount={total}
        />
      );
    }

    if (type === "exercise") {
      console.log("exerciseData", {
        data,
        exerciseCompletedRecord: exerciseCompletedRecord.value,
      });

      // 获取练习数据
      let exerciseData = data;
      if (exerciseCompletedRecord.value?.[data.index]) {
        exerciseData = {
          ...exerciseData,
          data: {
            ...exerciseData.data,
            hasNextQuestion: false,
          },
        };
      }

      return (
        <ExerciseInCourseView
          activeInCourse={isActive.value}
          widgetIndex={data.index}
          exerciseData={exerciseData}
          onComplete={handleComplete}
        />
      );
    }

    if (type === "interactive") {
      const interactiveData = data;
      return (
        <InteractiveView
          active={isActive.value}
          index={data.index}
          url={interactiveData.data.url}
          type={interactiveData.data.typeName}
          onReport={(e) => {
            console.log(e);
          }}
        />
      );
    }

    if (type === "video") {
      return (
        <VideoWidgetView
          content={data}
          active={isActive.value}
          totalGuideCount={total}
        />
      );
    }

    return (
      <div className="course-widget-unsupported">不支持的组件类型: {type}</div>
    );
  }, [
    error,
    isLoading,
    data,
    refresh,
    total,
    isActive.value,
    handleComplete,
    exerciseCompletedRecord,
  ]);

  return (
    <div
      ref={ref}
      data-name={`widget-${index}-${name}`}
      className={cn(
        "relative h-screen w-full overflow-y-auto overscroll-none bg-[#FAF8F6]"
        // summary.type === "video" && isActive && "bg-[#FAF8F6]"
      )}
    >
      <div className="h-0.25 w-full bg-transparent" />
      {widget}
    </div>
  );
};

const Widgets = memo(
  ({ widgetList }: { widgetList: CourseWidgetSummaryWithoutStatus[] }) => {
    if (widgetList.length === 0) {
      return null;
    }
    return (
      <>
        {widgetList.map((it) => {
          const { index } = it;
          return <WidgetLoader key={`widget-${index}`} summary={it} />;
        })}
      </>
    );
  }
);

const CourseWidgetsLoaderView: FC<ComponentProps<"div">> = () => {
  const {
    isVersionChanged,
    isLoading,
    error,
    widgetList,
    showFlipAnimation,
    flipDirection,
    completeFlipAnimation,
    refCourseContainer,
    exit,
  } = useCourseViewContext();

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-red-1 text-center text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <>
      <WidgetFlipAnimation
        show={showFlipAnimation}
        direction={flipDirection}
        onAnimationComplete={completeFlipAnimation}
      />
      <div
        ref={refCourseContainer}
        className="relative h-screen w-full transform-gpu overflow-y-scroll"
      >
        <DialogView
          title="当前课程内容已更新，请退出后重新进入课程"
          open={isVersionChanged.value}
          buttons={[
            {
              text: "确定",
              onClick: exit,
              color: "red",
            },
          ]}
        ></DialogView>
        <Widgets widgetList={widgetList} />
      </div>
    </>
  );
};

export { CourseWidgetsLoaderView };
