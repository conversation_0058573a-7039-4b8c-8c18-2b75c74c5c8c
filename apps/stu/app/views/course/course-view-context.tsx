"use client";
import { createContext, FC, ReactNode, useCallback, useContext } from "react";

import { exitLesson, trackEvent } from "@/app/utils/device";
import {
  CourseSequenceViewmodel,
  useCourseSequenceViewmodel,
} from "@/app/viewmodels/course/course-sequence-vm";
import { UserPreferences } from "@/types/app/course";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { GuideMode } from "@repo/core/types/data/widget-guide";

type CourseViewContextType = CourseSequenceViewmodel & {
  isProgressBarOpen: Signal<boolean>;
  guideMode: Signal<GuideMode>;
  showSubtitle: Signal<boolean>;
  playRate: Signal<number>; //文稿播放速率
  videoPlayRate: Signal<number>; //视频播放速率
  exit: () => void;
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  hadShownRedirectComment: Signal<boolean>;
  studySessionId: number;
  studyType: number;
  exerciseCompletedRecord: Signal<{
    [key: number]: boolean;
  }>;
  userPreferences: Signal<UserPreferences>;
};

const CourseViewContext = createContext<CourseViewContextType>(
  {} as CourseViewContextType
);

const useCourseViewContext = () => useContext(CourseViewContext);

interface CourseViewProviderProps {
  knowledgeId: number;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  children: ReactNode;
  redirectWidgetIndex?: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  studySessionId: number;
  studyType: number;
}

const CourseViewProvider: FC<CourseViewProviderProps> = ({
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  children,
  redirectCommentId,
  redirectCommentRootId,
  redirectReferenceId,
  redirectWidgetIndex,
  studySessionId,
  studyType,
}) => {
  // 获取数据及操作数据的方法
  const sequenceVm = useCourseSequenceViewmodel(
    knowledgeId,
    redirectWidgetIndex
  );

  const { lessonId, currentWidget, lessonVersion } = sequenceVm;
  const isProgressBarOpen = useSignal(false);
  const guideMode = useSignal(GuideMode.follow);
  const showSubtitle = useSignal(true);
  const playRate = useSignal(1);
  const videoPlayRate = useSignal(1);
  const hadShownRedirectComment = useSignal(false);
  const exerciseCompletedRecord = useSignal<{
    [key: number]: boolean;
  }>({});
  const userPreferences = useSignal<UserPreferences>({
    showPlayerConfigs: true,
  });

  const trackEventWithLessonId = useCallback(
    (eventID: string, needWidgetInfo?: boolean) => {
      const map = needWidgetInfo
        ? {
            widgetIndex: currentWidget?.index,
            widgetType: currentWidget?.type,
          }
        : {};
      trackEvent(eventID, {
        lesson_id: lessonId,
        ...map,
      });
    },
    [lessonId, currentWidget]
  );

  const exit = useCallback(() => {
    trackEventWithLessonId("lesson_exit_click", true);
    console.log("exit");
    exitLesson();
  }, [trackEventWithLessonId]);

  const value = {
    ...sequenceVm,
    isProgressBarOpen,
    guideMode,
    showSubtitle,
    playRate,
    videoPlayRate,
    exit,
    trackEventWithLessonId,
    subjectId,
    knowledgeName,
    lessonName,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    hadShownRedirectComment,
    studySessionId,
    studyType,
    exerciseCompletedRecord,
    lessonVersion,
    userPreferences,
  };
  return <CourseViewContext value={value}>{children}</CourseViewContext>;
};

export {
  CourseViewContext,
  CourseViewProvider,
  GuideMode,
  useCourseViewContext,
  type CourseViewContextType,
};
