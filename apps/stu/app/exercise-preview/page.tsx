"use client";

import { StudyType } from "@repo/core/enums";
import { ExercisePreview } from "@repo/core/exercise/preview/view/exercise-view";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense } from "react";
import SilentLoading from "../components/silent-loading";
import { useQuestionList } from "../models/question-list-model";

function ExercisePageContent() {
  const router = useRouter();
  // 从 url 中获取 studySessionId
  const searchParams = useSearchParams();
  const studySessionId = parseInt(searchParams.get("studySessionId") || "0");
  const currentQuestionId = searchParams.get("currentQuestionId");
  const onlyWrong = searchParams.get("onlyWrong") === "true";
  const source = searchParams.get("source");

  const {
    data: fullData,
    isLoading,
    error,
  } = useQuestionList({
    onlyWrong,
    studySessionId,
  });

  const questionList =
    fullData?.questions.map((question) => {
      const studentAnswer = fullData.studentAnswers.find(
        (answer) => answer.questionId === question.questionId
      );
      return {
        questionInfo: question,
        studentAnswer: studentAnswer,
      };
    }) || [];

  const handleBack = () => {
    router.replace(
      `/${source}?studySessionId=${studySessionId}&onlyWrong=${onlyWrong}`
    );
  };

  // 如果没有下一题，使用预览组件
  if (isLoading) {
    return (
      <div className="exercise-course-widget flex h-full w-full items-center justify-center">
        <div className="text-sm text-gray-500">加载题目数据中...</div>
      </div>
    );
  }

  if (error) throw error;

  return (
    <ExercisePreview
      progressDisplayMode="progress"
      questionList={questionList}
      questionId={currentQuestionId || undefined}
      studyType={StudyType.REINFORCEMENT_EXERCISE}
      onBack={handleBack}
      showContinueButton={false}
    />
  );
}

export default function ExercisePage() {
  return (
    <Suspense fallback={<SilentLoading />}>
      <ExercisePageContent />
    </Suspense>
  );
}
