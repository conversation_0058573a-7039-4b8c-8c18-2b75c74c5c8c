import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";
import path from "path";

const assetPrefix =
  process.env.NODE_ENV === "production"
    ? "https://static.xiaoluxue.cn/aipt"
    : process.env.NODE_ENV === "test"
      ? "https://static.test.xiaoluxue.cn/aipt"
      : undefined;

console.log("assetPrefix ==>", assetPrefix, process.env.NODE_ENV);
const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Document-Policy",
            value: "js-profiling",
          },
        ],
      },
    ];
  },
  productionBrowserSourceMaps: true,
  // reactStrictMode: false,
  transpilePackages: [
    "@repo/ui",
    "@repo/lib",
    "@repo/core",
    "@repo/rough-notation",
  ],
  outputFileTracingRoot: path.resolve(__dirname, "../../"),
  distDir: "dist",
  output: "standalone",
  assetPrefix,
  /* config options here */
  experimental: {
    swcPlugins: [
      [
        "@preact-signals/safe-react/swc",
        {
          // you should use `auto` mode to track only components which uses `.value` access.
          // Can be useful to avoid tracking of server side components
          mode: "auto",
        } /* plugin options here */,
      ],
    ],
    turbo: {
      rules: {
        "*.svg": {
          loaders: [
            {
              loader: "@svgr/webpack",
              options: {
                icon: false,
              },
            },
          ],
          as: "*.js",
        },
      },
    },
  },

  // 配置重写规则
  rewrites: async () => {
    return [
      {
        source: "/api/:path*",
        destination: `http://lesson-kit-api.local.xiaoluxue.cn/api/:path*`,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.aliyuncs.com",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.cn",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.com",
        port: "",
        search: "",
      },
    ],
  },
  webpack: (config) => {
    config.resolve.alias["micromark-extension-math"] =
      "micromark-extension-llm-math";
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: false,
          },
        },
      ],
    });
    console.log(process.env.NEXT_PUBLIC_API_HOST);
    return config;
  },
  turbopack: {
    resolveAlias: {
      "micromark-extension-math": "micromark-extension-llm-math",
      "preact/signals-react": "@preact-signals/safe-react",
    },
    rules: {
      "*.svg": {
        loaders: [
          {
            loader: "@svgr/webpack",
            options: {
              icon: false,
            },
          },
        ],
        as: "*.js",
      },
    },
  },
};
export default withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: "sentry",
  project: "aipt",
  sentryUrl: "https://sentry.xiaoluxue.com/",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
  sourcemaps: {
    disable: false,
    assets: ["**/*.js", "**/*.js.map"],
    ignore: ["**/node_modules/**"],
    deleteSourcemapsAfterUpload: true,
  },
});
