"use client";
import Link from "next/link";
import { Watermark } from "@/app/components/watermark";
import { useDailyWatermarkText } from "@/app/hooks/use-daily-watermark-text";

export default function Home() {
  // 获取水印文本（格式：AI产课+当天第一次访问的时间戳后4位）
  const watermarkText = useDailyWatermarkText();

  return (
    <div className="grid min-h-screen grid-rows-[20px_1fr_20px] items-center justify-items-center gap-16 p-8 pb-4 font-[family-name:var(--font-geist-sans)]">
      {/* 页面水印，覆盖整个首页 */}
      <Watermark text={watermarkText} />
      <main className="row-start-2 flex flex-col items-center gap-[32px] sm:items-start">
        <h1 className="text-4xl font-bold">课程生产工具.AI</h1>
        <Link href="/guide-set/editor">文稿组件生产</Link>
        <Link href="/guide-set/copy">复制文稿组件</Link>
        <Link href="/lesson-flow?lessonId=10">配课工具</Link>
        <Link href="/lesson-mgmt">课程管理工具</Link>
        <Link href="/lesson-preview?id=12">课程预览</Link>
        
      </main>
      <footer className="row-start-3 flex flex-wrap items-center justify-center gap-[24px] text-xs">
        ©GIL 2025
      </footer>
    </div>
  );
}
