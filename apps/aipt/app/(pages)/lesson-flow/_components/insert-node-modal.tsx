import { useState } from "react";
import { Label } from "@/app/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group";
import { Button } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/app/components/ui/dialog";
import { post } from "@/lib/fetcher";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Node, useReactFlow } from "@xyflow/react";
import emitter from "@/lib/emitter";

interface InsertNodeProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const InsertNode: React.FC<InsertNodeProps> = (props) => {
  const { getNode } = useReactFlow();
  const { onSubmit, setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数

  const [fromVal, setFromVal] = useState({
    type: "exercise",
  });

  const createWidget = async () => {
    const sourceNode = getNode(props.source) as Node;
    return post("/api/v1/lesson_widget/create", {
      arg: {
        lessonId,
        widgetType: fromVal.type,
        previous: sourceNode.data.index,
      },
    });
  };

  const onSubmitHandle = async () => {
    try {
      await createWidget(); // todo: 等待接口完成，放开注释
      onSubmit();
      setOpen(false);
      const sourceNode = getNode(props.source) as Node;
      emitter.emit("openEditModal", {
        lessonId,
        widgetType: fromVal.type,
        previous: sourceNode.data.index,
      });
    } catch (error: any) {
      toast.error(error.message);
    }
  };
  return (
    <div className="insert-node-modal">
      <div className="flex items-center gap-2">
        <div>组件类型：</div>
        <div>
          <RadioGroup
            defaultValue={fromVal.type}
            className="flex"
            onValueChange={(value) => {
              setFromVal({ ...fromVal, type: value });
            }}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="exercise" id="r1" />
              <Label htmlFor="r1">练习组件</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="video" id="r2" />
              <Label htmlFor="r2">视频组件</Label>
            </div>

            <div className="flex items-center space-x-2">
              <RadioGroupItem value="interactive" id="r3" />
              <Label htmlFor="r3">互动组件</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>

        <Button onClick={onSubmitHandle}>提交</Button>
      </div>
    </div>
  );
};

const InsertNodeModal = (props: any) => {
  const [open, setOpen] = useState(false);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{props.trigger}</DialogTrigger>
      <DialogContent className="bg-white">
        <DialogHeader>
          <DialogTitle>添加组件</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <InsertNode {...props} setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default InsertNodeModal;
