"use client";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";
import emitter from "@/lib/emitter";
import fetcher, { post } from "@/lib/fetcher";
import { genConnections, genNodes } from "@/lib/utils";
import SendImg from "@/public/send.svg";
import {
  Edge,
  EdgeTypes,
  Node,
  NodeTypes,
  ReactFlow,
  addEdge,
  useEdgesState,
  useNodesState,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useMemo, useRef, useState } from "react";
import { Toaster, toast } from "sonner";
import useSWR from "swr";
import CustomEdge from "./_components/custom-edge";
import CustomNodeExercise from "./_components/custom-node-exercise";
import CustomNodeGuide from "./_components/custom-node-guide";
import CustomNodeInteractive from "./_components/custom-node-interactive";
import CustomNodeTmpl from "./_components/custom-node-tmpl";
import CustomNodeVideo from "./_components/custom-node-video";
import EditExcercideModal from "./_components/edit-excercide-modal";
import EditInteractiveModal from "./_components/edit-interactive-modal";
import EditVideoModal from "./_components/edit-video-modal";
import ExerciseSheet from "./_components/exercise-sheet";
import InteractiveSheet from "./_components/interactive-sheet";
import PreviewModal from "./_components/preview-modal";
import ReleaceQsModal from "./_components/releace-qs-modal";
import { LessonFlowProvider } from "./_context/lesson-flow-context";

const FlowDemo = () => {
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const edgeTypes: EdgeTypes = { "custom-edge": CustomEdge };
  const nodeTypes: NodeTypes = {
    guide: CustomNodeGuide as any,
    exercise: CustomNodeExercise as any,
    video: CustomNodeVideo as any,
    interactive: CustomNodeInteractive as any,
    tmpl: CustomNodeTmpl as any,
  };
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);

  const url = "/api/v1/lesson_widget?lessonId=" + lessonId;
  const { data, mutate }: any = useSWR(url, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    revalidateIfStale: false,
    onSuccess: (data: any) => {
      const widgetList = data?.widgetList ?? [];
      if (widgetList.length > 0) {
        const nodes = genNodes(widgetList);
        const edges = genConnections(nodes);
        setNodes(nodes);
        setEdges(edges);
      }
    },
    onError(err: Error) {
      // toast.error(err.message);
    },
  });

  const onConnect = (params: any) => setEdges((eds) => addEdge(params, eds));

  const handlePublish = async () => {
    try {
      await post("/api/v1/lesson_widget/finish", { arg: { lessonId } });
      toast.success("操作完成");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("发生未知错误");
      }
    }
  };

  const [widgetIndex, setWidgetIndex] = useState<string | null>(null);
  useEffect(() => {
    const handleRefresh = (node: any) => {
      mutate();
    };

    const handleEditExercise = (widgetIndex: any) => {
      setOpenExerciseSheet(true);
      setWidgetIndex(widgetIndex);
    };

    const handleEditInteractive = (widgetIndex: any) => {
      setOpenInteractiveSheet(true);
      setWidgetIndex(widgetIndex);
    };

    emitter.on("refresh", handleRefresh);
    emitter.on("editExercise", handleEditExercise);
    emitter.on("editInteractive", handleEditInteractive);

    // 清理函数
    return () => {
      emitter.off("refresh", handleRefresh);
      emitter.off("editExercise", handleEditExercise);
      emitter.off("editInteractive", handleEditInteractive);
    };
  }, [mutate]);

  const [previewModal, setPreviewModal] = useState({
    lessonId: lessonId,
    open: false,
  });

  const [openExerciseSheet, setOpenExerciseSheet] = useState(false);
  const [openInteractiveSheet, setOpenInteractiveSheet] = useState(false);
  const [openEditExcercideModal, setOpenEditExcercideModal] = useState(false);
  const editExcercideModalProps = useRef<any>({});
  const [releaceQsModal, setReleaceQsModal] = useState(false);
  const [repleaceQsParams, setRepleaceQsParams] = useState<any>({});
  const [openEditVideoModal, setOpenEditVideoModal] = useState(false);
  const [openEditInteractiveModal, setOpenEditInteractiveModal] =
    useState(false);
  const editInteractiveModalProps = useRef({});
  const editVideoModalProps = useRef({});
  const interactiveNodes = useMemo(() => {
    return nodes.filter((node: any) => node.type === "interactive");
  }, [nodes]);
  useEffect(() => {
    const handleEditAll = (res: any) => {
      setOpenEditExcercideModal(true);
      editExcercideModalProps.current = res;
    };

    const handleRepleaceQs = (res: any) => {
      setReleaceQsModal(true);
      setRepleaceQsParams(res);
    };

    const handleOpenSheet = (arg: any) => {
      if (!openExerciseSheet) {
        setOpenExerciseSheet(true);
        setWidgetIndex(arg.data.index);
      }
    };

    const handleOpenInteractiveSheet = (arg: any) => {
      if (!openInteractiveSheet) {
        setOpenInteractiveSheet(true);
        setWidgetIndex(arg.data.index);
      }
    };

    const handleOpenEditModal = (arg: any) => {
      const isVideo = arg.widgetType === "video";
      const isExercise = arg.widgetType === "exercise";
      const isInteractive = arg.widgetType === "interactive";

      if (isVideo) {
        setOpenEditVideoModal(true);
        editVideoModalProps.current = {
          index: arg.previous + 1,
        };
      } else if (isExercise) {
        setOpenEditExcercideModal(true);
        editExcercideModalProps.current = {
          index: arg.previous + 1,
          exercise: "",
        };
      } else if (isInteractive) {
        setOpenEditInteractiveModal(true);
        editInteractiveModalProps.current = {
          index: arg.previous + 1,
          ...arg,
        };
      }
    };

    emitter.on("editAll", handleEditAll);
    emitter.on("repleaceQs", handleRepleaceQs);
    emitter.on("openSheet", handleOpenSheet);
    emitter.on("openEditModal", handleOpenEditModal);
    emitter.on("openInteractiveSheet", handleOpenInteractiveSheet);

    // 清理函数
    return () => {
      emitter.off("editAll", handleEditAll);
      emitter.off("repleaceQs", handleRepleaceQs);
      emitter.off("openSheet", handleOpenSheet);
      emitter.off("openEditModal", handleOpenEditModal);
      emitter.off("openInteractiveSheet", handleOpenInteractiveSheet);
    };
  }, [openExerciseSheet]);

  return (
    <LessonFlowProvider interactiveNodes={interactiveNodes}>
      <div className="flex h-screen w-screen flex-col">
        <div className="flex items-center justify-between bg-white p-4">
          <div className="flex items-center gap-4">
            <div className="text-lg font-bold">课程配置</div>
            <Separator orientation="vertical" />
            <div className="text-gray-500">
              课程名称：<span>{data?.lessonName}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="border border-indigo-400 text-indigo-400"
              onClick={() => {
                // setPreviewModal({ lessonId: lessonId, open: true });
                if (typeof window !== "undefined") {
                  window.open(`/lesson-preview?id=${lessonId}`, "_blank");
                }
              }}
            >
              预览本课
            </Button>
            <Button
              variant="outline"
              className="border border-indigo-400 text-indigo-400"
              onClick={handlePublish}
            >
              <SendImg />
              提交发布
            </Button>
          </div>
        </div>
        <div className="flex-1 bg-gray-100">
          <ReactFlow
            deleteKeyCode={null}
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            edgeTypes={edgeTypes}
            nodeTypes={nodeTypes}
          />
        </div>
      </div>
      <Toaster position="top-center" />

      <PreviewModal
        lessonId={previewModal.lessonId.toString()}
        open={previewModal.open}
        onClose={() => setPreviewModal({ lessonId: lessonId, open: false })}
      />
      <ExerciseSheet
        open={openExerciseSheet}
        onClose={() => setOpenExerciseSheet(false)}
        lessonId={String(lessonId)}
        widgetIndex={widgetIndex as string}
        refresh={mutate}
      />
      <InteractiveSheet
        open={openInteractiveSheet}
        onClose={() => setOpenInteractiveSheet(false)}
        lessonId={String(lessonId)}
        widgetIndex={widgetIndex as string}
        refresh={mutate}
      />
      <EditExcercideModal
        open={openEditExcercideModal}
        setOpen={setOpenEditExcercideModal}
        data={editExcercideModalProps.current}
      />
      <ReleaceQsModal
        open={releaceQsModal}
        setOpen={setReleaceQsModal}
        {...repleaceQsParams}
      />

      <EditVideoModal
        open={openEditVideoModal}
        setOpen={setOpenEditVideoModal}
        data={editVideoModalProps.current}
      />

      <EditInteractiveModal
        open={openEditInteractiveModal}
        setOpen={setOpenEditInteractiveModal}
        data={editInteractiveModalProps.current}
      />
    </LessonFlowProvider>
  );
};

const Page = () => {
  return (
    <Suspense>
      <FlowDemo />
    </Suspense>
  );
};

export default Page;
