import { get } from "@/app/utils/fetcher";
import { analyzeTextLayoutForH3Part } from "@/app/utils/text-layout-analysis";
import { useCallback } from "react";
import { useLineSplitEditorContext } from "../line-split-editor-context";

// API响应类型定义
interface SubColumnImageItem {
  resourceUrl: string;
  blockIndex: number;
  styleH3: number;
}

interface GuideWidgetInfoResponse {
  subColumnImages?: SubColumnImageItem[];
}

/**
 * 自动分栏布局的 Hook
 * 用于生成图片后自动应用分栏样式
 */
export const useAutoColumnLayout = () => {
  const { parts, updatePart, batchUpdateParts, manualRemoveColumn } =
    useLineSplitEditorContext();

  /**
   * 应用自动分栏布局（新增自动文字排版优先逻辑）
   * @param h3Id - 课件ID
   * @param partId - 组件ID
   */
  const applyAutoColumnLayout = useCallback(
    async (h3Id: string, partId: string) => {
      if (manualRemoveColumn) {
        // 用户手动删除过分栏，不再自动分栏
        return;
      }
      try {
        // 1. 获取 subColumnImages 数据 - 增加重试机制
        let subColumnImages: SubColumnImageItem[] = [];
        let retryCount = 0;
        const maxRetries = 5; // 最多重试5次
        const retryDelay = 2000; // 每次重试间隔2秒

        while (retryCount < maxRetries) {
          const result = (await get("/api/v1/guideWidget/info", {
            query: {
              guideWidgetId: partId,
              guideWidgetSetId: h3Id,
            },
          })) as GuideWidgetInfoResponse;

          subColumnImages = result?.subColumnImages || [];

          if (subColumnImages.length > 0) {
            break;
          }

          retryCount++;
          if (retryCount < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
          }
        }

        if (subColumnImages.length === 0) {
          return;
        }

        // 构造一个 map，方便通过 partIndex 查找图片和分栏样式
        const subColumnMap = new Map<
          number,
          { styleH3: number; resourceUrl: string }
        >();
        for (const imageData of subColumnImages) {
          const { blockIndex, resourceUrl, styleH3 } = imageData;
          const partIndex = blockIndex - 1;
          if (partIndex >= 0) {
            subColumnMap.set(partIndex, { styleH3, resourceUrl });
          }
        }

        // 一次性处理所有 part，优先自动文字排版，否则自动分栏，否则原样
        const updates: Array<{ partIndex: number; part: any[] }> = [];
        for (let i = 0; i < parts.length; i++) {
          const currentPart = parts[i];
          if (!currentPart) {
            updates.push({ partIndex: i, part: [] });
            continue;
          }

          // 检查是否已手动分栏或手动排版
          const hasManualColumnLayout = currentPart.some(
            (line: any) =>
              line.tag === "block" &&
              line.layout &&
              line.styleType &&
              !line.autoGenerated // 排除自动生成的block
          );
          const hasTextLayout = currentPart.some(
            (line: any) =>
              (line.tag === "ol" && line.styleText && line.styleText > 0) ||
              (line.tag === "block" && line.styleText === 3)
          );

          if (hasManualColumnLayout || hasTextLayout) {
            // 已手动分栏或手动排版，保持原样
            updates.push({ partIndex: i, part: currentPart });
            continue;
          }

          // 优先自动文字排版
          const textLayoutAnalysis = analyzeTextLayoutForH3Part(currentPart);
          if (textLayoutAnalysis.canUseTextLayout) {
            // 自动应用文字排版
            const applicableGroups = textLayoutAnalysis.groupResults.filter(
              (result) => result.canUseStyle
            );
            if (applicableGroups.length > 0) {
              // 先清理旧的样式3容器
              const newLines = currentPart.filter(
                (line: any) => !(line.tag === "block" && line.styleText === 3)
              );
              // 应用所有可用分组的排版
              applicableGroups.forEach((group) => {
                if (!group.olLines || group.olLines.length === 0) return;
                if (group.styleToApply === 1 || group.styleToApply === 2) {
                  // 样式1、2：给OL行和父级容器加styleText
                  group.olLines.forEach((olLine) => {
                    const lineIndex = newLines.findIndex(
                      (line: any) => line.id === olLine.id
                    );
                    if (lineIndex !== -1) {
                      newLines[lineIndex] = {
                        ...(olLine as any),
                        styleText: group.styleToApply,
                      } as any;
                      // 给父级容器加styleText
                      // 找到父级容器（h3、h4或block）
                      let parentIndex = -1;
                      for (let j = lineIndex - 1; j >= 0; j--) {
                        const line = newLines[j];
                        if (
                          line &&
                          (line.tag === "h3" ||
                            line.tag === "h4" ||
                            line.tag === "block")
                        ) {
                          parentIndex = j;
                          break;
                        }
                      }
                      if (parentIndex !== -1 && !!newLines[parentIndex]?.tag) {
                        const parentLine = newLines[parentIndex] as any;
                        newLines[parentIndex] = {
                          ...parentLine,
                          styleText: group.styleToApply,
                        } as any;
                      }
                    }
                  });
                } else if (group.styleToApply === 3) {
                  // 样式3：创建block容器包裹
                  const validOlItems = group.olLines
                    .map((ol) => ({
                      idx: newLines.findIndex((line: any) => line.id === ol.id),
                      ol,
                    }))
                    .filter((item) => item.idx !== -1);
                  if (validOlItems.length === 0) return;
                  const firstIndex = validOlItems[0]!.idx;
                  const wrapperId = `text-layout-style3-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                  const wrappedOLs = validOlItems.map((item) => ({
                    ...JSON.parse(JSON.stringify(item.ol)),
                    styleText: undefined,
                  }));
                  const wrapperLine = {
                    id: wrapperId,
                    tag: "block" as const,
                    level: newLines[firstIndex]?.level || 0,
                    inFrame: newLines[firstIndex]?.inFrame || 0,
                    outFrame: newLines[firstIndex]?.outFrame || 0,
                    content: wrappedOLs,
                    styleText: 3,
                  };
                  // 替换第一个OL行为包装容器，删除其余OL行
                  newLines[firstIndex] = wrapperLine;
                  validOlItems
                    .slice(1)
                    .map((item) => item.idx)
                    .sort((a, b) => b - a)
                    .forEach((idx) => newLines.splice(idx, 1));
                }
              });
              updates.push({ partIndex: i, part: newLines });
              continue;
            }
          }

          // 自动分栏（如果有图片和分栏样式）
          const subCol = subColumnMap.get(i);
          if (subCol) {
            const processedPart = await processColumnLayoutToBlock(
              i,
              subCol.styleH3,
              subCol.resourceUrl
            );
            if (processedPart) {
              updates.push({ partIndex: i, part: processedPart });
              continue;
            }
          }

          // 否则保持原样
          updates.push({ partIndex: i, part: currentPart });
        }

        // 一次性批量更新所有parts，确保同时渲染
        if (updates.length > 0) {
          batchUpdateParts(updates);
        }
      } catch (error) {
        console.error("自动分栏布局失败:", error);
      }
    },
    [parts, batchUpdateParts, manualRemoveColumn]
  );

  /**
   * 处理分栏布局逻辑，返回处理后的part数据（不直接更新）
   */
  const processColumnLayoutToBlock = useCallback(
    async (
      partIndex: number,
      styleH3: number,
      imageUrl: string
    ): Promise<any[] | null> => {
      try {
        const currentPart = parts[partIndex];

        if (!currentPart) {
          return null;
        }

        // 样式配置映射
        const styleConfig = {
          1: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style1" as const,
          },
          2: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.75,
            styleType: "style2" as const,
          },
          3: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.65,
            styleType: "style3" as const,
          },
          4: {
            layout: "horizontal" as const,
            imageRatio: "9:16" as const,
            textRatio: 0.55,
            styleType: "style4" as const,
          },
          5: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style5" as const,
          },
        };

        const config = styleConfig[styleH3 as keyof typeof styleConfig];
        if (!config) {
          return null;
        }

        // 🎯 模拟手动分栏的"默认全选"逻辑
        // 计算所有可分栏的行索引（排除h3和已有的block）
        const selectableIndexes = new Set<number>();
        currentPart.forEach((line: any, index: number) => {
          if (line.tag !== "h3" && line.tag !== "block") {
            selectableIndexes.add(index);
          }
        });

        if (selectableIndexes.size === 0) {
          return null;
        }

        // 🔧 使用与手动分栏相同的逻辑创建分栏布局
        const result: any[] = [];
        let i = 0;

        while (i < currentPart.length) {
          if (selectableIndexes.has(i)) {
            // 找到连续选中的行
            const group: any[] = [];
            while (i < currentPart.length && selectableIndexes.has(i)) {
              const line = currentPart[i];
              if (line && typeof line === "object") {
                group.push(line);
              }
              i++;
            }

            // 将这些行合并为一个block
            if (group.length > 0) {
              const firstLine = group[0];
              const lastLine = group[group.length - 1];

              if (!firstLine || !lastLine) {
                continue;
              }

              const newBlock = {
                id: `auto-block-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                tag: "block" as const,
                level: firstLine?.level ?? 0,
                inFrame: firstLine?.inFrame ?? 0,
                outFrame: lastLine?.outFrame ?? 0,
                width: `${config.textRatio * 100}%`,
                layout: config.layout,
                imageRatio: config.imageRatio,
                styleType: config.styleType,
                autoGenerated: true, // 标记为自动生成
                pic: {
                  url: imageUrl,
                  width:
                    config.imageRatio === "16:9"
                      ? 400
                      : config.imageRatio === "1:1"
                        ? 300
                        : 200,
                  height:
                    config.imageRatio === "16:9"
                      ? 225
                      : config.imageRatio === "1:1"
                        ? 300
                        : 350,
                  fileType: "image" as const,
                },
                content: group
                  .filter((line) => line && typeof line === "object")
                  .map((line: any) => ({
                    ...line,
                    pic: undefined,
                  })), // 清除原始行的图片
              };

              result.push(newBlock);
            }
          } else {
            // 未选中的行直接添加（如H3标题）
            const line = currentPart[i];
            if (line) {
              result.push(line);
            }
            i++;
          }
        }

        return result;
      } catch (error) {
        console.error(`❌ H3块 ${partIndex + 1} 分栏样式处理失败:`, error);
        return null;
      }
    },
    [parts]
  );

  /**
   * 为指定的 H3 块应用分栏样式（直接更新）
   */
  const applyColumnLayoutToBlock = useCallback(
    async (partIndex: number, styleH3: number, imageUrl: string) => {
      try {
        const currentPart = parts[partIndex];

        if (!currentPart) {
          return;
        }

        // 样式配置映射
        const styleConfig = {
          1: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style1" as const,
          },
          2: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.75,
            styleType: "style2" as const,
          },
          3: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.65,
            styleType: "style3" as const,
          },
          4: {
            layout: "horizontal" as const,
            imageRatio: "9:16" as const,
            textRatio: 0.55,
            styleType: "style4" as const,
          },
          5: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style5" as const,
          },
        };

        const config = styleConfig[styleH3 as keyof typeof styleConfig];
        if (!config) {
          return;
        }

        // 🎯 模拟手动分栏的"默认全选"逻辑
        // 计算所有可分栏的行索引（排除h3和已有的block）
        const selectableIndexes = new Set<number>();
        currentPart.forEach((line: any, index: number) => {
          if (line.tag !== "h3" && line.tag !== "block") {
            selectableIndexes.add(index);
          }
        });

        if (selectableIndexes.size === 0) {
          return;
        }

        // 🔧 使用与手动分栏相同的逻辑创建分栏布局
        const result: any[] = [];
        let i = 0;

        while (i < currentPart.length) {
          if (selectableIndexes.has(i)) {
            // 找到连续选中的行
            const group: any[] = [];
            while (i < currentPart.length && selectableIndexes.has(i)) {
              const line = currentPart[i];
              if (line && typeof line === "object") {
                group.push(line);
              }
              i++;
            }

            // 将这些行合并为一个block
            if (group.length > 0) {
              const firstLine = group[0];
              const lastLine = group[group.length - 1];

              if (!firstLine || !lastLine) {
                continue;
              }

              const newBlock = {
                id: `auto-block-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                tag: "block" as const,
                level: firstLine?.level ?? 0,
                inFrame: firstLine?.inFrame ?? 0,
                outFrame: lastLine?.outFrame ?? 0,
                width: `${config.textRatio * 100}%`,
                layout: config.layout,
                imageRatio: config.imageRatio,
                styleType: config.styleType,
                autoGenerated: true, // 标记为自动生成
                pic: {
                  url: imageUrl,
                  width:
                    config.imageRatio === "16:9"
                      ? 400
                      : config.imageRatio === "1:1"
                        ? 300
                        : 200,
                  height:
                    config.imageRatio === "16:9"
                      ? 225
                      : config.imageRatio === "1:1"
                        ? 300
                        : 350,
                  fileType: "image" as const,
                },
                content: group
                  .filter((line) => line && typeof line === "object")
                  .map((line: any) => ({
                    ...line,
                    pic: undefined,
                  })), // 清除原始行的图片
              };

              result.push(newBlock);
            }
          } else {
            // 未选中的行直接添加（如H3标题）
            const line = currentPart[i];
            if (line) {
              result.push(line);
            }
            i++;
          }
        }

        // 🎯 更新H3部分的内容（保留H3标题，添加分栏块）
        updatePart(partIndex, result);
      } catch (error) {
        console.error(`❌ H3块 ${partIndex + 1} 分栏样式应用失败:`, error);
      }
    },
    [parts, updatePart]
  );

  return {
    applyAutoColumnLayout,
  };
};
