"use client";

import { But<PERSON> } from "@/app/components/common/button";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import { GuideStatus } from "@/types/base";
import { ImageIcon } from "lucide-react";
import { useCallback, useEffect } from "react";
import useSWRMutation from "swr/mutation";
import { useAutoColumnLayout } from "./hooks/use-auto-column-layout";

/**
 * 生成图片按钮 - 参考生成视频按钮实现
 */
export const GenerateImagesButton = () => {
  const { guide, isInit, isGenerating, refresh } = useGuideContext();
  const { videoGenTime } = guide;

  // 从 guide 中获取 genImageButtonActive 状态
  const genImageButtonActive = guide.genImageButtonActive;

  // 自动分栏布局 hook
  const { applyAutoColumnLayout } = useAutoColumnLayout();

  // 使用SWR处理接口调用 - 参考生成视频按钮的实现
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/produce/subColumn/images",
    post
  );

  // 判断按钮是否可用：视频已生成完成且不在生成中，且后端允许点击
  const isDisabled =
    isInit || isGenerating || !videoGenTime || genImageButtonActive === 0; // 后端控制：0不可点，1可点

  const handleGenerate = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId } = guide;

    // 检查前置条件
    if (guide.genImageButtonActive === 0) {
      return;
    }

    try {
      // 调用生成图片接口
      await trigger({
        guideWidgetId,
        guideWidgetSetId,
      });

      // 立即刷新数据以获取最新状态（这会触发全局 isGenerating 状态）
      refresh?.();
    } catch (error) {
      console.error("生成图片接口调用失败:", error);
    }
  }, [guide, refresh, trigger]);

  // 监听生成状态变化，当生成完成时自动应用分栏
  useEffect(() => {
    // 当生成状态从 true 变为 false 时，说明生成完成
    if (!isGenerating && guide.guideWidgetStatus !== GuideStatus.Loading) {
      const timer = setTimeout(async () => {
        try {
          await applyAutoColumnLayout(
            guide.guideWidgetSetId.toString(),
            guide.guideWidgetId.toString()
          );
        } catch (error) {
          console.error("自动分栏应用失败:", error);
        }
      }, 3000); // 延迟3秒确保数据已更新

      return () => clearTimeout(timer);
    }
  }, [
    isGenerating,
    guide.guideWidgetStatus,
    guide.guideWidgetSetId,
    guide.guideWidgetId,
    applyAutoColumnLayout,
  ]);

  return (
    <Button
      type="outline"
      disabled={isDisabled}
      loading={isMutating}
      onClick={handleGenerate}
      className="rounded-sm text-xs"
      icon={<ImageIcon className="size-4" />}
    >
      生成图片
    </Button>
  );
};
