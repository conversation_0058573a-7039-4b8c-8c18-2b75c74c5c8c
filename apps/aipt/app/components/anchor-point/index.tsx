"use client";

import {
  arrow,
  autoUpdate,
  flip,
  offset,
  shift,
  useFloating,
} from "@floating-ui/react";
import {
  Book,
  ChevronLeft,
  ChevronRight,
  Edit,
  FileText,
  Play,
  Volume2,
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

const LOCAL_KEY = "anchor-point-is-open";

const menuItems = [
  {
    icon: Play,
    label: "视频",
    anchorId: "video-section",
  },
  {
    icon: FileText,
    label: "逐字稿",
    anchorId: "transcript-section",
  },
  {
    icon: Volume2,
    label: "朗读稿",
    anchorId: "reading-section",
  },
  {
    icon: Edit,
    label: "板书",
    anchorId: "whiteboard-section",
  },
  {
    icon: Book,
    label: "分栏",
    anchorId: "column-section",
  },
  //   {
  //     icon: MessageCircle,
  //     label: "互动",
  //     anchorId: "interactive-section",
  //   },
];

const AnchorPoint = () => {
  // 简单 localStorage 记忆 isOpen
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== "undefined") {
      const stored = window.localStorage.getItem(LOCAL_KEY);
      if (stored !== null) return stored === "true";
    }
    return true;
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(LOCAL_KEY, String(isOpen));
    }
  }, [isOpen]);

  const [activeSection, setActiveSection] = useState<string>("");
  const arrowRef = React.useRef(null);
  const containerRef = React.useRef<HTMLElement | null>(null);
  const targetSectionRef = React.useRef<string | null>(null); // 记录目标区域
  const scrollCheckIntervalRef = React.useRef<NodeJS.Timeout | null>(null);


  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    middleware: [
      offset(-20),
      flip(),
      shift({ padding: 8 }),
      arrow({ element: arrowRef }),
    ],
    whileElementsMounted: autoUpdate,
    placement: "left",
  });

  // 监听父容器滚动事件，检测当前可见的区域
  const handleScroll = useCallback(() => {
    // 如果正在滚动到目标位置，暂不监听
    if (targetSectionRef.current) return;

    if (!containerRef.current) return;

    const container = containerRef.current;
    const sections = menuItems
      .map((item) => document.getElementById(item.anchorId))
      .filter(Boolean);

    let currentSection = "";
    const containerRect = container.getBoundingClientRect();
    const containerScrollTop = container.scrollTop;

    for (const section of sections) {
      if (section) {
        const rect = section.getBoundingClientRect();
        const sectionTop = rect.top - containerRect.top + containerScrollTop;

        // 如果区域在容器视口上半部分，则认为是当前活跃区域
        if (sectionTop <= containerScrollTop + 200) {
          currentSection = section.id;
        }
      }
    }

    setActiveSection(currentSection);
  }, []);

  // 检查是否到达目标位置
  const checkTargetReached = useCallback(() => {
    if (!targetSectionRef.current || !containerRef.current) return false;

    const targetElement = document.getElementById(targetSectionRef.current);
    if (!targetElement) return true; // 如果找不到元素，认为已到达

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    const elementRect = targetElement.getBoundingClientRect();

    // 检查目标元素是否在视口的合理位置（允许一定误差）
    const isInPosition = Math.abs(elementRect.top - containerRect.top) < 150;

    return isInPosition;
  }, []);

  // 开始检查是否到达目标位置
  const startTargetCheck = useCallback(
    (targetId: string) => {
      targetSectionRef.current = targetId;

      // 清除之前的检查
      if (scrollCheckIntervalRef.current) {
        clearInterval(scrollCheckIntervalRef.current);
      }

      // 定期检查是否到达目标位置
      scrollCheckIntervalRef.current = setInterval(() => {
        if (checkTargetReached()) {
          // 到达目标位置，恢复滚动监听
          targetSectionRef.current = null;
          if (scrollCheckIntervalRef.current) {
            clearInterval(scrollCheckIntervalRef.current);
            scrollCheckIntervalRef.current = null;
          }
          // 手动触发一次状态检测
          handleScroll();
        }
      }, 100);

      // 设置最大等待时间，避免无限等待
      setTimeout(() => {
        if (targetSectionRef.current === targetId) {
          targetSectionRef.current = null;
          if (scrollCheckIntervalRef.current) {
            clearInterval(scrollCheckIntervalRef.current);
            scrollCheckIntervalRef.current = null;
          }
          handleScroll();
        }
      }, 2000);
    },
    [checkTargetReached, handleScroll]
  );

  // 查找滚动容器并添加监听器
  useEffect(() => {
    // 查找最近的可滚动父元素
    const findScrollableParent = (element: HTMLElement): HTMLElement | null => {
      if (!element || element === document.body) return null;

      const { overflow, overflowY } = window.getComputedStyle(element);
      if (
        overflow === "auto" ||
        overflow === "scroll" ||
        overflowY === "auto" ||
        overflowY === "scroll"
      ) {
        return element;
      }

      return findScrollableParent(element.parentElement!);
    };

    // 从触发按钮开始查找滚动容器
    if (refs.reference.current) {
      const scrollContainer = findScrollableParent(
        refs.reference.current as HTMLElement
      );
      containerRef.current = scrollContainer || document.documentElement;
    } else {
      containerRef.current = document.documentElement;
    }

    const container = containerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll, { passive: true });
      handleScroll(); // 初始化检测
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, [handleScroll, refs.reference]);

  // 处理菜单项点击事件
  const handleMenuClick = (item: (typeof menuItems)[0]) => {
    console.log("点击菜单项:", item.label);

    // 立即设置当前活跃区域
    setActiveSection(item.anchorId);

    // 滚动到对应区域
    scrollToAnchor(item.anchorId);

    // 点击后关闭面板
    // setIsOpen(false);
  };

  // 滚动到指定锚点
  const scrollToAnchor = (anchorId: string) => {
    const element = document.getElementById(anchorId);
    if (element && containerRef.current) {
      // 开始目标位置检查
      startTargetCheck(anchorId);

      // 如果是 document.documentElement，使用 window 滚动
      if (containerRef.current === document.documentElement) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest",
        });
      } else {
        // 否则计算相对于容器的滚动位置
        const container = containerRef.current;
        const containerRect = container.getBoundingClientRect();
        const elementRect = element.getBoundingClientRect();

        const scrollTop =
          container.scrollTop + (elementRect.top - containerRect.top) - 100;

        container.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
      }
    } else {
      console.warn(`未找到锚点元素: ${anchorId}`);
    }
  };

  // 检查菜单项是否为活跃状态
  const isActiveItem = (item: (typeof menuItems)[0]) => {
    return activeSection === item.anchorId;
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollCheckIntervalRef.current) {
        clearInterval(scrollCheckIntervalRef.current);
      }
    };
  }, []);

  return (
    <>
      {/* 触发按钮 - 固定在右侧，面板展开时隐藏但保持DOM结构 */}
      <button
        ref={refs.setReference}
        onClick={() => setIsOpen(true)}
        className={`fixed right-4 top-1/2 z-50 -translate-y-1/2 rounded-lg border border-gray-200 bg-white p-3 shadow-lg transition-all duration-200 hover:shadow-xl ${isOpen ? "pointer-events-none opacity-0" : "opacity-100"
          }`}
      >
        <ChevronLeft className="h-5 w-5 cursor-pointer text-gray-600" />
      </button>

      {/* 浮动面板 */}
      {isOpen && (
        <div
          ref={refs.setFloating}
          style={floatingStyles}
          className="z-40 min-w-[160px] rounded-lg border border-gray-200 bg-white p-4 shadow-xl"
        >
          <ChevronRight
            className="mb-2 h-5 w-5 cursor-pointer text-gray-600"
            onClick={() => setIsOpen(false)}
          />
          {/* 菜单项 */}
          <div className="space-y-3 pl-4">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = isActiveItem(item);

              return (
                <div
                  key={index}
                  onClick={() => handleMenuClick(item)}
                  className={`flex cursor-pointer items-center gap-3 rounded-lg p-2 transition-colors hover:bg-gray-50 ${isActive
                    ? "border border-blue-200 bg-blue-50"
                    : "text-gray-600"
                    }`}
                >
                  <Icon
                    className={`h-5 w-5 ${isActive ? "text-blue-500" : "text-gray-600"
                      }`}
                  />
                  <span
                    className={`text-sm font-medium ${isActive ? "text-blue-500" : "text-gray-600"
                      }`}
                  >
                    {item.label}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </>
  );
};

export default AnchorPoint;
