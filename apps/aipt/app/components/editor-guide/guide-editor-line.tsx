import { LineTexture } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo, useRef } from "react";

import {
  H2,
  H3,
  H4,
  OL,
  Picture,
  TextureWithOutFrame,
  UL,
} from "@repo/core/guide/components/guide-elements";
import { LineTreeNode } from "@repo/core/guide/viewmodels/guide-tree-viewmodel";
import type { Picture as PictureType } from "@repo/core/types/data/base";
import { EditH3PartMenu } from "../line-split-editor/h3-part/h3-part-controls";

// 渲染不同类型文件的组件
const FileRenderer: FC<{
  pic: PictureType;
  className?: string;
  styleType?: string;
}> = ({ pic, className, styleType }) => {
  if (!pic) return null;

  const { url, fileType } = pic;
  // 兜底判断，保证.html结尾的url一定用iframe渲染
  const realFileType =
    fileType === "html" || (url && url.toLowerCase().endsWith(".html"))
      ? "html"
      : fileType;

  // 针对样式2/3，缩放比例更小，防止滚动条
  const isStyle2or3 = styleType === "style2" || styleType === "style3";
  const scale = isStyle2or3 ? 0.25 : 0.33;
  const percent = isStyle2or3 ? "400%" : "300%";

  // 如果是JS或HTML文件，使用InteractiveComponent
  if (realFileType === "js") {
    // 对于JS文件，创建一个包含脚本的HTML页面
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
          }
          .loading {
            text-align: center;
            color: #666;
            padding: 40px;
          }
        </style>
      </head>
      <body>
        <div class="loading">正在加载JS组件...</div>
        <script src="${url}"></script>
        <script>
          // 监听自定义元素注册
          const originalDefine = customElements.define;
          let registeredElements = [];

          customElements.define = function(name, constructor, options) {
            registeredElements.push(name);
            return originalDefine.call(this, name, constructor, options);
          };

          // 多次尝试检测和创建元素
          function tryCreateElement() {
            const loadingDiv = document.querySelector('.loading');

            // 首先尝试新注册的元素
            if (registeredElements.length > 0) {
              const elementName = registeredElements[registeredElements.length - 1];
              const element = document.createElement(elementName);
              document.body.appendChild(element);
              if (loadingDiv) loadingDiv.remove();
              return true;
            }

            // 然后尝试预设名称和常见模式
            const possibleNames = [
              'web-component-class4-subject1',
              'test-interactive',
              'interactive-component',
              'custom-element'
            ];

            for (const name of possibleNames) {
              if (customElements.get(name)) {
                const element = document.createElement(name);
                document.body.appendChild(element);
                if (loadingDiv) loadingDiv.remove();
                return true;
              }
            }

            return false;
          }

          // 多次尝试，因为JS加载可能需要时间
          let attempts = 0;
          const maxAttempts = 10;

          function attemptCreation() {
            attempts++;

            if (tryCreateElement()) {
              return;
            }

            if (attempts < maxAttempts) {
              setTimeout(attemptCreation, 200);
            } else {
              const loadingDiv = document.querySelector('.loading');
              if (loadingDiv) {
                loadingDiv.innerHTML = 'JS文件已加载，但未找到自定义元素。请确保JS文件中调用了 customElements.define()。<br>已注册的元素: ' + registeredElements.join(', ');
              }
            }
          }

          // 开始尝试
          setTimeout(attemptCreation, 100);
        </script>
      </body>
      </html>
    `;

    const htmlBlob = new Blob([htmlContent], { type: "text/html" });
    const htmlUrl = URL.createObjectURL(htmlBlob);

    return (
      <div
        className={cn(
          "relative h-full w-full overflow-hidden rounded-lg",
          className
        )}
      >
        <iframe
          src={htmlUrl}
          className="absolute inset-0 border-0"
          title="JS文件预览"
          sandbox="allow-scripts allow-same-origin"
          style={{
            width: percent,
            height: percent,
            transform: `scale(${scale})`,
            transformOrigin: "0 0",
            overflow: "hidden",
          }}
        />
      </div>
    );
  }

  if (realFileType === "html") {
    return (
      <div
        className={cn(
          "relative h-full w-full overflow-hidden rounded-lg",
          className
        )}
      >
        <iframe
          src={url}
          className="absolute inset-0 border-0"
          title="HTML预览"
          sandbox="allow-scripts allow-same-origin"
          style={{
            width: percent,
            height: percent,
            transform: `scale(${scale})`,
            transformOrigin: "0 0",
            overflow: "hidden",
          }}
        />
      </div>
    );
  }

  // 默认图片渲染
  return <img src={url} alt="" className={className} />;
};
// import { HandDrawn } from "./guide-hand-drawn";
// import { SketchBoard } from "./sketch-board";

export const GuideEditorLine: FC<{
  data: LineTreeNode;
  className?: string;
  editable?: boolean;
  root?: boolean;
  parentStyleText?: number;
}> = ({ data, editable = false, root = false, parentStyleText }) => {
  // const { selectedLineId } = useGuideViewContext();
  const { id, line, index, children } = data;
  const { tag, content, pic, width, layout, imageRatio, styleText } = line;
  // console.log("Line", { ...data });
  const refLine = useRef<HTMLDivElement>(null);
  // const { canvasRef, strokeColor, strokeWidth } = useSketchCanvasRef();

  // 使用和 GuideLine 完全一致的样式实现，确保编辑区和预览区样式统一
  const childrenClassName = useMemo(() => {
    if (styleText === 1) return cn("flex w-full flex-col");
    if (styleText === 2) return cn("flex w-full flex-col gap-5");
    if (styleText === 3)
      return cn("w-full grid gap-5 px-6 py-7 rounded-3xl bg-[#F3F3F3]", {
        "grid-cols-3": children.length > 2,
        "grid-cols-2": children.length <= 2,
      });
    return cn("flex w-full flex-col gap-4"); // 🔧 修复：改为和 GuideLine 一致的 gap-4
  }, [children.length, styleText]);

  const compContent = useMemo(() => {
    if (!content) return null;
    if (tag === "block") return null;

    const result = content.map((it, idx) => {
      return (
        <TextureWithOutFrame
          lineId={id}
          key={`${id}:${idx}:${styleText || 0}`}
          data={it as LineTexture}
          styleText={styleText}
        />
      );
    });

    return result;
  }, [id, content, tag, styleText]);

  const compWrapper = useMemo(() => {
    switch (tag) {
      case "h2":
        return <H2 content={compContent} />;
      case "h3":
        return <H3 content={compContent} />;
      case "h4":
        return <H4 content={compContent} />;
      case "ul":
        return <UL content={compContent} />;
      case "ol":
        return (
          <OL
            index={index + 1}
            content={compContent}
            styleText={styleText || parentStyleText}
          />
        );
      case "block":
        return null;
      default:
        return (
          <div className="text-lg font-medium leading-9 tracking-wider text-yellow-950">
            {compContent}
          </div>
        );
    }
  }, [compContent, tag, index, styleText, parentStyleText]);

  if (tag === "block") {
    return (
      <div
        data-name="block-wrapper"
        data-id={id}
        className={cn(
          "flex w-full",
          layout === "vertical" ? "flex-col" : "flex-row items-start",
          !root && "gap-6 pb-5"
        )}
      >
        {layout === "vertical" ? (
          // 上下布局：文字在上，图片在下
          <>
            <div className="w-full">
              {children && children.length > 0 && (
                <div data-name="children" className={childrenClassName}>
                  {children.map((it) => (
                    <GuideEditorLine
                      key={it.id}
                      data={it}
                      editable={editable}
                      parentStyleText={styleText}
                    />
                  ))}
                </div>
              )}
            </div>

            {pic && (
              <div className="mt-0 w-full">
                <div
                  className="relative w-full"
                  style={{
                    aspectRatio:
                      imageRatio === "16:9"
                        ? "16/9"
                        : imageRatio === "1:1"
                          ? "1/1"
                          : "9/16",
                  }}
                >
                  <FileRenderer
                    pic={pic}
                    styleType={line.styleType}
                    className="h-full w-full rounded-lg object-cover object-center"
                  />
                </div>
                {!root && editable && !styleText && (
                  <div className="mt-2">
                    <EditH3PartMenu lineId={id} styleType={line.styleType} />
                  </div>
                )}
              </div>
            )}

            {/* 如果没有图片，也要显示按钮 */}
            {!pic && !root && editable && !styleText && (
              <div className="mt-4">
                <EditH3PartMenu lineId={id} styleType={line.styleType} />
              </div>
            )}
          </>
        ) : (
          // 左右布局：文字在左，图片在右
          <>
            <div style={{ width }}>
              {children && children.length > 0 && (
                <div data-name="children" className={childrenClassName}>
                  {children.map((it) => (
                    <GuideEditorLine
                      key={it.id}
                      data={it}
                      editable={editable}
                      parentStyleText={styleText}
                    />
                  ))}
                </div>
              )}
            </div>
            <div className={cn("flex-1")}>
              <div className="w-full">
                <div
                  className={cn(
                    "w-full",
                    imageRatio === "9:16"
                      ? pic?.fileType === "html" || pic?.fileType === "js"
                        ? "aspect-[9/16] min-h-[400px]" // HTML/JS文件使用固定宽高比和最小高度
                        : "h-fit max-h-full" // 普通图片保持原有逻辑
                      : imageRatio === "1:1"
                        ? pic?.fileType === "html" || pic?.fileType === "js"
                          ? line.styleType === "style2"
                            ? "aspect-square min-h-[300px]" // 样式2: 75%文字，25%图片，图片相对较小
                            : line.styleType === "style3"
                              ? "aspect-square min-h-[350px]" // 样式3: 65%文字，35%图片，图片相对较大
                              : "aspect-square min-h-[300px]" // 默认
                          : "aspect-square"
                        : "aspect-[16/9]"
                  )}
                >
                  {pic && (
                    <FileRenderer
                      pic={pic}
                      styleType={line.styleType}
                      className={cn(
                        "w-full rounded-lg object-contain object-top",
                        imageRatio === "9:16"
                          ? pic?.fileType === "html" || pic?.fileType === "js"
                            ? "h-full" // HTML/JS文件使用h-full
                            : "h-auto max-h-full" // 普通图片保持原有逻辑
                          : "h-full"
                      )}
                    />
                  )}
                </div>
                {!root && editable && !styleText && (
                  <div className="mt-2">
                    <EditH3PartMenu lineId={id} styleType={line.styleType} />
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div
      data-name="line-wrapper"
      className={cn(
        "relative z-10 flex w-full flex-col",
        //样式1的OL行不需要底部间距，其他情况保持pb-2
        !(tag === "ol" && styleText === 1) && "pb-2"
        // sketchProps?.mode === "draw" && "h-full"
      )}
    >
      <div
        data-name="line-container"
        ref={refLine}
        className={cn(
          "font-resource-han-rounded",
          styleText === 2 || parentStyleText === 2 ? "w-full" : "w-fit",
          ["h2", "h3", "h4"].includes(tag) && "my-2"
        )}
      >
        <div
          className={cn(
            styleText === 2 || parentStyleText === 2 ? "w-full" : "w-fit"
          )}
        >
          {compWrapper}
        </div>
      </div>
      <Picture data={pic} />
      {children && children.length > 0 && (
        <div data-name="children" className={childrenClassName}>
          {children.map((it) => (
            <GuideEditorLine
              key={it.id}
              data={it}
              editable={editable}
              parentStyleText={styleText}
            />
          ))}
        </div>
      )}
    </div>
  );
};
