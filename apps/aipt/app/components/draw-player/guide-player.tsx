"use client";

import { Player, PlayerRef } from "@remotion/player";
import {
  DrawElement,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useEffect, useMemo, useRef } from "react";
import { EmptyTip } from "../panels/empty-tip";
import { playbackRateOptions } from "../player/player-controls";
import { GuideView } from "./guide-view";

interface GuidePlayerProps {
  width: number;
  height: number;
  className?: string;
  data: GuideWidgetData | string;
  inFrame?: number;
  outFrame?: number;
  onDrawChange?: (paths: DrawElement[] | null) => void;
  eraserMode?: boolean;
  highlighter?: boolean;
  partIndex: number;
  totalPartsCount: number;
  controls?: boolean;
  numberOfSharedAudioTags?: number;
  mode?: "draw" | "edit";
  hideSubtitles?: boolean;
  initSeekFrame?: number;
}

const GuideDrawPlayer: FC<GuidePlayerProps> = ({
  width,
  height,
  className,
  data,
  inFrame = 0,
  outFrame,
  onDrawChange,
  eraserMode,
  highlighter,
  partIndex,
  totalPartsCount,
  controls = true,
  numberOfSharedAudioTags = 5,
  mode,
  hideSubtitles = false,
  initSeekFrame,
}) => {
  const playerRef = useRef<PlayerRef>(null);
  const guideData = useMemo(() => {
    if (typeof data === "string") {
      return data ? (JSON.parse(data) as GuideWidgetData) : null;
    }
    return data;
  }, [data]);

  useEffect(() => {
    const player = playerRef.current;
    if (!player || !outFrame) return;

    const handlePlay = () => {
      const currentFrame = player.getCurrentFrame();
      if (currentFrame >= outFrame) {
        player.seekTo(inFrame);
      }
    };

    player.addEventListener("play", handlePlay);

    return () => {
      player.removeEventListener("play", handlePlay);
    };
  }, [playerRef, inFrame, outFrame]);

  useEffect(() => {
    if (initSeekFrame) {
      playerRef.current?.seekTo(initSeekFrame);
      return;
    }
    if (!inFrame !== undefined && playerRef.current) {
      playerRef.current.seekTo(inFrame);
    }
  }, [inFrame, initSeekFrame]);


  // 监听播放器事件，限制拖拽范围
  useEffect(() => {
    const player = playerRef.current;
    if (!player || !outFrame) return;

    const handleTimeUpdate = () => {
      const currentFrame = player.getCurrentFrame();
      
      // 如果当前帧超出范围，自动跳回到有效范围内
      if (currentFrame < inFrame) {
        player.seekTo(inFrame);
      } else if (currentFrame > outFrame) {
        player.seekTo(outFrame);
      }
    };

    // 监听时间更新事件
    player.addEventListener("timeupdate", handleTimeUpdate);

    return () => {
      player.removeEventListener("timeupdate", handleTimeUpdate);
    };
  }, [playerRef, inFrame, outFrame]);

  if (!guideData) {
    return <EmptyTip texture="视频未生成" />;
  }
  // const guideData = localData as unknown as GuideWidgetData;

  const { avatar } = guideData;

  if (outFrame === 0) {
    return <EmptyTip texture="这个H3的时长为0, 不能播放" />;
  }

  if (outFrame && outFrame < inFrame) {
    return (
      <EmptyTip
        texture={`数据异常，outFrame: ${outFrame} 不能小于 inFrame: ${inFrame}`}
      />
    );
  }

  if (inFrame > avatar?.durationInFrames) {
    return <EmptyTip texture={`数据异常，inFrame: ${inFrame} 不能大于 durationInFrames: ${avatar?.durationInFrames}`} />;
  }

  const duration = outFrame && outFrame > inFrame ? outFrame - inFrame : 1;
  return (
    <Player
      ref={playerRef}
      className={cn("remotion-player-draw", className)}
      style={{ width: "100%" }}
      component={GuideView}
      inputProps={{
        data: guideData,
        startFrame: inFrame,
        outFrame,
        duration,
        onDrawChange,
        eraserMode,
        highlighter,
        partIndex,
        totalPartsCount,
        mode,
        hideSubtitles,
      }}
      durationInFrames={avatar?.durationInFrames}
      fps={avatar?.fps}
      initialFrame={inFrame}
      inFrame={inFrame}
      outFrame={
        outFrame && outFrame >= avatar?.durationInFrames
          ? avatar?.durationInFrames - 1
          : outFrame
      }
      showPlaybackRateControl={playbackRateOptions}
      playbackRate={1}
      controls={controls}
      initiallyShowControls={true}
      allowFullscreen={false}
      compositionWidth={width}
      compositionHeight={height}
      acknowledgeRemotionLicense
      clickToPlay={false}
      moveToBeginningWhenEnded={false}
      numberOfSharedAudioTags={numberOfSharedAudioTags}
      errorFallback={(e: { error: { message: string } }) => (
        <span className="text-sm text-red-500">错误: {e.error.message}</span>
      )}
    />
  );
};
export { GuideDrawPlayer };
