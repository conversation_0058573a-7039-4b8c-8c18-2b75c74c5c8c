import {
  HomeWorkData,
  HomeworkDetailsData,
  StudentTaskReportAnswersData,
  TaskReportAnswersData,
} from "@/types/homeWork";
import { SubjectClassListData } from "@/types/subjectClass";
import { r } from "../libs/axios";
import { StudentDetailResponse } from "../types/homeWork";

export interface GetHomeworkListParams {
  /**
   * 任务查询范围截止日期，格式：2025-01-01
   */
  endDate?: string;
  /**
   * 查询对象 id，group_type 为 1 时是 0，为 2 时是班级 id，为 3 时是小组 id
   */
  groupId?: number;
  /**
   * 0 全部，1 临时小组，2 班级，3 学生小组
   */
  groupType?: number;
  /**
   * 检索关键字，模糊查询
   */
  keyword?: string;
  /**
   * 默认 1
   */
  page?: number;
  /**
   * 默认10，最大 100
   */
  pageSize?: number;
  /**
   * 任务查询范围开始日期，格式：2025-01-01
   */
  startDate?: string;
  /**
   * 学科 id，1语文,2数学,3英语,4物理,5化学,6生物,7历史,8地理,9道德与法治；班主任可查看全部报告，学科教师只能查看任教科目报告
   */
  subject?: number;
  /**
   * 任务类型，0 所有，10课程，20作业，30测验，40资源
   */
  taskType?: number;
}
export const getHomeworkList = async (params: GetHomeworkListParams) => {
  // Mock 请求
  // return getHomeworkData(params);
  return r.get<HomeWorkData>("/task/report/list", {
    params: params,
  });
};

// /api/v1/task/report/latest 获取最近布置的作业
export const getLatestHomework = async () => {
  return r.get<HomeWorkData>("/task/report/latest");
};

export interface GetHomeworkDetailParams {
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 学生列表分页页码
   */
  page?: number;
  /**
   * 学生列表分页大小，默认 10，最大 100
   */
  pageSize?: number;
  /**
   * 资源 id，查询具体资源对象的报告，为空表示查询所有
   */
  resourceId?: string;
  /**
   * 资源类型，必须要资源 id 同时赋值
   */
  resourceType?: number;
  /**
   * 学生姓名，支持模糊查询
   */
  studentName?: string;
}
/* 获取作业详情 */
export const getHomeworkDetail = async (params: GetHomeworkDetailParams) => {
  // return getHomeworkDetailMock();
  return r.get<HomeworkDetailsData>(`/task/report/detail`, {
    params: params,
  });
};

export interface DeleteHomeworkAssignParams {
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 任务布置ID
   */
  assignIds: number[];
}

// /api/v1/task/management/assign/delete 删除任务布置
export const deleteHomeworkAssign = async (
  params: DeleteHomeworkAssignParams
) => {
  return r.post<void>("/task/management/assign/delete", params);
};

/**
 * 学生行为类型
 */
export type StudentBehaviorType = "task_praise" | "task_attention";

/**
 * 统一的学生行为处理参数
 */
export interface StudentBehaviorParams {
  /**
   * 学生ID列表
   */
  studentIds: number[];
  /**
   * 行为类型：task_praise 表扬，task_attention 提醒
   */
  behaviorType: StudentBehaviorType;
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 行为内容（提醒时的消息内容，表扬时可选）
   */
  content?: string;
}

export interface StudentBehaviorParamsV2 {
  /**
   * 学生列表
   */
  students: StudentBehavior[];
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 任务ID
   */
  taskId: number;
}
interface StudentBehavior {
  /**
   * 学生ID
   */
  id: number;
  /**
   * 教师ID
   */
  teacherId: number;
  /**
   * 行为类型
   */
  behaviorType: StudentBehaviorType;
  /**
   * 默认提醒文本
   */
  pushDefaultText: string;
  /**
   * 课程URL
   */
  courseUrl?: string;
}

/**
 * 统一处理学生行为（表扬/提醒）
 * @param params 学生行为参数
 * @returns Promise<void>
 */
export const handleStudentBehavior = async (params: StudentBehaviorParams) => {
  return r.post<void>("/behavior/student/handle", params);
};
export const handleStudentBehaviorV2 = async (
  params: StudentBehaviorParamsV2
) => {
  return r.post<void>("/task/report/message/send", params);
};

/**
 * 表扬学生（便捷方法）
 * @param studentIds 学生ID列表
 * @param taskId 任务ID
 * @param assignId 任务布置ID
 * @param content 表扬内容（可选）
 * @returns Promise<void>
 */
export const praiseStudents = async (
  studentIds: number[],
  taskId: number,
  assignId: number,
  content?: string
) => {
  return handleStudentBehavior({
    studentIds,
    behaviorType: "task_praise",
    taskId,
    assignId,
    content,
  });
};

/**
 * 提醒学生（便捷方法）
 * @param studentIds 学生ID列表
 * @param taskId 任务ID
 * @param assignId 任务布置ID
 * @param content 提醒内容
 * @returns Promise<void>
 */
export const attentionStudents = async (
  studentIds: number[],
  taskId: number,
  assignId: number,
  content?: string
) => {
  return handleStudentBehavior({
    studentIds,
    behaviorType: "task_attention",
    taskId,
    assignId,
    content,
  });
};

/**
 * 更新任务名称参数
 */
export interface UpdateTaskNameParams {
  /**
   * 任务ID
   */
  taskId: number;
  /**
   * 新的任务名称
   */
  taskName: string;
}

/**
 * 更新任务名称
 * @param params 更新任务名称参数
 * @returns Promise<void>
 */
export const updateTaskName = async (params: UpdateTaskNameParams) => {
  return r.post<void>("/task/management/update", params);
};

/**
 * 更新任务时间参数
 */
export interface UpdateTaskAssignParams {
  /**
   * 布置ID
   */
  assignId: number;
  /**
   * 开始时间（时间戳，秒）
   */
  startTime?: number;
  /**
   * 截止时间（时间戳，秒）
   */
  deadline?: number;
}

/**
 * 更新任务时间
 * @param params 更新任务时间参数
 * @returns Promise<void>
 */
export const updateTaskAssign = async (params: UpdateTaskAssignParams) => {
  return r.post<void>("/task/management/assign/update", params);
};

/**
 * 题目面板请求参数
 */
export interface AnswerPanelRequest {
  /**
   * 任务布置 id
   */
  assignId?: number;
  /**
   * 资源 id
   */
  resourceId?: string;
  /**
   * 资源类型，101AI课，102巩固练习，103试题，104试卷
   */
  resourceType?: number;
  /**
   * 任务 id
   */
  taskId?: number;
  /**
   * 是否查看全部题目，默认只看共性错题
   */
  allQuestions?: boolean;

  /**
   * 学生列表分页页码
   */
  page: number;
  /**
   * 学生列表分页大小，默认 10，最大 100
   */
  pageSize: number;
  /**
   * 0全部题型 ，1单选题，2多选题，3填空题，4判断题，5解答题
   */
  questionType?: number;
  /**
   * 题目关键词，支持模糊查询
   */
  titleKey?: string;
}

export interface AnswerPanelData {
  /**
   * 任务布置 id
   */
  assignId: number;
  /**
   * 题目面板
   */
  panel: PanelItem[];
  /**
   * 任务 id
   */
  taskId: number;
}

export interface PanelItem {
  /**
   * 正确率，0-1
   */
  correctRate: number;
  /**
   * 题目 id
   */
  questionId: string;
  /**
   * 母题 id
   */
  rootQuestionId?: string;
  /**
   * 题目在任务中的序号，从 1 开始
   */
  questionIndex: number;
  questionIndexStr?: string;
  /**
   * 0全部题型 ，1单选题，2多选题，3填空题，4判断题，5解答题
   */
  questionType?: number;
  /**
   * 资源 id
   */
  resourceId: string;
  /**
   * 资源类型，101AI课，102巩固练习，103试题，104试卷
   */
  resourceType: number;
}

/**
 * 获取题目面板数据
 * @param params 请求参数
 * @returns Promise<AnswerPanelResponse>
 */
export const getAnswerPanel = async (params: AnswerPanelRequest) => {
  // return getAnswerPanelMock(params);
  return r.get<AnswerPanelData>("/task/report/answer-panel", {
    params: params,
  });
};

/**
 * 查询班级下的题目列表数据请求参数
 */
export interface TaskReportAnswersRequest {
  /**
   * 是否查看全部题目，默认只看共性错题
   */
  allQuestions?: boolean;
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 学生列表分页页码
   */
  page: number;
  /**
   * 学生列表分页大小，默认 10，最大 100
   */
  pageSize: number;
  /**
   * 0全部题型 ，1单选题，2多选题，3填空题，4判断题，5解答题
   */
  questionType?: number;
  /**
   * 资源 id，查询具体资源对象的报告，为空表示查询所有
   */
  resourceId?: string;
  /**
   * 资源类型，必须和资源 id 同时赋值
   */
  resourceType?: number;
  /**
   * 排序 key，answerCount, incorrectCount
   */
  sortBy?: string;
  /**
   * 排序方式：asc，desc
   */
  sortType?: string;
  /**
   * 任务 id
   */
  taskId: number;
  /**
   * 题目关键词，支持模糊查询
   */
  titleKey?: string;
  // 允许额外属性
  [property: string]: unknown;
}

/**
 * 查询班级下的题目列表数据
 * @param params 请求参数
 * @returns Promise<TaskReportAnswersDataV2>
 */
export const getTaskReportAnswers = async (
  params: TaskReportAnswersRequest
) => {
  return r.get<TaskReportAnswersData>("/task/report/answers", {
    params: params,
  });
};

/**
 * 查询学生视图下的题目列表数据
 * @param params 请求参数
 * @returns Promise<StudentTaskReportAnswersData>
 */
export interface StudentAnswersRequest extends TaskReportAnswersRequest {
  /**
   * 学生 ID
   */
  studentId: number;
}

export const getStudentTaskReportAnswers = async (
  params: StudentAnswersRequest
) => {
  return r.get<StudentTaskReportAnswersData>("/task/report/student/answers", {
    params: params,
  });
};

/**
 * 导出报告接口参数
 */
export interface ExportReportParams {
  /**
   * 任务布置ID
   */
  assignId: number;
  /**
   * 要导出的字段，用英文逗号连接，支持字段：studentName 学生姓名, studyScore 学习能量, progress 完成进度, accuracyRate 正确率,
   * difficultyDegree 难度, incorrectCount/answerCount 错题数/答题数
   */
  fields?: string;
  /**
   * 资源 id，查询具体资源对象的报告，为空表示查询所有
   */
  resourceId?: string;
  /**
   * 资源类型，必须和资源 id 同时赋值
   */
  resourceType?: number;
  /**
   * 任务 id
   */
  taskId: number;
  /**
   * 排序字段
   * studyScore - 学习能量
   * progress - 完成进度
   * accuracyRate - 正确率
   * answerCount - 答题数
   */
  sortBy?: "studyScore" | "progress" | "accuracyRate" | "answerCount";
  /**
   * 排序方式
   * asc - 升序
   * desc - 降序
   */
  sortType?: "asc" | "desc";
}

/**
 * 导出报告
 * @param params 导出参数
 * @returns Promise<Blob> 返回二进制数据
 */
export const exportReport = async (
  params: ExportReportParams
): Promise<Blob> => {
  const response = await r.get<Blob>("/task/report/export", {
    params: params,
    responseType: "blob",
  });

  // 返回二进制数据
  return response;
};

/**
 * 获取学科班级列表
 * @returns Promise<SubjectClassListData>
 */
export const getSubjectClassList = async (): Promise<SubjectClassListData> => {
  // 使用 mock 数据
  // return mockSubjectClassList();

  // 实际调用接口
  return r.get<SubjectClassListData>("/task/report/subject-class/list");
};

export const getTeacherTaskClassList = async (params: {
  taskId: number;
  classes: Array<{ classId: number; className: string }>;
}): Promise<
  Array<{
    taskId: number;
    assignId: number;
    classId: number;
    className: string;
  }>
> => {
  return r.post<
    Array<{
      taskId: number;
      assignId: number;
      classId: number;
      className: string;
    }>
  >("/task/report/getTeacherTaskClassList", params);
};

export const getClassTaskAvgAccuracyAndAvgProgress = async (param: {
  taskId: number;
  assignId: number;
}): Promise<{
  avgAccuracy: number;
  avgProgress: number;
  commonIncorrectCount: number;
}> => {
  return r.get<{
    avgAccuracy: number;
    avgProgress: number;
    commonIncorrectCount: number;
  }>("/task/report/getClassTaskAvgAccuracyAndAvgProgress", {
    params: {
      taskId: param.taskId,
      assignId: param.assignId,
    },
  });
};

export const getTaskResourceReport = async (param: {
  taskId: number;
  assignId: number;
  studentId?: number;
}) => {
  return r.get<{
    subjectId: number;
    resourceReports: Array<{
      averageCostTime: number;
      completionRate: number;
      correctRate: number;
      needAttentionQuestionNum: number;
      needAttentionUserNum: number;
      resourceId: string;
      resourceName: string;
      resourceType: number;
      extendedResourceId: string;
      resourceExtra: {
        firstLevelBizTreeNodeId: number;
        firstLevelBizTreeNodeName: string;
        firstLevelBizTreeNodeSerialPath: string;
        lastLevelBizTreeNodeId: number;
        lastLevelBizTreeNodeName: string;
        lastLevelBizTreeNodeSerialPath: string;
      };
    }>;
  }>("/task/report/getTaskResourceReport", {
    params: {
      taskId: param.taskId,
      assignId: param.assignId,
      studentId: param.studentId,
    },
  });
};

/**
 * 获取学生详情信息
 * @param taskId 任务ID
 * @param assignId 布置ID
 * @param studentId 学生ID
 */
export const getStudentDetail = (
  taskId: number,
  assignId: number,
  studentId: number,
  schoolId: number,
  recommendationType: "task_praise" | "task_attention" | "task_other",
  resourceId?: string
): Promise<StudentDetailResponse> => {
  return r.get<StudentDetailResponse>("/task/report/student/detail", {
    params: {
      taskId,
      assignId,
      studentId,
      schoolId,
      recommendationType,
      resourceId,
    },
  });
};
