// https://developer.mozilla.org/en-US/docs/Web/API/Network_Information_API
interface NetworkInformation {
  downlink?: number;
  effectiveType?: "slow-2g" | "2g" | "3g" | "4g";
  rtt?: number;
  saveData?: boolean;
  type?:
    | "bluetooth"
    | "cellular"
    | "ethernet"
    | "none"
    | "wifi"
    | "wimax"
    | "other"
    | "unknown";
  downlinkMax?: number;
  onchange?: EventListener;
}

interface Navigator {
  connection?: NetworkInformation;
  mozConnection?: NetworkInformation;
  webkitConnection?: NetworkInformation;
}

interface Window {
  /**
   * 只有平板有
   */
  TecJsb?: {
    /**
     * 页面加载完成时调用，通知平板
     */
    firstPageReady: () => void;
  };
}