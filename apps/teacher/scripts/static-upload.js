const fs = require("fs");
const path = require("path");
const OSS = require("ali-oss");

const dirName = "tch";
console.log("打包环境：", process.env.npm_lifecycle_event);

let env = process.env.npm_lifecycle_event;

if (env === "build:dev") {
  env = "build:test";
}

const store = new OSS({
  region: "oss-cn-beijing",
  accessKeyId:
    env === "build:test"
      ? "LTAI5tPsnnDoL3Fh1uPdMDwe"
      : "LTAI5tGYTHcLGpVnzJSi93EJ",
  accessKeySecret: env === "build:test"
    ? "******************************"
    : "******************************",
  bucket: env === "build:test" ? "gil-test" : "gil-prod",
});

// 上传目录到 OSS
const uploadDirectory = async (sourceDir, version) => {
  const staticDir = path.join(sourceDir, "dist/static");
  const publicDir = path.join(sourceDir, "public");

  if (!fs.existsSync(staticDir)) {
    throw new Error(`Static directory not found: ${staticDir}`);
  }

  if (!fs.existsSync(publicDir)) {
    throw new Error(`Public directory not found: ${publicDir}`);
  }

  const ossPath = `${dirName}/_next`;

  // 递归上传目录
  const uploadDir = async (dir, ossPrefix) => {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);

      if (stats.isDirectory()) {
        // 递归上传子目录
        await uploadDir(filePath, `${ossPrefix}/${file}`);
      } else {
        // 上传文件
        const ossFilePath = `${ossPrefix}/${file}`;
        await store.putStream(ossFilePath, fs.createReadStream(filePath));
        console.log(`✅ 上传文件: ${ossFilePath}`);
      }
    }
  };

  try {
    // 上传 static 目录
    console.log("📤 开始上传 static 目录...");
    await uploadDir(staticDir, `${ossPath}/static`);

    // 上传 public 目录
    console.log("📤 开始上传 public 目录...");
    await uploadDir(publicDir, `${ossPath}/public`);

    return {
      ossPath,
      downloadUrl: `https://${env === "build:test" ? "gil-test" : "gil-prod"}.oss-cn-beijing.aliyuncs.com/${ossPath}`,
    };
  } catch (error) {
    console.error("上传目录失败:", error);
    throw error;
  }
};

const main = async () => {
  try {
    const packageJson = JSON.parse(
      fs.readFileSync(path.join(__dirname, "../package.json"), "utf8")
    );
    const version = packageJson.version;
    console.log("✅ 获取到当前版本号:", version);

    const buildDir = path.resolve(__dirname, "..");

    console.log("🚀 开始上传静态资源...");
    console.log(`📂 源目录: ${buildDir}`);

    const { ossPath, downloadUrl } = await uploadDirectory(buildDir, version);
    console.log("✨ 上传完成！");
    console.log("✅ OSS 路径:", ossPath);
    console.log("✅ 下载链接:", downloadUrl);

    console.log("✅ 文件上传成功:");
  } catch (error) {
    console.error("❌ 上传过程出错:", error);
    process.exit(1);
  }
};

main();
