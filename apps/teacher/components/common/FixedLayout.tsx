import { useApp } from "@/hooks";

export default function FixedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { statusBarHeight } = useApp();
  // 保持zindex > 50，高于drawer就可以
  return (
    <div
      className="fixed left-0 z-50 h-full w-full"
      style={{
        height: `calc(100vh - ${statusBarHeight}px)`,
        top: `${statusBarHeight}px`,
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {children}
    </div>
  );
}
