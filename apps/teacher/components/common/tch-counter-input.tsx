import { Input } from "@/ui/input";
import { cn } from "@/utils/utils";
import { forwardRef, useEffect, useState } from "react";

interface TchCounterInputProps extends React.ComponentProps<"input"> {
  maxLength: number;
}

export const TchCounterInput = forwardRef<
  HTMLInputElement,
  TchCounterInputProps
>(({ maxLength, ...props }, ref) => {
  const [value, setValue] = useState(props.value?.toString() || "");

  const placeholderClassName = "placeholder:text-gray-4";
  const hoverClassName = "hover:border-primary-2";
  const focusClassName = "focus-visible:ring-0";

  useEffect(() => {
    setValue(props.value?.toString() || "");
  }, [props.value]);

  return (
    <div className="relative">
      <Input
        ref={ref}
        {...props}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
          props.onChange?.(e);
        }}
        maxLength={maxLength}
        className={cn(
          `border-line-3 h-12 rounded-md border px-3 py-2 pr-12 text-sm leading-normal `,
          placeholderClassName,
          hoverClassName,
          focusClassName,
          props.className
        )}
      />
      <span className="text-gray-4 absolute right-4 top-1/2 -translate-y-1/2 text-xs font-normal">
        {value.length}/{maxLength}
      </span>
    </div>
  );
});

TchCounterInput.displayName = "TchCounterInput";
