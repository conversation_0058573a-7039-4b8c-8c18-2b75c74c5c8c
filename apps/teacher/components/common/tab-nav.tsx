"use client";
import { TabItem } from "@/app/homework/[id]/_components/layout/TabNav";
import { cn } from "@/utils/utils";
import { ReactNode, useEffect, useRef, useState } from "react";

interface BaseTabNavProps<T extends string = string> {
  tabs: TabItem<T>[];
  activeTab: T;
  onTabChange: (tab: T) => void;
  rightContent?: ReactNode;
  className?: string;
  tabClassName?: string;
  tabWidth?: any;
}

// 渲染纯标签导航
export default function TabNav<T extends string = string>({
  tabs,
  activeTab,
  onTabChange,
  rightContent,
  className = "",
  tabClassName = "",
  tabWidth,
}: BaseTabNavProps<T>) {
  // 定义标签按钮样式
  const getTabButtonClass = (tab: TabItem<T>) => {
    const isActive = activeTab === tab.id;
    const isDisabled = tab.disabled;

    return `mr-[1.5rem] relative flex-1 items-center justify-center gap-2 pb-1.5 text-center text-base font-medium leading-6 ${
      isDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
    } ${
      isActive
        ? "text-primary-1 hover:text-primary-1"
        : "text-gray-4 hover:text-gray-700"
    }`;
  };

  // 过滤可见标签
  const visibleTabs = tabs.filter((tab) => !tab.hidden);

  // 使用 useRef 来存储 DOM 元素引用
  const tabRefs = useRef<Map<string, HTMLButtonElement>>(new Map());

  // 使用 useState 来更新下划线宽度，但避免频繁更新
  const [activePositionInfo, setActivePositionInfo] = useState({
    width: 0,
    left: 0,
  });
  const [actTab, setActTab] = useState<T>(activeTab);
  // 只在组件挂载和 activeTab 变化时测量一次
  useEffect(() => {
    // 如果有预定义的宽度，则不需要测量
    if (actTab !== activeTab) {
      setActTab(activeTab);
    }
    if (tabWidth && tabWidth[actTab]) return;

    // 获取当前活动标签的 DOM 元素
    const activeElement = tabRefs.current.get(activeTab);

    if (activeElement) {
      // 使用 requestAnimationFrame 确保在下一帧测量，避免强制同步布局
      requestAnimationFrame(() => {
        setActivePositionInfo({
          width: activeElement.offsetWidth,
          left: activeElement.offsetLeft,
        });
      });
    }
  }, [activeTab, tabWidth]);

  // 处理点击事件，使用事件委托优化性能
  const handleTabClick = (tab: TabItem<T>) => {
    if (tab.disabled) return;
    setActTab(tab.id);
    console.log("handleTabClick", tab.id, performance.now());
    // 立即更新视觉状态，不等待父组件状态更新
    if (tabWidth && tabWidth[tab.id]) {
      // 如果有预定义的宽度，直接使用
    } else {
      const element = tabRefs.current.get(tab.id);
      if (element) {
        setActivePositionInfo({
          width: element.offsetWidth,
          left: element.offsetLeft,
        });
      }
    }

    // 通知父组件状态变化
    onTabChange(tab.id);
  };

  // 设置 ref 回调函数
  const setTabRef = (element: HTMLButtonElement | null, tabId: string) => {
    if (element) {
      tabRefs.current.set(tabId, element);
    } else {
      tabRefs.current.delete(tabId);
    }
  };

  return (
    <div
      className={`pt-2.25 flex w-full items-center justify-between border-b ${className}`}
    >
      {/* 1. 标签自身 */}
      <div className="relative flex">
        {/* 滑动的下划线 - 使用 transform 和 will-change 优化性能 */}
        <div
          className="bg-primary-2 rounded-xs absolute -bottom-px h-0.5 will-change-transform"
          style={{
            width: `${tabWidth && tabWidth[activeTab] ? tabWidth[activeTab].width : activePositionInfo.width}px`,
            transform: `translateX(${tabWidth && tabWidth[activeTab] ? tabWidth[activeTab].left : activePositionInfo.left}px)`,
            transition:
              "transform 160ms cubic-bezier(0.4, 0, 0.2, 1), width 160ms cubic-bezier(0.4, 0, 0.2, 1)",
            willChange: "transform, width",
          }}
        />

        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            ref={(el) => setTabRef(el, tab.id as string)}
            onClick={() => handleTabClick(tab)}
            disabled={tab.disabled}
            className={cn(getTabButtonClass(tab), tabClassName)}
            // 添加触摸优化属性
            style={{
              touchAction: "manipulation",
              WebkitTapHighlightColor: "transparent", // 移除默认的触摸高亮
              userSelect: "none", // 防止文本选择
            }}
            // 添加触摸反馈 - 使用 CSS 变量实现更流畅的反馈
            onTouchStart={(e) => {
              if (!tab.disabled) {
                // 使用 CSS 变量实现更高效的触摸反馈
                console.log("touchStart", tab.id, performance.now());
                e.currentTarget.style.transform = "scale(0.97)";
                e.currentTarget.style.opacity = "0.8";
                handleTabClick(tab);
              }
            }}
            onTouchEnd={(e) => {
              if (!tab.disabled) {
                e.currentTarget.style.transform = "scale(1)";
                e.currentTarget.style.opacity = "1";
              }
            }}
            onTouchCancel={(e) => {
              if (!tab.disabled) {
                e.currentTarget.style.transform = "scale(1)";
                e.currentTarget.style.opacity = "1";
              }
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 2. 右侧内容区域 */}
      {rightContent && <div className="flex items-center">{rightContent}</div>}
    </div>
  );
}
