import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { getEstimatedTime } from "@/app/assign/[subjectKey]/course/utils";
import { cn } from "@/utils/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useClickAway } from "ahooks";
import { CircleHelp } from "lucide-react";
import { useRef } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./assign-tooltip";
interface AssignTimeButtonProps {
  style?: React.CSSProperties;
  content?: string;
  className?: string;
}

export function AssignTimeButton({
  style = {},
  className = "",
}: AssignTimeButtonProps) {
  const { selectedAiCourses, selectedPractices } = useAssignCourseContext();
  const tipOpen = useSignal(false);
  const ref = useRef(null);
  useClickAway(() => {
    tipOpen.value = false;
  }, ref);

  const aiCourseEstimatedTime = useComputed(() => {
    return getEstimatedTime(selectedAiCourses.value.map((e) => e.aiCourse));
  });

  const practiceEstimatedTime = useComputed(() => {
    return getEstimatedTime(selectedPractices.value.map((e) => e.practice));
  });

  return (
    <div
      className={cn(
        "mr-[-4] inline-flex h-8 items-center whitespace-nowrap rounded-full bg-slate-100 px-3 text-xs font-normal leading-tight text-slate-500 outline-1 outline-offset-[-0.50px] outline-slate-200",
        className
      )}
      style={style}
    >
      <div className="flex items-center gap-0.5 pr-2">
        <span className="leading-[normal]">预估时长</span>
        <TooltipProvider>
          <Tooltip open={tipOpen.value}>
            <TooltipTrigger asChild>
              <CircleHelp
                ref={ref}
                className="size-2.5 text-[#8F94AB]"
                onClick={() => (tipOpen.value = !tipOpen.value)}
              />
            </TooltipTrigger>
            <TooltipContent className="bg-slate-600" arrowPadding={10}>
              <div className="px-4 py-3">
                <div className="text-fill-fill-white text-sm font-medium leading-tight">
                  预估学习时长是系统基于你选取的内容，结合学生的
                  <br />
                  历史学习数据计算得出
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="w-0.25 h-full bg-slate-200"></div>
      {aiCourseEstimatedTime.value && (
        <div className="pl-3 text-neutral-900">
          课程学习: {aiCourseEstimatedTime.value}
        </div>
      )}
      {practiceEstimatedTime.value && aiCourseEstimatedTime.value && (
        <div className="text-color-3 pl-3">|</div>
      )}

      {practiceEstimatedTime.value && (
        <div className="pl-3 text-neutral-900">
          巩固练习: {practiceEstimatedTime.value}
        </div>
      )}
      {!practiceEstimatedTime.value && !aiCourseEstimatedTime.value && (
        <div className="text-line-3 pl-3 text-[.5rem] leading-[normal]">|</div>
      )}
    </div>
  );
}
