import DatePickerIcon from "@/public/assign/datepicker.svg";
import { AssignCourseTimeRange } from "@/types/assign/course";
import { Button } from "@/ui/tch-button";
import { getFormatTimeStr } from "@/utils";
import { cn } from "@/utils/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { ConfigProvider, DatePicker } from "antd";
import { RangePickerProps } from "antd/es/date-picker";
import zhCN from "antd/es/locale/zh_CN";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/zh-cn";
import { useEffect, useMemo } from "react";

export type DateTimeChangePayload =
  | {
      type: "immediate" | "recommend";
      isStart: boolean;
      index: number;
      customTimestamp?: number;
    }
  | {
      type: "custom";
      isStart: boolean;
      index: number;
      customTimestamp: number;
    };

interface AssignDatePickerProps {
  style?: React.CSSProperties;
  className?: string;
  isStart: boolean;
  timeRange: AssignCourseTimeRange;
  timeRangeIndex: number;
  recommendEndTime?: number;
  onDateTimeChange: (payload: DateTimeChangePayload) => void;
}

export function AssignDatePicker({
  style = {},
  className = "",
  isStart = true,
  timeRange,
  timeRangeIndex,
  recommendEndTime,
  onDateTimeChange,
}: AssignDatePickerProps) {
  const open = useSignal(false);
  const isOk = useSignal(false);
  const pickerValue = dayjs(
    isStart
      ? timeRange.isImmediate
        ? Date.now()
        : timeRange.startTime
      : timeRange.isRecommend
        ? recommendEndTime
        : timeRange.endTime
  );

  const formatEndTime = useComputed(() => {
    if (recommendEndTime) {
      return `推荐时间 (${getFormatTimeStr(recommendEndTime)})`;
    }
    return "";
  });

  const popupUnitClassName = `gil-date-picker-popup-${timeRangeIndex}-${isStart ? "start" : "end"}`;

  const startTimeStr = useMemo(() => {
    if (timeRange.isImmediate) {
      return "立即发布";
    }
    return dayjs(timeRange.startTime).format("YYYY-MM-DD HH:mm");
  }, [timeRange.isImmediate, timeRange.startTime]);

  const endTimeStr = useMemo(() => {
    const time = dayjs(timeRange.endTime).format("YYYY-MM-DD HH:mm");
    if (timeRange.isRecommend) {
      return time + " (推荐)";
    }
    return time;
  }, [timeRange.isRecommend, timeRange.endTime]);

  // 立即发布按钮
  const onImmediatePublish = () => {
    isOk.value = false;
    open.value = false;

    onDateTimeChange({
      type: "immediate",
      isStart: isStart,
      index: timeRangeIndex,
    });
  };

  // 推荐发布按钮
  const onRecommendPublish = () => {
    isOk.value = false;
    open.value = false;
    onDateTimeChange({
      type: "recommend",
      isStart: isStart,
      index: timeRangeIndex,
    });
  };

  const onOk = () => {
    open.value = false;
    isOk.value = true;
  };

  const onDateChange = (value: Dayjs) => {
    if (isOk.value) {
      onDateTimeChange({
        type: "custom",
        isStart: isStart,
        index: timeRangeIndex,
        customTimestamp: value.valueOf(),
      });
    }
  };

  // useEffect(() => {
  //   pickerValue.value = dayjs(
  //     isStart ? timeRange.startTime : timeRange.endTime
  //   );
  // }, [timeRange, isStart, pickerValue]);

  const disabledDate: RangePickerProps["disabledDate"] = (current) => {
    // 昨天之前的日期不可选
    const yesterday = dayjs().subtract(1, "day").endOf("day");
    return current && current.isBefore(yesterday);
  };

  const disabledDateTime = (current: Dayjs) => {
    if (current && current.isSame(dayjs(), "day")) {
      const now = dayjs();
      return {
        // 禁用当前小时之前的所有小时
        disabledHours: () => {
          const hours = [];
          for (let i = 0; i < now.hour(); i++) {
            hours.push(i);
          }
          return hours;
        },
        // 如果是当前小时，则禁用当前分钟之前的所有分钟
        disabledMinutes: (selectedHour: number) => {
          if (selectedHour === now.hour()) {
            const minutes = [];
            for (let i = 0; i < now.minute(); i++) {
              minutes.push(i);
            }
            return minutes;
          }
          return [];
        },
      };
    }
    return {
      disabledHours: () => [],
      disabledMinutes: () => [],
      disabledSeconds: () => [],
    };
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const panel = document.querySelector("." + popupUnitClassName);
      if (open && panel && !panel.contains(event.target as Node)) {
        open.value = false;
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, popupUnitClassName]);

  const antPickerCls = `
    [&.ant-picker]:!border-slate-300 
    [&.ant-picker]:!px-3 
    [&.ant-picker]:!py-1.25 
    [&.ant-picker]:!text-neutral-900 
    [&.ant-picker]:!leading-tight 
    [&.ant-picker]:!rounded-md
    [&.ant-picker]:!text-sm 
    [&.ant-picker_.ant-picker-suffix]:!text-neutral-900 
  `;
  const antPickerInputCls = `
    [&_.ant-picker-input_>input]:!text-sm 
    [&_.ant-picker-input_>input]:!leading-normal 
  `;

  const pickerTheme = {
    components: {
      DatePicker: {
        cellWidth: 42,
        cellHeight: 36,
        cellBgDisabled: "transparent",
        textHeight: 36,
        activeBorderColor: "#6574FC",
      },
    },
  };
  return (
    <ConfigProvider locale={zhCN} theme={pickerTheme}>
      <div
        onClick={() => (open.value = true)}
        className="py-1.25 border-line-3 text-gray-1 flex cursor-pointer items-center justify-between gap-2 rounded-md border bg-white px-3 text-sm leading-normal"
      >
        <div className="flex-1">{isStart ? startTimeStr : endTimeStr}</div>
        <DatePickerIcon className="size-3.5" />
      </div>
      <DatePicker
        open={open.value}
        value={pickerValue}
        className={cn(
          "rain-date-picker",
          antPickerCls,
          antPickerInputCls,
          className
        )}
        classNames={{
          popup: {
            root: cn("gil-date-picker-popup", popupUnitClassName),
          },
        }}
        style={{ ...style, visibility: "hidden", width: 0 }}
        format="YYYY-MM-DD HH:mm"
        disabledDate={disabledDate}
        disabledTime={disabledDateTime}
        showTime={{ showNow: false }}
        showNow={false}
        needConfirm={false}
        onChange={onDateChange}
        renderExtraFooter={() => (
          <div className="flex items-center justify-between gap-2 px-8 py-5">
            {isStart ? (
              <Button
                type="default"
                className="h-10 px-6"
                radius="full"
                size="lg"
                onClick={onImmediatePublish}
              >
                立即发布
              </Button>
            ) : (
              <div
                className="text-primary-1 cursor-pointer text-sm font-normal"
                onClick={onRecommendPublish}
              >
                {formatEndTime.value}
              </div>
            )}
            <Button
              type="primary"
              className="h-10 px-6"
              size="lg"
              radius="full"
              onClick={onOk}
            >
              确定
            </Button>
          </div>
        )}
      />
    </ConfigProvider>
  );
}
