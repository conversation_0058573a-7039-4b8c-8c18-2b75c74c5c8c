"use client";
import { cn } from "@/utils/utils";
import { AssignCheckboxButton } from "./assign-checkbox-button";
// import { AssignButton } from "./assign-button";
import { useAssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { useComputed } from "@preact-signals/safe-react";
import { useMemo } from "react";
import { TchDrawer } from "../../ui/tch-drawer";
import { AssignStudentCard } from "./assign-student-card";

export function AssignTargets({
  className,
  style,
}: {
  className?: string;
  style?: React.CSSProperties;
}) {
  const {
    checkedTargetClassIds,
    targetClassList,
    checkedClasses,
    toggleClass,
    checkedClassStudentList,
  } = useAssignCourseContext();

  const totalSelectedStudents = useMemo(() => {
    return (
      checkedClassStudentList?.reduce((prev, item) => {
        return prev + (item.students?.length ?? 0);
      }, 0) ?? 0
    );
  }, [checkedClassStudentList]);

  const enabledGrade = useComputed(() => {
    return checkedClasses.value.length > 0
      ? checkedClasses.value[0].jobGrade
      : undefined;
  });

  const SelectedStudentsTrigger = () => {
    return (
      <div className="inline-block cursor-pointer select-none text-sm font-normal text-indigo-600">
        查看已选学生({totalSelectedStudents})
      </div>
    );
  };

  // TODO: Check assignTimeRanges 的逻辑
  // const classItemClick = useCallback(
  //   (item: TargetJobClass) => {
  //     toggleClass(item.jobClass);
  // if (checkedTargetClassIds.value.includes(item.jobClass)) {
  //   const classes = checkedClasses.value.filter(
  //     (checkedClass) => checkedClass.jobClass !== item.jobClass
  //   );
  //   checkedClasses.value = classes;
  // TM不会写代码可以不写
  // assignTimeRanges.value = assignTimeRanges.value.filter(
  //   (range) => range.classInfo.jobClass !== item.jobClass
  // );
  // } else {
  //   checkedClasses.value = [...checkedClasses.value, item];
  // const startTime = getImmediatePublishTimestamp();
  // assignTimeRanges.value = [
  //   ...assignTimeRanges.value,
  //   {
  //     isImmediate: true,
  //     isRecommend: true,
  //     startTime,
  //     endTime: getEndOfDayAfter(startTime, assignRecommendTime),
  //     classInfo: item,
  //   },
  // ];
  // }
  //   },

  //   [
  //     toggleClass,
  //   ]
  // );

  return (
    <div
      className={cn("flex flex-wrap items-center gap-3", className)}
      style={style}
    >
      {targetClassList?.map((item) => (
        <AssignCheckboxButton
          key={item.jobClass}
          content={item.jobGradeName + item.name}
          checked={checkedTargetClassIds.value.includes(item.jobClass)}
          disabled={
            !!enabledGrade.value && item.jobGrade !== enabledGrade.value
          }
          onClick={() => toggleClass(item.jobClass)}
        />
      ))}
      {/* <AssignButton variant="outline">自定义选择</AssignButton> */}
      {checkedClasses.value.length > 0 && (
        <TchDrawer
          title={`已选择的学生(${totalSelectedStudents})`}
          trigger={<SelectedStudentsTrigger />}
        >
          <div className="rounded-2xl bg-white px-6 py-3">
            <AssignStudentCard classStudentList={checkedClassStudentList} />
          </div>
        </TchDrawer>
      )}
    </div>
  );
}
