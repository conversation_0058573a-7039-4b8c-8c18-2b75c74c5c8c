import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import duration from "dayjs/plugin/duration";
import updateLocale from "dayjs/plugin/updateLocale";
import weekOfYear from "dayjs/plugin/weekOfYear";

// 初始化dayjs配置
dayjs.extend(duration);
dayjs.extend(updateLocale);
dayjs.extend(weekOfYear);
dayjs.locale("zh-cn");
// 设置一周的第一天为周一
dayjs.updateLocale("zh-cn", {
  weekStart: 1,
});

if (typeof window !== "undefined") {
  // 动态加载神策数据分析脚本
  import("@/public/js/sensors/web/sensorsdata.js")
    .then(() => {
      const sensors = window["sensorsDataAnalytic201505"];

      if (sensors) {
        // 神策数据埋点
        sensors.init({
          server_url:
            "https://yhzx-pro.datasink.sensorsdata.cn/sa?project=default&token=39e6f5c78934680b",
          use_client_time: true,
          send_type: "beacon",
          show_log: process.env.NODE_ENV === "development",
        });

        // 注册公共属性
        sensors.registerPage({
          app_name: "教师端",
          // TODO 修改平台
          platform: "web",
        });

        sensors.quick("autoTrack");
      }
    })
    .catch((error) => {
      console.error("Failed to load sensors analytics:", error);
    });
}
