import { cn } from "@/utils/utils";
import Link, { useLinkStatus } from "next/link";
import { FC, ReactNode, SVGProps } from "react";

export default function MenuLink({
  href,
  icon: Icon,
  activeIcon: ActiveIcon,
  children,
  active,
}: {
  active: boolean;
  href: string;
  icon: FC<SVGProps<SVGElement>>;
  activeIcon: FC<SVGProps<SVGElement>>;
  children: ReactNode;
}) {
  return (
    <Link
      href={href}
      className={cn(
        "text-gray-3 pl-6.75 flex h-12 items-center gap-2 rounded-xl border-[0.5px] border-transparent pr-4 text-base transition-colors",
        active && "border-line-2 text-primary-1 bg-white font-medium",
        "hover:border-line-2 hover:bg-white"
      )}
      prefetch
    >
      {active ? <ActiveIcon /> : <Icon />}
      <span className="flex-1">{children}</span>
    </Link>
  );
}

// 加载指示器组件
function LoadingIndicator() {
  const { pending } = useLinkStatus();

  return pending ? (
    <div
      role="status"
      aria-label="Loading"
      className="border-primary-1 ml-auto h-3 w-3 animate-spin rounded-full border-2 border-t-transparent"
      style={{
        opacity: 0,
        animation: "spin 1s linear infinite, fadeIn 0.3s 0.5s forwards",
      }}
    />
  ) : null;
}
