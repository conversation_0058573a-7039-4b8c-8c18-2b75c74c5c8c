"use client";
import { TASK_TYPE, taskTypeEnumManager } from "@/enums";
import { useApp } from "@/hooks";
import { Homework, HomeworkReport, StatData } from "@/types/homeWork";
import { Skeleton } from "@/ui/skeleton";
import { cn } from "@/utils/utils";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { TaskDataWithClassData } from "../[id]/_context/task-context";
// import { useOptions } from "../hooks/useOptions";
import { getClassTaskAvgAccuracyAndAvgProgress } from "@/services/homework";
import { useMount, useUpdateEffect } from "ahooks";
import { useOptions } from "../hooks/useOptionsV2";
import { MoreMenu } from "./more-menu";

export interface TaskData {
  creatorId: number;
  homeworkData: Homework;
  id: number;
  title: string;
  startTime: number;
  endTime: number;
  type: TASK_TYPE;
  typeName: string;
  date: string;
  bgColor: string;
  lineColor: string;
  classes: ClassData[];
  subject: number;
}

export interface NewTaskData {
  creatorId: number;
  id: number;
  title: string;
  startTime: number;
  endTime: number;
  type: TASK_TYPE;
  typeName: string;
  date: string;
  bgColor: string;
  lineColor: string;
  subject: number;
  taskList: TaskData[];
}

export type OriginalClassData = {
  taskId: number;
  taskName: string;
  taskType: TASK_TYPE;
} & HomeworkReport;

// 班级数据类型
export interface ClassData {
  assignId: number;
  id: number;
  statData: StatData;
  name: string;
  stats: Array<{
    label: string;
    value: string;
  }>;
  isDue?: boolean;
  classData: OriginalClassData;
}

// TaskCard 属性类型
export interface TaskCardProps {
  taskData?: TaskData;
  newTaskData: NewTaskData;
  className?: string;
  onlyShow?: boolean;
  onClickClass?: (classData: TaskDataWithClassData) => void;
  onClick?: () => void;
  classNames?: {
    classContent?: string;
  };
  minCardWidth?: string;
  screenWidth?: number;
}

const LoadingIcon = (
  <svg
    className={cn("animate-spin", "h-4 w-4")}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 12 12"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.36541 7.8763C0.954085 6.86031 0.888013 5.73731 1.17736 4.6801C1.46671 3.62288 2.09544 2.69004 2.96681 2.0251C3.83818 1.36017 4.90391 0.999993 6 0.999993C6.27614 0.999993 6.5 1.22385 6.5 1.49999C6.5 1.77614 6.27614 1.99999 6 1.99999C5.12313 1.99999 4.27054 2.28813 3.57345 2.82008C2.87635 3.35203 2.37337 4.0983 2.14189 4.94408C1.91041 5.78985 1.96327 6.68825 2.29233 7.50104C2.62138 8.31383 3.20841 8.99598 3.96308 9.44251C4.71774 9.88904 5.59823 10.0752 6.46906 9.9724C7.33988 9.86957 8.15279 9.48344 8.7827 8.87342C9.41261 8.2634 9.82462 7.46329 9.95532 6.59621C10.086 5.72913 9.92818 4.84312 9.50608 4.07453C9.37315 3.83248 9.46161 3.52851 9.70365 3.39558C9.9457 3.26266 10.2497 3.35111 10.3826 3.59316C10.9102 4.55391 11.1075 5.66141 10.9441 6.74526C10.7808 7.82911 10.2658 8.82925 9.47838 9.59178C8.69099 10.3543 7.67485 10.837 6.58632 10.9655C5.49779 11.094 4.39718 10.8613 3.45384 10.3031C2.51051 9.74498 1.77673 8.89229 1.36541 7.8763Z"
      fill="#ffffff"
    />
  </svg>
);

// 格式化日期
export function formatDate(statData?: StatData): string {
  if (!statData?.startTime) return "";

  const startTimestamp = statData.startTime * 1000;
  const endTimestamp = statData?.deadline ? statData.deadline * 1000 : null;

  const startStr = format(startTimestamp, "MM/dd HH:mm", { locale: zhCN });
  const endStr = endTimestamp
    ? ` 至 ${format(endTimestamp, "MM/dd HH:mm", { locale: zhCN })}`
    : "";

  return `${startStr}${endStr}`;
}
// 处理各种率
export function formatRate(rate: number, type?: number): string {
  if (rate === 0) return "";
  //   return (rate * 100) % 1 === 0
  //     ? (rate * 100).toFixed(0) + "%"
  //     : (rate * 100).toFixed(1) + "%";
  return type === 1
    ? Math.ceil(rate * 100) + "%"
    : Math.floor(rate * 100) + "%";
}

// 根据任务类型获取统计数据
export function getStatsFromReport(
  statData: StatData,
  taskType: number
): Array<{ label: string; value: string }> {
  if (taskType === 40) {
    // 资源类型
    return [
      { label: "平均进度", value: formatRate(statData.averageProgress || 0) },
      { label: "课时数", value: `${statData.classHours || 0}节` },
    ];
  }

  return [
    { label: "完成率", value: formatRate(statData?.completionRate || 0, 1) },
    { label: "正确率", value: formatRate(statData?.correctRate || 0) },
    { label: "待关注", value: `${statData?.needAttentionQuestionNum || 0}题` },
  ];
}

// 将API数据转换为UI数据的工具函数
export function transformHomeworkToTaskData(homework: Homework): TaskData {
  const { taskId, taskName, taskType, reports, subject, creatorId } = homework;

  const classes = reports.map((report) => {
    const stats = getStatsFromReport(report.statData, taskType);
    const isDue = report.statData?.deadline
      ? report.statData.deadline < Date.now() / 1000
      : false;

    return {
      assignId: report.assignId,
      id: report.assignObject.id,
      statData: report.statData,
      name: report.assignObject.name,
      stats,
      isDue,
      classData: {
        taskId,
        taskName,
        taskType,
        ...report,
      },
    };
  });

  return {
    creatorId,
    subject,
    id: taskId,
    title: taskName,
    type: taskType,
    typeName: taskTypeEnumManager.getLabelByValue(taskType) || "",
    date: formatDate(homework.reports[0]?.statData),
    startTime: homework.reports[0]?.statData?.startTime || 0,
    endTime: homework.reports[0]?.statData?.deadline || 0,
    bgColor: taskTypeEnumManager.getEnumByValue(taskType)?.bg || "",
    lineColor: taskTypeEnumManager.getEnumByValue(taskType)?.lineColor || "",
    classes,
    homeworkData: homework,
  };
}
/**
 * 任务卡片组件
 * 显示任务详情和相关班级数据
 */
export function TaskCardV2({
  //   taskData,
  newTaskData,
  className,
  onlyShow,
  //   onClick,
  onClickClass,
  classNames,
  minCardWidth,
  screenWidth,
}: TaskCardProps) {
  const { userInfo } = useApp();
  // const { title, typeName, date, bgColor, classes, lineColor } = taskData;
  const { title, typeName, date, bgColor, lineColor, taskList } = newTaskData;
  const isOwnTask = userInfo?.userID === newTaskData.creatorId;
  const router = useRouter();
  const { classFlatList } = useOptions();
  const [isActive, setIsActive] = useState(false);
  const [rows, setRows] = useState(1);
  const [statState, setStatState] = useState<{
    [key: number]: {
      avgAccuracy?: string;
      avgProgress?: string;
      commonIncorrectCount?: string;
      status?: string;
    };
  }>({});
  // const handleClick = () => {
  //   // 使用 RAF 确保状态更新后再执行点击回调
  //   requestAnimationFrame(() => {
  //     onClick?.();
  //   });
  // };
  const getGrade = (id: number) => {
    const classItem = classFlatList.find((item) => item.classID === id);
    return classItem?.gradeName || "";
  };
  const handleClickClass = (task: TaskDataWithClassData) => {
    // 使用 RAF 确保状态更新后再执行点击回调
    requestAnimationFrame(() => {
      onClickClass?.(task as TaskDataWithClassData);
    });
  };
  const getClassStats = async (
    paramArr: Array<{ taskId: number; assignId: number; classId: number }>
  ) => {
    const promises = paramArr.map((param) =>
      getClassTaskAvgAccuracyAndAvgProgress({
        taskId: param.taskId,
        assignId: param.assignId,
      })
    );
    const results = await Promise.allSettled(promises);
    const stats: {
      [key: number]: {
        avgAccuracy?: string;
        avgProgress?: string;
        commonIncorrectCount?: string;
        status?: string;
      };
    } = {};
    results.forEach((result, index) => {
      if (result.status === "fulfilled") {
        // result.value.json().then((data) => {
        //   stats[data.id] = data;
        // });
        stats[paramArr[index].classId] = {
          avgAccuracy: formatRate(result.value.avgAccuracy || 0),
          avgProgress: formatRate(result.value.avgProgress || 0, 1),
          commonIncorrectCount: `${result.value.commonIncorrectCount || 0}题`,
        };
      } else {
        stats[paramArr[index].classId] = { status: "error" };
      }
    });
    setStatState({ ...stats });
    // console.log("task stats", stats);
  };

  useUpdateEffect(() => {
    const len = Math.ceil(newTaskData.taskList.length / 4);
    setRows(len);
    const classStatsList: Array<{
      classId: number;
      assignId: number;
      taskId: number;
    }> = [];
    taskList.forEach((task) => {
      task.classes.forEach((cls) => {
        classStatsList.push({
          classId: cls.id,
          assignId: cls.assignId,
          taskId: newTaskData.id,
        });
      });
    });
    getClassStats(classStatsList);
  }, [newTaskData]);

  useMount(() => {
    const len = Math.ceil(newTaskData.taskList.length / 4);
    setRows(len);
    const classStatsList: Array<{
      classId: number;
      assignId: number;
      taskId: number;
    }> = [];
    taskList.forEach((task) => {
      task.classes.forEach((cls) => {
        classStatsList.push({
          classId: cls.id,
          assignId: cls.assignId,
          taskId: newTaskData.id,
        });
      });
    });
    getClassStats(classStatsList);
    // console.log("task card mount", classStatsList);
  });
  //   console.log("current task", taskList);
  return (
    // 任务卡片容器，添加点击状态样式
    <div
      className={cn(
        "flex flex-col items-start gap-4 rounded-2xl bg-white p-6 transition-all duration-200",
        // "active:scale-[0.98] active:opacity-40",
        className
      )}
      // onClick={handleClick}
    >
      <div className={`w-full`}>
        {/* 任务标题区域 */}
        <h2
          className={cn(
            "text-gray-2 mb-1 overflow-hidden text-ellipsis whitespace-nowrap text-base font-medium leading-[150%]"
            // screenWidth && screenWidth <= 1200
            //   ? taskList.length === 1
            //     ? "w-[267px]"
            //     : taskList.length === 2
            //       ? "w-[596px]"
            //       : taskList.length === 3
            //         ? "w-[932px]"
            //         : taskList.length >= 4 && "w-[928px]"
            //   : taskList.length >= 4
            //     ? "w-[928px]"
            //     : taskList.length === 3
            //       ? "w-[902px]"
            //       : taskList.length === 2
            //         ? "w-[596px]"
            //         : "w-[290px]"
          )}
        >
          {title}
        </h2>
        {/* 任务类型和日期信息 */}
        <p className="text-gray-4 mb-4 overflow-hidden text-ellipsis whitespace-nowrap text-xs font-normal leading-[150%]">
          <span className="text-[#8388ab]">{typeName}</span> {date}
        </p>

        {/* 班级数据列表 */}
        <div
          className={`grid flex-1 gap-4 ${taskList.length >= 4 ? "grid-cols-4" : "grid-cols-" + taskList.length}`}
          // className={"flex gap-4"}
        >
          {taskList.map((task) => {
            return task.classes.map((classData, index) => (
              <div
                onClick={() => {
                  handleClickClass(task as TaskDataWithClassData);
                }}
                onMouseEnter={() => {
                  router.prefetch(
                    `/homework/${task.id}?tab=report&assignId=${classData?.assignId}`
                  );
                }}
                key={index}
                className={cn(
                  `row-span flex flex-1 cursor-pointer flex-col items-start self-stretch rounded-[0.75rem] px-4 py-3 last:mb-0`,
                  !isActive && "active:scale-[0.98] active:opacity-40",
                  //   "min-w-[214px] max-w-[290px] flex-1",
                  // screenWidth && screenWidth <= 1200
                  //   ? taskList.length === 1
                  //     ? "w-[267px]"
                  //     : taskList.length === 2
                  //       ? "w-[290px]"
                  //       : taskList.length === 3
                  //         ? "w-[300px]"
                  //         : taskList.length >= 4 && "w-[220px]"
                  //   : taskList.length >= 4
                  //     ? "w-[220px]"
                  //     : minCardWidth || "w-[290px]",
                  // taskList.length >= 4
                  //   ? "w-[214px]"
                  //   : minCardWidth || "w-[290px]",
                  bgColor,
                  classNames?.classContent
                )}
              >
                {/* 班级名称和状态区域 */}
                <div className="flex w-full items-center justify-between">
                  {/* 班级名称 */}
                  <div className="text-gray-2 flex h-5 flex-1 flex-col justify-between overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium leading-[150%]">
                    {classData.name}
                  </div>
                  {/* 条件渲染：到期标记或更多菜单 */}
                  {classData.isDue && (
                    // 到期标记
                    <div className="flex h-[1.125rem] items-center gap-[0.125rem] rounded-[6px] bg-white/40 px-[0.25rem] text-[#64698a]">
                      <p className="align-middle text-xs leading-[1]">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 12 12"
                          fill="none"
                        >
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M1.5 6.5C1.5 4.01472 3.51472 2 6 2C8.48528 2 10.5 4.01472 10.5 6.5C10.5 8.98528 8.48528 11 6 11C3.51472 11 1.5 8.98528 1.5 6.5ZM6.5 4.5C6.5 4.22386 6.27614 4 6 4C5.72386 4 5.5 4.22386 5.5 4.5V6.5C5.5 6.63261 5.55268 6.75979 5.64645 6.85355L6.64645 7.85355C6.84171 8.04882 7.15829 8.04882 7.35355 7.85355C7.54882 7.65829 7.54882 7.34171 7.35355 7.14645L6.5 6.29289V4.5Z"
                            fill="#838BAB"
                          />
                          <path
                            d="M2.30003 2.65003C2.07912 2.81571 1.76571 2.77094 1.60003 2.55003C1.43434 2.32912 1.47912 2.01571 1.70003 1.85003L2.70003 1.10003C2.92094 0.934344 3.23434 0.979115 3.40003 1.20003C3.56571 1.42094 3.52094 1.73434 3.30003 1.90003L2.30003 2.65003Z"
                            fill="#838BAB"
                          />
                          <path
                            d="M9.30003 1.10009C9.07912 0.934403 8.76571 0.979174 8.60003 1.20009C8.43434 1.421 8.47912 1.7344 8.70003 1.90009L9.70003 2.65009C9.92094 2.81577 10.2343 2.771 10.4 2.55009C10.5657 2.32917 10.5209 2.01577 10.3 1.85009L9.30003 1.10009Z"
                            fill="#838BAB"
                          />
                        </svg>
                      </p>

                      <span
                        className="text-xxs align-middle"
                        style={{ lineHeight: "normal" }}
                      >
                        到期
                      </span>
                    </div>
                  )}
                  {/* 更多操作菜单 */}
                  {isOwnTask && !onlyShow && (
                    <MoreMenu taskData={task} onSetIsActive={setIsActive} />
                  )}
                </div>
                {/* 分割线 */}
                <div
                  className={cn(
                    "my-[0.625rem] h-[1px] w-full scale-y-50",
                    lineColor
                  )}
                ></div>

                {/* 统计数据区域 */}
                <div
                  className={
                    classData.stats.length === 2
                      ? "flex w-full justify-start gap-8" // 两个统计项时：第一个在开始位置，第二个有间距
                      : "flex w-full justify-between" // 三个或更多统计项时：均匀分布
                  }
                >
                  {/* 统计数据项 */}
                  {classData.stats.map((stat, statIndex) => (
                    <div key={statIndex} className="flex flex-col">
                      {/* 统计项标签 */}
                      <span className="text-gray-4 text-[0.625rem] font-normal leading-[150%]">
                        {stat.label}
                      </span>
                      {/* 统计项数值 */}
                      {/* <span
                        className={`text-gray-2 text-sm lining-nums proportional-nums leading-[150%] ${stat.label === "待关注" ? "font-semibold" : "font-medium"}`}
                      >
                        {stat.value ? stat.value : "—"}
                      </span> */}
                      {!statState[classData.id] ? (
                        <span className="align-center flex h-5 justify-center">
                          <svg
                            className={cn("animate-spin", "h-4 w-4")}
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 12 12"
                          >
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M1.36541 7.8763C0.954085 6.86031 0.888013 5.73731 1.17736 4.6801C1.46671 3.62288 2.09544 2.69004 2.96681 2.0251C3.83818 1.36017 4.90391 0.999993 6 0.999993C6.27614 0.999993 6.5 1.22385 6.5 1.49999C6.5 1.77614 6.27614 1.99999 6 1.99999C5.12313 1.99999 4.27054 2.28813 3.57345 2.82008C2.87635 3.35203 2.37337 4.0983 2.14189 4.94408C1.91041 5.78985 1.96327 6.68825 2.29233 7.50104C2.62138 8.31383 3.20841 8.99598 3.96308 9.44251C4.71774 9.88904 5.59823 10.0752 6.46906 9.9724C7.33988 9.86957 8.15279 9.48344 8.7827 8.87342C9.41261 8.2634 9.82462 7.46329 9.95532 6.59621C10.086 5.72913 9.92818 4.84312 9.50608 4.07453C9.37315 3.83248 9.46161 3.52851 9.70365 3.39558C9.9457 3.26266 10.2497 3.35111 10.3826 3.59316C10.9102 4.55391 11.1075 5.66141 10.9441 6.74526C10.7808 7.82911 10.2658 8.82925 9.47838 9.59178C8.69099 10.3543 7.67485 10.837 6.58632 10.9655C5.49779 11.094 4.39718 10.8613 3.45384 10.3031C2.51051 9.74498 1.77673 8.89229 1.36541 7.8763Z"
                              fill="#ffffff"
                            />
                          </svg>
                        </span>
                      ) : statState[classData.id].status === "error" ? (
                        <span
                          className={`text-gray-2 h-5 text-sm lining-nums proportional-nums leading-[150%] ${stat.label === "待关注" ? "font-semibold" : "font-medium"}`}
                        >
                          {stat.label === "待关注" ? "0题" : "—"}
                        </span>
                      ) : (
                        <span
                          className={`text-gray-2 h-5 text-sm lining-nums proportional-nums leading-[150%] ${stat.label === "待关注" ? "font-semibold" : "font-medium"}`}
                        >
                          {stat.label === "待关注"
                            ? statState[classData.id].commonIncorrectCount
                            : stat.label === "正确率"
                              ? statState[classData.id].avgAccuracy || "—"
                              : statState[classData.id].avgProgress || "—"}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ));
          })}
        </div>
      </div>
    </div>
  );
}

export function TaskCardSkeleton() {
  return (
    <div className="h-[221px] rounded-2xl p-6">
      {/* 标题和时间 */}
      <Skeleton className="mb-1 h-6 w-2/3 bg-gray-100" />
      <Skeleton className="w-9/10 mb-4 h-4 bg-gray-100" />

      {/* 两个班级卡片 */}
      <Skeleton className="mb-4 h-28 w-full rounded-xl bg-gray-100" />
    </div>
  );
}
