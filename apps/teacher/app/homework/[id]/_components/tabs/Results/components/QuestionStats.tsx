"use client";
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import BaseTabNav from "@/components/common/tab-nav";
import IcCorrect from "@/public/icons/ic_correct.svg";
import IcHalfcorrect from "@/public/icons/ic_halfcorrect.svg";
import IcIncorrect from "@/public/icons/ic_incorrect.svg";
import IcUnanswered from "@/public/icons/ic_noanswer.svg";
import Avatar from "@/ui/tch-avatar";
import { QaContentType } from "@repo/core/views/tch-question-view";
import { useMount, useUpdateEffect } from "ahooks";
// import Image from "next/image";
import ImageViewer from "@/ui/imageViewer";
import { AnswerDetail } from "@repo/core/views/tch-question-view";
import { useCallback, useState } from "react";
import { QuestionAnswerDetailDrawer } from "./QuestionAnswerDetailDrawer";
interface StatsProps {
  qaContent: QaContentType;
  // 可选的自定义渲染函数
  renderCustomStats?: (stats: StatsData) => React.ReactNode;
  // 可选的学生总数，用于计算百分比
  totalStudents?: number;
  // 可选的学生信息获取函数
  getStudentInfo?: (
    studentId: number
  ) => { name: string; avatar?: string } | null;
  // 可选的图标组件
  icons?: {
    correct?: React.ReactNode;
    incorrect?: React.ReactNode;
    unanswered?: React.ReactNode;
    undecided?: React.ReactNode;
    halfcorrect?: React.ReactNode;
  };
}

// 统计数据类型
export interface StatsData {
  totalStudents: number;
  answeredStudents: number;
  correctAnswers: number;
  incorrectAnswers: number;
  correctRate: number;
  avgTime: {
    minutes: number;
    seconds: number;
  };
  optionStats: OptionStat[];
}

export interface OptionStat {
  key: string;
  count: number;
  percentage: number;
  status: "correct" | "incorrect" | "unanswered" | "halfcorrect" | "undecided";
  students: Array<{
    studentId: number;
    isCorrect: boolean;
    answerResult?: number;
    costTime?: number;
  }>;
}

const statsMap = {
  correct: "正确",
  incorrect: "错误",
  unanswered: "未作答",
  halfcorrect: "半对",
  undecided: "未判定",
};
// 选项状态类型
type AnswerStatus =
  | "correct"
  | "incorrect"
  | "unanswered"
  | "halfcorrect"
  | "undecided";

// 默认图标组件
const DefaultIconComponent = ({ status }: { status: AnswerStatus }) => {
  // const iconStyle =
  //   "w-5 h-5 rounded-full flex items-center justify-center text-white text-xs font-bold";

  // switch (status) {
  //   case "correct":
  //     return <div className={`${iconStyle} bg-green-500`}>✓</div>;
  //   case "incorrect":
  //     return <div className={`${iconStyle} bg-red-500`}>✗</div>;
  //   case "unanswered":
  //     return <div className={`${iconStyle} bg-gray-400`}>?</div>;
  // }
  switch (status) {
    case "correct":
      return <IcCorrect className="h-6 w-6" width={24} height={24} />;
    case "incorrect":
      return <IcIncorrect className="h-6 w-6" width={24} height={24} />;
    case "unanswered":
      return <IcUnanswered className="h-6 w-6" width={24} height={24} />;
    case "halfcorrect":
      return <IcHalfcorrect className="h-6 w-6" width={24} height={24} />;
    case "undecided":
      return <IcUnanswered className="h-6 w-6" width={24} height={24} />;
  }
};

export default function Stats({
  qaContent,
  renderCustomStats,
  totalStudents,
  getStudentInfo,
  icons,
}: StatsProps) {
  const [activeTab, setActiveTab] = useState<string>();
  const { viewMode, viewModeNew, studentListMap } = useTaskContext();
  const [tabs, setTabs] = useState<Array<{ id: string; label: string }>>([]);
  const [currentStats, setCurrentStats] = useState<StatsData>({
    totalStudents: 0,
    answeredStudents: 0,
    correctAnswers: 0,
    incorrectAnswers: 0,
    correctRate: 0,
    avgTime: { minutes: 0, seconds: 0 },
    optionStats: [],
  });
  const [currentSubQuestionAnswer, setCurrentSubQuestionAnswer] =
    useState<QaContentType>();

  const handleSetTabs = useCallback(() => {
    // console.log("setTabs", qaContent);
    if (
      !qaContent.subQuestionAnswers ||
      qaContent.subQuestionAnswers?.length === 0
    )
      return;
    const tempTabs: { id: string; label: string }[] = [];
    qaContent.subQuestionAnswers?.forEach((subQuestion) => {
      tempTabs.push({
        id: subQuestion.questionIndexStr || "",
        label: `题目${subQuestion.questionIndexStr || ""}`,
      });
    });
    setTabs([...tempTabs]);
    setActiveTab(tempTabs[0].id);
  }, [qaContent]);

  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab);
    },
    [activeTab]
  );

  // 定义标签配置
  // const tabs: Array<{ id: string; label: string }> = [
  //   { id: "1", label: "题目1" },
  //   { id: "2", label: "题目2-1" },
  //   { id: "3", label: "题目2-2" },
  // ];
  // 计算统计数据
  const calculateStats = useCallback(
    (tab?: string): StatsData => {
      if (tab) {
        const subQuestionAnswer = qaContent.subQuestionAnswers?.find(
          (item) => item.questionIndexStr === tab
        );
        setCurrentSubQuestionAnswer(subQuestionAnswer || qaContent);
        const answeredStudents = subQuestionAnswer?.answers?.length || 0;
        const correctAnswers =
          subQuestionAnswer?.answers?.filter((detail) => detail.isCorrect)
            .length || 0;
        const incorrectAnswers = answeredStudents - correctAnswers;
        const correctRate =
          answeredStudents > 0 ? (correctAnswers / answeredStudents) * 100 : 0;
        const avgTime = (subQuestionAnswer?.avgCostTime || 0) / 1000;
        const minutes = Math.floor(avgTime / 60);
        const seconds = Math.round(avgTime % 60);

        const optionStudentMap = new Map<
          string,
          Array<{
            studentId: number;
            isCorrect: boolean;
            answerResult?: number;
            costTime?: number;
          }>
        >();
        const answeredStudentIds = new Set<number>();

        if (
          subQuestionAnswer?.questionAnswerMode === 1 ||
          subQuestionAnswer?.questionAnswerMode === 2
        ) {
          subQuestionAnswer?.answers?.forEach((detail) => {
            answeredStudentIds.add(detail.studentId);
            const answer = detail.answer || "未作答";
            if (!optionStudentMap.has(answer)) {
              optionStudentMap.set(answer, []);
            }
            optionStudentMap.get(answer)?.push({
              studentId: detail.studentId,
              isCorrect: detail.isCorrect,
              answerResult: detail.answerResult,
              costTime: detail.costTime,
            });
          });
        } else {
          subQuestionAnswer?.answers?.forEach((detail) => {
            answeredStudentIds.add(detail.studentId);
            const answer =
              detail.answerResult === 1
                ? "正确"
                : detail.answerResult === 2
                  ? "错误"
                  : detail.answerResult === 3
                    ? "半对"
                    : detail.answerResult === 99
                      ? "未判定"
                      : "未作答";
            if (!optionStudentMap.has(answer)) {
              optionStudentMap.set(answer, []);
            }
            optionStudentMap.get(answer)?.push({
              studentId: detail.studentId,
              isCorrect: detail.isCorrect,
              answerResult: detail.answerResult,
              costTime: detail.costTime,
            });
          });
        }

        if (totalStudents) {
          const unansweredCount = totalStudents - answeredStudents;
          if (unansweredCount > 0) {
            const studentIds = { ...studentListMap.value };
            answeredStudentIds.forEach((id) => {
              delete studentIds[id];
            });
            // 这里我们创建虚拟的未作答学生ID
            // const unansweredStudents = Array.from(
            //   { length: unansweredCount },
            //   (_, i) => ({
            //     studentId: -1 - i, // 使用负数作为虚拟ID
            //     isCorrect: false,
            //   })
            // );
            const unansweredStudents = Object.values(studentIds).map(
              (student) => ({
                studentId: student.studentId,
                studentName: student.studentName,
                avatar: student.avatar,
                isCorrect: false,
              })
            );
            optionStudentMap.set("未作答", unansweredStudents);
          }
        }

        const optionStats: OptionStat[] = Array.from(optionStudentMap.entries())
          .map(([key, students]) => ({
            key,
            students,
            count: students.length,
            percentage: totalStudents
              ? (students.length / totalStudents) * 100
              : 0,
            status: students.some((s) => s.isCorrect || s.answerResult === 1)
              ? ("correct" as const)
              : students.some((s) => s.answerResult === 3)
                ? ("halfcorrect" as const)
                : students.some((s) => s.answerResult === 99)
                  ? ("undecided" as const)
                  : key === "未作答"
                    ? ("unanswered" as const)
                    : ("incorrect" as const),
          }))
          .sort((a, b) => {
            // 未作答放最后
            if (a.key === "未作答") return 1;
            if (b.key === "未作答") return -1;
            if (a.key === "正确") return -1;
            if (b.key === "正确") return 1;
            // 按选项字母顺序排序 (A, B, C, D...)
            return a.key.localeCompare(b.key);
          });
        if (qaContent.questionAnswerMode === 2) {
          optionStats.sort((a, b) => {
            if (a.key === "未作答") return 1;
            if (b.key === "未作答") return -1;
            return a.status.localeCompare(b.status);
          });
        }

        return {
          totalStudents: totalStudents || answeredStudents,
          answeredStudents,
          correctAnswers,
          incorrectAnswers,
          correctRate,
          avgTime: { minutes, seconds },
          optionStats,
        };
      } else {
        setCurrentSubQuestionAnswer(qaContent);
        const answeredStudents =
          qaContent.answerCount || qaContent.answerDetails.length;
        const correctAnswers = qaContent.answerDetails.filter(
          (detail) => detail.isCorrect || detail.answerResult === 1
        ).length;
        const incorrectAnswers =
          qaContent.incorrectCount || answeredStudents - correctAnswers;
        const correctRate =
          answeredStudents > 0 ? (correctAnswers / answeredStudents) * 100 : 0;

        // 时间格式转换
        const avgTime = qaContent.avgCostTime / 1000;
        const minutes = Math.floor(avgTime / 60);
        const seconds = Math.round(avgTime % 60);

        // 计算选项分布数据
        const optionStudentMap = new Map<
          string,
          Array<{
            studentId: number;
            isCorrect: boolean;
            answerResult?: number;
            costTime?: number;
          }>
        >();
        const answeredStudentIds = new Set<number>();

        // 初始化答案分布计数
        if (
          qaContent.questionAnswerMode === 1 ||
          qaContent.questionAnswerMode === 2
        ) {
          qaContent.answerDetails.forEach((detail) => {
            answeredStudentIds.add(detail.studentId);
            const answer = detail.answer || "未作答";
            if (!optionStudentMap.has(answer)) {
              optionStudentMap.set(answer, []);
            }
            optionStudentMap.get(answer)?.push({
              studentId: detail.studentId,
              isCorrect: detail.isCorrect,
              answerResult: detail.answerResult,
              costTime: detail.costTime,
            });
          });
        } else {
          qaContent.answerDetails.forEach((detail) => {
            answeredStudentIds.add(detail.studentId);
            const answer =
              detail.answerResult === 1
                ? "正确"
                : detail.answerResult === 2
                  ? "错误"
                  : detail.answerResult === 3
                    ? "半对"
                    : detail.answerResult === 99
                      ? "未判定"
                      : "未作答";
            if (!optionStudentMap.has(answer)) {
              optionStudentMap.set(answer, []);
            }
            optionStudentMap.get(answer)?.push({
              studentId: detail.studentId,
              isCorrect: detail.isCorrect,
              answerResult: detail.answerResult,
              costTime: detail.costTime,
            });
          });
        }

        // 如果提供了总学生数，添加未作答的学生
        if (totalStudents) {
          const unansweredCount = totalStudents - answeredStudents;
          if (unansweredCount > 0) {
            const studentIds = { ...studentListMap.value };
            answeredStudentIds.forEach((id) => {
              delete studentIds[id];
            });
            // 这里我们创建虚拟的未作答学生ID
            // const unansweredStudents = Array.from(
            //   { length: unansweredCount },
            //   (_, i) => ({
            //     studentId: -1 - i, // 使用负数作为虚拟ID
            //     isCorrect: false,
            //   })
            // );
            const unansweredStudents = Object.values(studentIds).map(
              (student) => ({
                studentId: student.studentId,
                studentName: student.studentName,
                avatar: student.avatar,
                isCorrect: false,
              })
            );
            optionStudentMap.set("未作答", unansweredStudents);
          }
        }
        // 转换为数组并排序
        const optionStats: OptionStat[] = Array.from(optionStudentMap.entries())
          .map(([key, students]) => ({
            key,
            students,
            count: students.length,
            percentage: totalStudents
              ? (students.length / totalStudents) * 100
              : 0,
            status: students.some((s) => s.isCorrect || s.answerResult === 1)
              ? ("correct" as const)
              : students.some((s) => s.answerResult === 3)
                ? ("halfcorrect" as const)
                : students.some((s) => s.answerResult === 99)
                  ? ("undecided" as const)
                  : key === "未作答"
                    ? ("unanswered" as const)
                    : ("incorrect" as const),
          }))
          .sort((a, b) => {
            // 未作答放最后
            if (a.key === "未作答") return 1;
            if (b.key === "未作答") return -1;
            if (a.key === "正确") return -1;
            if (b.key === "正确") return 1;
            // 按选项字母顺序排序 (A, B, C, D...)
            return a.key.localeCompare(b.key);
          });
        if (qaContent.questionAnswerMode === 2) {
          optionStats.sort((a, b) => {
            if (a.key === "未作答") return 1;
            if (b.key === "未作答") return -1;
            return a.status.localeCompare(b.status);
          });
        }

        return {
          totalStudents: totalStudents || answeredStudents,
          answeredStudents,
          correctAnswers,
          incorrectAnswers,
          correctRate,
          avgTime: { minutes, seconds },
          optionStats,
        };
      }
    },
    [qaContent, studentListMap.value, totalStudents]
  );

  // const stats = calculateStats();
  const handleSetCurrentStats = useCallback(() => {
    const tempStats = calculateStats(activeTab);
    setCurrentStats({ ...tempStats });
  }, [activeTab, calculateStats]);

  useUpdateEffect(() => {
    handleSetCurrentStats();
  }, [activeTab]);

  useUpdateEffect(() => {
    handleSetTabs();
  }, [qaContent]);

  useUpdateEffect(() => {
    handleSetCurrentStats();
  }, [handleSetCurrentStats]);

  useMount(() => {
    handleSetTabs();
    handleSetCurrentStats();
  });

  // 如果提供了自定义渲染函数，使用它
  if (renderCustomStats) {
    return <>{renderCustomStats(currentStats)}</>;
  }

  const getStudentAnswerStats = () => {
    if (currentSubQuestionAnswer?.answerDetails.length === 0)
      return "unanswered";
    const studentId = currentSubQuestionAnswer?.answerDetails[0].studentId;
    const studentAnswer = currentStats?.optionStats.find((option) => {
      return option.students.find((student) => student.studentId === studentId);
    });
    return studentAnswer?.status || "unanswered";
  };
  // console.log("currentQu", qaContent, currentSubQuestionAnswer);
  // 默认渲染
  return (
    <div className="mx-auto w-full font-sans">
      <h2 className="mb-3 text-base font-medium text-gray-900">作答详情</h2>
      {tabs.length > 1 && (
        <BaseTabNav
          activeTab={activeTab || ""}
          onTabChange={handleTabChange}
          tabs={tabs as Array<{ id: string; label: string }>}
          className="sticky top-0 z-50 mb-2 border-none"
          tabClassName="text-sm flex-auto"
        />
      )}
      <div
        className={`mb-4 rounded-xl border border-gray-200 bg-white px-5 py-4 ${viewMode.value === "student" || viewModeNew.value === "student" ? "!bg-blue-50" : ""}`}
      >
        {/* <h2 className="text-base font-medium text-gray-900 mb-3">作答统计</h2> */}

        <div
          className={`w-full items-center ${viewMode.value === "student" || viewModeNew.value === "student" ? "flex" : "grid grid-cols-4 justify-between"}`}
        >
          {/* 正确率 */}
          {viewMode.value !== "student" && viewModeNew.value !== "student" && (
            <div className="flex-1">
              <div className="flex items-baseline gap-1">
                <p className="text-3xl font-normal text-gray-900">
                  {currentStats ? Math.floor(currentStats.correctRate) : 0}
                </p>
                <p className="text-sm font-normal text-gray-900">%</p>
              </div>
              <p className="mt-2 text-sm font-normal text-gray-500">
                答题正确率
              </p>
            </div>
          )}
          {(viewMode.value === "student" ||
            viewModeNew.value === "student") && (
            <div className="mr-[30px] w-[100px] text-center">
              <div className="mt-2 flex items-baseline justify-center gap-1">
                <DefaultIconComponent
                  status={getStudentAnswerStats() as AnswerStatus}
                />
              </div>
              <p className="mt-4 text-sm text-gray-500">
                {statsMap[getStudentAnswerStats() as AnswerStatus] || ""}
              </p>
            </div>
          )}

          {/* 答对人数 */}
          {viewMode.value !== "student" && viewModeNew.value !== "student" && (
            <div className="relative flex-1">
              <div className="border-l-1 absolute -left-6 top-6 h-6 w-0 border-[#dbdee9]"></div>
              <div className="text-3xl font-normal text-green-600">
                {currentStats?.correctAnswers}
              </div>
              <p className="mt-2 text-sm font-normal text-gray-500">答对人数</p>
            </div>
          )}

          {/* 答错人数 */}
          {viewMode.value !== "student" && viewModeNew.value !== "student" && (
            <div className="relative flex-1">
              <div className="border-l-1 absolute -left-6 top-6 h-6 w-0 border-[#dbdee9]"></div>
              <div className="text-3xl font-bold text-red-600">
                {currentStats?.incorrectAnswers}
              </div>
              <p className="mt-2 text-sm text-gray-500">答错人数</p>
            </div>
          )}

          {/* 平均时间 */}
          <div className="relative flex-1">
            <div className="border-l-1 absolute -left-6 top-6 h-6 w-0 border-[#dbdee9]"></div>
            <div className="flex items-baseline">
              <span className="text-3xl font-normal text-gray-900">
                {viewMode.value !== "student" && viewModeNew.value !== "student"
                  ? currentStats?.avgTime.minutes || 0
                  : currentSubQuestionAnswer &&
                      currentSubQuestionAnswer.answerDetails.length > 0
                    ? Math.floor(
                        (currentSubQuestionAnswer.answerDetails[0].costTime ||
                          0) /
                          1000 /
                          60
                      )
                    : 0}
              </span>
              <span className="mb-1 mr-1 text-sm font-normal text-gray-900">
                分钟
              </span>
              <span className="text-3xl font-normal text-gray-900">
                {viewMode.value !== "student" && viewModeNew.value !== "student"
                  ? currentStats?.avgTime.seconds || 0
                  : currentSubQuestionAnswer &&
                      currentSubQuestionAnswer.answerDetails.length > 0
                    ? Math.floor(
                        ((currentSubQuestionAnswer.answerDetails[0].costTime ||
                          0) /
                          1000) %
                          60
                      )
                    : 0}
              </span>
              <span className="mb-1 text-sm font-normal text-gray-900">秒</span>
            </div>
            <p className="mt-2 text-sm font-normal text-gray-500">
              {viewMode.value !== "student" && viewModeNew.value !== "student"
                ? "平均答题时长"
                : "答题时长"}
            </p>
          </div>
        </div>
      </div>

      {/* 选项列表 */}
      {viewMode.value !== "student" &&
        viewModeNew.value !== "student" &&
        currentStats?.optionStats.map((option) => (
          <AnswerRow
            key={option.key}
            status={option.status}
            option={option.key}
            count={option.count}
            percentage={option.percentage}
            students={option.students}
            getStudentInfo={getStudentInfo}
            icons={icons}
            qaContent={qaContent}
          />
        ))}

      {/* 作答结果 */}
      {(viewMode.value === "student" || viewModeNew.value === "student") && (
        <div className="mb-3 rounded-xl border-none bg-blue-50 p-4">
          <h2 className="mb-3 text-base font-medium text-gray-900">作答结果</h2>
          {!currentSubQuestionAnswer?.answerDetails[0]?.answer ||
          !currentSubQuestionAnswer?.answerDetails[0]?.answer.includes(
            "https://"
          ) ? (
            <div>
              <span className="mr-4 text-sm text-gray-600">
                {qaContent.questionAnswerMode === 1 ||
                qaContent.questionAnswerMode === 2
                  ? "选项"
                  : "答案"}
              </span>
              <span className="text-sm text-red-600">
                {currentSubQuestionAnswer?.answerDetails[0]?.answer || "未作答"}
              </span>
            </div>
          ) : (
            <div className="flex gap-4">
              {(currentSubQuestionAnswer?.answerDetails[0]?.answer || "")
                .split(";")
                .map((imgUrl: string, index) => (
                  <ImageViewer key={index} imageUrl={imgUrl} />
                ))}
              {currentSubQuestionAnswer?.answerDetails[0]?.answer && (
                <div className="text-gray-4 flex items-end justify-center text-sm font-normal">
                  （可上传
                  <span>
                    {
                      (
                        currentSubQuestionAnswer?.answerDetails[0]?.answer || ""
                      ).split(";").length
                    }
                    /3
                  </span>
                  ）
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface AnswerRowProps {
  status: AnswerStatus;
  option: string;
  count: number;
  percentage: number;
  students: Array<{
    studentId: number;
    isCorrect: boolean;
    answerResult?: number;
    costTime?: number;
  }>;
  getStudentInfo?: (
    studentId: number
  ) => { name: string; avatar?: string } | null;
  icons?: {
    correct?: React.ReactNode;
    incorrect?: React.ReactNode;
    unanswered?: React.ReactNode;
    halfcorrect?: React.ReactNode;
    undecided?: React.ReactNode;
  };
  qaContent: QaContentType;
}

function AnswerRow({
  status,
  option,
  count,
  percentage,
  students,
  getStudentInfo,
  icons,
  qaContent,
}: AnswerRowProps) {
  const [open, setOpen] = useState(false);
  const [answer, setAnswer] = useState<AnswerDetail>();
  const IconComponent = icons?.[status] || (
    <DefaultIconComponent status={status} />
  );
  const handleStudentShowDrawer = (studentId: number) => {
    if (
      status !== "unanswered" &&
      qaContent.questionAnswerMode !== 1 &&
      qaContent.questionAnswerMode !== 2
    ) {
      const studentAnswer: AnswerDetail | undefined =
        qaContent.answerDetails.find(
          (student) => student.studentId === studentId
        );
      setAnswer(studentAnswer);
      setOpen(true);
    }
  };
  return (
    <>
      <div className="mb-3 rounded-xl border-none bg-blue-50 p-4">
        <div className="flex items-start">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">{IconComponent}</div>
            <div className="w-16 text-xs font-medium">{option}</div>

            <div className="flex items-center">
              <div className="h-2 w-32 rounded-full bg-gray-200">
                <div
                  className="h-2 rounded-full bg-blue-500"
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
              <div className="text-gray-2 ml-2 text-sm font-normal">
                {count}人
              </div>
            </div>
          </div>

          {getStudentInfo && (
            <div className="ml-6 flex flex-1 flex-wrap gap-x-3 gap-y-4">
              {students.map((student) => {
                const studentInfo = getStudentInfo(student.studentId);
                if (!studentInfo) return null;

                return (
                  <div
                    key={student.studentId}
                    className={`flex items-center gap-2 ${status !== "unanswered" && qaContent.questionAnswerMode !== 1 && qaContent.questionAnswerMode !== 2 && "cursor-pointer"}`}
                    onClick={() => handleStudentShowDrawer(student.studentId)}
                  >
                    {/* {studentInfo.avatar && (
                    <img
                      className="h-6 w-6 rounded-full border-2 border-white"
                      src={studentInfo.avatar}
                      alt="学生"
                    />
                  )} */}
                    <Avatar
                      src={studentInfo.avatar || ""}
                      alt={studentInfo.name || ""}
                      className="mr-1 h-6 w-6 rounded-full"
                    />
                    <span className="text-gray-1 whitespace-nowrap text-sm font-normal">
                      {studentInfo.name}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
      <QuestionAnswerDetailDrawer
        open={open}
        onOpenChange={setOpen}
        answerData={
          answer as unknown as {
            answer: string;
            answerResult: number;
            costTime: number;
            isCorrect: boolean;
            studentId: number;
          }
        }
      />
    </>
  );
}
