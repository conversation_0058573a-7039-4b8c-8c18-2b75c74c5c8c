"use client";
import {
  fetchHomeworkDetail,
  useTaskContext,
} from "@/app/homework/[id]/_context/task-context";
import { OfflineCommunicationDrawer } from "@/components/common/offline-communication";
import BaseTabNav from "@/components/common/tab-nav";
import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { useFeedbackByType } from "@/hooks/useReportFeedback";
import comingSoon from "@/public/images/coming-soon.png";

import ProcessIcon from "@/public/icons/ic_report.svg";
import { getStudentDetail, PanelItem } from "@/services/homework";
import { StudentBase, StudentDetailV2 } from "@/types/homeWork";
import { Button } from "@/ui/button";
import { ScrollArea } from "@/ui/scroll-area";
import { InputSearch } from "@/ui/searchInput";
import { Separator } from "@/ui/separator";
import { toast } from "@/ui/toast";
import { umeng, Umeng<PERSON>ategory, UmengHomeworkAction } from "@/utils";
import { batch, useComputed, useSignal } from "@preact-signals/safe-react";
import { useMount, useRequest, useUnmount, useUpdateEffect } from "ahooks";
import { format } from "date-fns";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useCallback, useMemo, useRef, useState } from "react";
import { TabItem, TabValue } from "../../../../_components/layout/TabNav";
import { DrawerContainer } from "../../../layout/Header/course-select-drawer";
import { ThumbsUpButton } from "../../../thumbs-up-button";
import { QuestionItemSkeleton } from "../../Results";
import { QuestionDetailDrawer } from "../../Results/components/QuestionDetailDrawer";
import QuestionListWrapper from "../../Results/components/QuestionListWrapper";
import Toolbar from "../../Results/components/Toolbar";
import {
  selectQuestion,
  useAnswerResults,
  useAnswerResultsPolling,
} from "../../Results/store/answers";
import { useStudentRemind } from "../hooks/useStudentRemind";
import { EvaluationDrawer } from "./evaluation-drawer";
import { PushReminderDrawer } from "./push-reminder-drawer";
import { StudentRemindDrawer, StudentStatus } from "./student-remind-drawer";

const tabWidth = {
  results: {
    width: 63,
    left: 0,
  },
  ask: {
    width: 63,
    left: 87,
  },
};
let isBack = false;
export default function StudentDetail() {
  const { userInfo } = useApp();
  const searchParams = useSearchParams();
  const { closeFeedback } = useFeedbackByType();
  const [pushReminderOpen, setPushReminderOpen] = useState(false);
  const [evaluationOpen, setEvaluationOpen] = useState(false);
  const [offlineCommunicationOpen, setOfflineCommunicationOpen] =
    useState(false);
  const [studentRemindOpen, setStudentRemindOpen] = useState(false);
  const {
    studentData,
    homeworkData,
    taskData,
    updateStudentData,
    taskType,
    currentCourse,
    useFetchHomeworkDetailRequest,
    setStudentListMap,
    showStudentDetail,
    taskId,
    classData,
    viewModeNew,
    selectedCourseList,
  } = useTaskContext();
  const {
    filter,
    setFilter,
    fetchParams,
    answerResultsState,
    pagination,
    panel,
    loading: loadingAnswer,
    fetchAnswerResults,
    selectedQuestionId,
  } = useAnswerResults();
  const data = useComputed(() => studentData.value);
  const publishTime = useComputed(() =>
    format((data.value.publishTime || 0) * 1000, "yyyy.MM.dd HH:mm")
  );
  const deadline = useComputed(() =>
    format((data.value.deadline || 0) * 1000, "yyyy.MM.dd HH:mm")
  );
  const { useBatchPraise } = useStudentRemind();
  //   const newStudents = homeworkData.value?.students?.filter(
  //     (student) => student.studentId === Number(studentData.value.studentId)
  //   );
  const { run } = useRequest(
    async () => {
      if (!taskData.value?.id || !studentData.value.studentId) return;
      console.log("Fetching student detail for:", studentData.value.studentId);
      return getStudentDetail(
        taskData.value.id,
        taskData.value.classes[0].assignId,
        Number(studentData.value.studentId),
        userInfo?.currentSchoolID || 0,
        "task_praise",
        currentCourse.value?.id
      );
    },
    {
      manual: true,
      debounceWait: 1000,
      onSuccess: (data) => {
        if (!data) return;
        const newStudentData = {
          id: data.studentId,
          name: data.studentName,
          avatar: data.avatar,
          className: "", // API中暂无此字段
          status:
            data.studentAccuracyRate >=
            (data.classAccuracyRate ||
              homeworkData.value?.detail?.avgAccuracy ||
              0)
              ? "good"
              : ("attention" as StudentStatus),
          praiseCount: data.praiseCount,
          attentionCount: data.attentionCount,
          performance: {
            homeworkAccuracy: parseFloat(
              (data.studentAccuracyRate * 100).toFixed(2)
            ),
            averageAccuracy: parseFloat(
              (
                (data.classAccuracyRate ||
                  homeworkData.value?.detail?.avgAccuracy ||
                  0) * 100
              ).toFixed(2)
            ),
            completionRate: parseFloat(
              (data.studentCompletedProgress * 100).toFixed(2)
            ),
            averageCompletionRate: parseFloat(
              (
                (data.classCompletedProgress ||
                  homeworkData.value?.detail?.avgProgress ||
                  0) * 100
              ).toFixed(2)
            ),
          },
          feedback: {
            description: data.attentionText || "",
            recommendations: data.attentionTextList || [],
          },
          pushDefaultText: data.pushDefaultText || "",
          praiseDefaultText: data.praiseDefaultText || "",
          courseUrl: data.courseUrl || "",
        };
        updateStudentData(newStudentData);
      },
      onError: (err) => {
        console.error("Failed to fetch student detail:", err);
        toast.error("获取学生信息失败", {
          description: "请稍后重试",
        });
        // updateStudentData(null); // Clear data on error
        return;
      },
    }
  );
  const handleRouteChange = useCallback((e: PopStateEvent) => {
    // history.pushState(null, "", window.location.href);
    e.preventDefault();
    console.log("history", history);
    // history.go(-1);
    closeFeedback();
    isBack = true;
    handleBackNavigation();
  }, []);
  useMount(() => {
    // if (viewMode.value === "student") {
    if (currentCourse.value) {
      fetchParams.value.resourceId = currentCourse.value.id;
    }
    run();
    runAnswer();
    window.addEventListener("popstate", handleRouteChange);
    history.pushState(null, "", window.location.href);
    // }
  });
  useUnmount(() => {
    window.removeEventListener("popstate", handleRouteChange);
  });
  // 在 App Router 中，beforePopState 不可用，使用 useEffect 监听路由变化
  //   useUpdateEffect(() => {
  //     // 监听路由变化，当用户点击返回时触发
  //     const handleRouteChange = () => {
  //       handleBackNavigation();
  //     };

  //     // 添加事件监听器
  //     window.addEventListener("popstate", handleRouteChange);

  //     return () => {
  //       window.removeEventListener("popstate", handleRouteChange);
  //     };
  //   }, []);
  //   useUpdateEffect(() => {
  //     // setViewValue(viewMode.value);
  //     // if (viewMode.value === "student") {
  //     run();
  //     // }
  //   }, []);
  const { run: handleRunPraise } = useBatchPraise(
    [studentData.value] as unknown as StudentDetailV2[],
    () => {
      fetchHomeworkDetail();
      run();
    }
  );
  // 处理鼓励
  const handlePraise = () => {
    if (studentData.value.studentId) {
      // handleBatchPraise([Number(studentData.value.studentId)]);
      // console.log("handlePraise", studentData.value);
      handleRunPraise();
    }
  };

  // 处理去处理按钮点击
  const handleProcessClick = () => {
    setStudentRemindOpen(true);
  };
  const handleBackNavigation = () => {
    if (!isBack) {
      history.back();
      // history.go(-1);
    }
    showStudentDetail.value = false;
    currentCourse.value = searchParams?.get("courseId")
      ? selectedCourseList.value.find(
          (item) => item.id === searchParams?.get("courseId")
        ) || null
      : null;
    viewModeNew.value = "class";
  };
  const [activeTab, setActiveTab] = useState("results");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const tabs: TabItem<TabValue>[] = [
    { id: "results", label: "答题结果" },
    { id: "ask", label: "学生提问" },
  ];
  // 获取班级列表
  const classList =
    taskData.value?.homeworkData?.reports.map((report) => ({
      id: report.assignObject.id,
      name: report.assignObject.name,
      gradeName: report.assignObject.gradeName,
    })) || [];

  const { data: dataHomework, run: runHomework } =
    useFetchHomeworkDetailRequest();
  const {
    data: dataAnswer,
    loading: loadingAnswerPolling,
    run: runAnswer,
    cancel: cancelAnswer,
  } = useAnswerResultsPolling();

  useUpdateEffect(() => {
    if (dataHomework) {
      homeworkData.value = dataHomework;
      setStudentListMap(
        dataHomework.detail.studentReports as unknown as StudentBase[]
      );
    }
  }, [dataHomework]);

  useUpdateEffect(() => {
    let response;
    let panelResponse: { panel: PanelItem[] } = {
      panel: [],
    };
    if (dataAnswer && dataAnswer[0]) {
      response = dataAnswer[0];
      panelResponse = dataAnswer[1];
    } else {
      response = dataAnswer;
    }
    batch(() => {
      answerResultsState.value = {
        data: response,
        panel: panelResponse?.panel,
        loading: false,
        selectedQuestionId: null,
      };
      // 如果页码超过总页数，重置到第一页
      if (response) {
        if (
          fetchParams.value.page >
          Math.ceil(response.pageInfo.total / fetchParams.value.pageSize)
        ) {
          pagination.value.current = 1;
        }
      }
    });
  }, [dataAnswer]);
  const handleTabChange = useCallback(
    (tab: string) => {
      // 确保tab是TabValue类型
      const tabValue = tab as TabValue;
      const tabLabels: Record<TabValue, string> = {
        report: "student_tab",
        results: "question_tab",
        ask: "ask_tab",
      };

      // 只有当选择不同的标签页时才更新
      if (activeTab !== tabValue) {
        // 立即更新UI状态，提高响应速度
        setActiveTab(tabValue);

        // 延迟执行非关键操作，确保UI响应优先
        setTimeout(() => {
          console.log("[TabNav] Tab changed to:", tabValue);

          // 埋点操作延迟执行，不阻塞UI

          umeng.trackEvent(
            UmengCategory.HOMEWORK,
            UmengHomeworkAction.REPORT_STUDENT_TAB_CLICK,
            {
              tab: tabLabels[tabValue],
            }
          );
        }, 50);
      }
    },
    [activeTab, setActiveTab]
  );
  const classSelector = (
    <div className="flex items-center gap-3 text-sm">
      {/*  知识点搜索 */}
      {activeTab === "results" && (
        <InputSearch
          debounce
          wait={300}
          classNames={{
            input: " rounded-[1.125rem] text-sm",
          }}
          className="lg:w-50 xs:w-20 sm:w-30 h-6 md:w-40"
          placeholder="请输入题目关键词"
          value={filter.value.keyword}
          onChange={(value) => {
            setFilter({ keyword: value.target.value });
          }}
        />
      )}

      {/* 课程选择器 - 无论在哪种视图下都显示（如果有的话） */}
      {taskType.value === 10 && (
        <div
          className="flex cursor-pointer items-center"
          onClick={() => {
            setIsDrawerOpen(true);
          }}
        >
          <span className="mr-1">课程：</span>
          <span className="mr-1 font-medium">
            {currentCourse.value?.name || "全部"}
          </span>
          <ChevronDown className="h-4 w-4" />
        </div>
      )}
    </div>
  );
  const memoizedTabNav = useMemo(
    () => (
      <BaseTabNav
        activeTab={activeTab}
        onTabChange={handleTabChange}
        tabs={tabs}
        rightContent={classSelector}
        className="border-line-2 sticky top-0 h-[42px] border-b"
        tabClassName="font-semibold"
        tabWidth={tabWidth}
      />
    ),
    [activeTab, handleTabChange, tabs, classSelector]
  );
  const [drawerOpen, setDrawerOpen] = useState(false);
  const isPanlLoading = useSignal(false);
  const [isInit, setIsInit] = useState(false);
  // 使用 useRef 跟踪数据加载状态
  const dataLoaded = useRef(false);
  const runDelayRef = useRef<NodeJS.Timeout>(null);
  // 监听标签页变化和数据加载
  useUpdateEffect(() => {
    // 如果当前标签页是答题结果，并且有必要的参数，则加载数据
    // fetchAnswerResults(fetchParams.value);
    if (activeTab === "results" && taskId && classData.value?.assignId) {
      // 如果数据尚未加载，则加载数据
      if (!dataLoaded.current) {
        // 加载答题数据
        // fetchAnswerResults(fetchParams.value);
        // console.log("run fetchAnswerResults");
        runAnswer();
        // 标记数据已加载
        dataLoaded.current = true;
      }
    } else {
      // 如果不是答题结果标签页，重置加载标志，以便下次切换到此标签页时重新加载
      dataLoaded.current = false;
      cancelAnswer();
      if (runDelayRef.current) {
        clearTimeout(runDelayRef.current);
      }
    }

    // 组件卸载时重置加载标志
    return () => {
      if (activeTab !== "results") {
        dataLoaded.current = false;
        cancelAnswer();
        if (runDelayRef.current) {
          clearTimeout(runDelayRef.current);
        }
      }
    };
  }, [
    taskId,
    classData.value?.assignId,
    activeTab,
    fetchAnswerResults,
    fetchParams,
  ]);
  return (
    <div
      className={`bg-fill-light absolute w-full ${activeTab === "results" && answerResultsState.value?.data?.questionAnswers && answerResultsState.value?.data?.questionAnswers.length > 3 ? "h-auto" : "h-full"}`}
    >
      <div className="z-0 shrink-0 px-6">
        <PageHeader
          onBack={handleBackNavigation}
          needBack={true}
          className="pl-0"
        >
          <div className="flex flex-1 flex-col pl-4">
            {/* 顶部导航栏 - 三部分结构 */}
            <div className="h-17.5 flex items-center justify-between py-3">
              <div className="flex flex-1 items-center gap-4">
                {/* 1. 返回按钮 */}
                {/* <div
            onClick={handleBackNavigation}
            className="flex cursor-pointer items-center gap-1"
          >
            <ReturnIcon className="h-9 w-9" />
          </div> */}

                {/* 2. 标题部分 - 上下结构 */}
                <div className="flex flex-col items-start justify-start">
                  <h1 className="text-gray-1 text-left text-[1.125rem] font-semibold leading-[150%]">
                    <div className="flex items-center">
                      {studentData.value.studentName}
                      <Separator
                        orientation="vertical"
                        className="bg-line-2 mx-2 !h-4 w-[1px]"
                      />
                      {data.value.title}
                    </div>
                  </h1>
                  {/* 详细信息区域 - 放在标题下方 */}
                  <div className="flex justify-start">
                    <div className="text-gray-4 inline-flex items-center gap-2 text-[0.75rem] font-normal leading-[150%]">
                      <div className="flex items-center">
                        <span className="text-gray-4">发布时间:</span>
                        <span className="ml-2 font-normal">
                          {publishTime.value}
                        </span>
                      </div>
                      <Separator
                        orientation="vertical"
                        className="bg-line-2 !h-2 w-[1px]"
                      />

                      <div className="flex items-center">
                        <span className="text-gray-4">截止时间:</span>
                        <span className="ml-2 font-normal">
                          {deadline.value}
                        </span>
                      </div>

                      <>
                        <Separator
                          orientation="vertical"
                          className="bg-line-2 !h-2 w-[1px]"
                        />
                        <div className="flex items-center">
                          <span className="text-gray-4">完成进度:</span>
                          <span className="ml-2 font-normal">
                            {studentData.value?.progress
                              ? Math.ceil(studentData.value?.progress * 100)
                              : 0}
                            %
                          </span>
                        </div>
                      </>
                    </div>
                  </div>
                </div>
              </div>

              {/* 3. 右侧按钮区域 */}

              <div className="flex items-center gap-3">
                {/* 鼓励按钮 */}
                <ThumbsUpButton
                  variant="secondary"
                  className={`bg-transparent font-normal ${studentData.value.praiseCount >= 1 ? "text-primary-2 cursor-pointer" : "hover:bg-indigo-50"}`}
                  onClick={handlePraise}
                  disabled={studentData.value.praiseCount >= 1}
                  praiseCount={studentData.value.praiseCount}
                >
                  {studentData.value.praiseCount < 1 ? "鼓励一下" : "已鼓励"}
                </ThumbsUpButton>

                {/* 去处理按钮 */}
                <Button
                  className="text-0.875rem text-gray-2 flex h-9 cursor-pointer items-center gap-2 rounded-[1.125rem] border border-[#E9ECF5] bg-transparent font-[500] font-normal leading-[150%] hover:bg-indigo-50"
                  onClick={handleProcessClick}
                >
                  <ProcessIcon className="h-5 w-5" width={20} height={20} />
                  发消息
                </Button>
              </div>
            </div>

            {/* 设置抽屉 */}
            {/* <SettingsDrawer open={settingsOpen} onOpenChange={setSettingsOpen} /> */}

            {/* 学生视图相关抽屉 */}

            <>
              <PushReminderDrawer
                open={pushReminderOpen}
                onOpenChange={setPushReminderOpen}
                studentId={Number(studentData.value.studentId)}
              />

              <EvaluationDrawer
                open={evaluationOpen}
                onOpenChange={setEvaluationOpen}
                studentId={Number(studentData.value.studentId)}
              />

              <OfflineCommunicationDrawer
                open={offlineCommunicationOpen}
                onOpenChange={setOfflineCommunicationOpen}
              />

              <StudentRemindDrawer
                open={studentRemindOpen}
                onOpenChange={setStudentRemindOpen}
                studentId={Number(studentData.value.studentId)}
                type={studentData.value.studentType || "attention"}
              />
            </>
          </div>
        </PageHeader>
        <>
          {memoizedTabNav}

          {/* 课程选择抽屉 */}
          <DrawerContainer
            open={isDrawerOpen}
            onOpenChange={(open) => setIsDrawerOpen(open)}
            onConfirm={() => {
              fetchParams.value.resourceId = currentCourse.value?.id;
              runAnswer();
            }}
            viewValue="student"
          />
        </>
      </div>
      <div className="flex-1 will-change-transform">
        {activeTab === "results" ? (
          <div className="relative flex h-full flex-col">
            {/* <ResultFloatingButton
              resultPanlData={panel}
              isLoading={isPanlLoading}
            /> */}

            {/* 将整个内容区域放在一个滚动容器中 */}
            <ScrollArea className="flex-1 overflow-auto">
              <div className="flex flex-col">
                {/* 工具栏 - 普通堆叠 */}
                <Toolbar />

                {/* 题目列表 */}
                {loadingAnswer.value ? (
                  <div className="px-4">
                    <div className="flex flex-col gap-y-4 pb-2">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <QuestionItemSkeleton key={i} />
                      ))}
                    </div>
                  </div>
                ) : (
                  <QuestionListWrapper onClick={() => setDrawerOpen(true)} />
                )}
              </div>
            </ScrollArea>

            {/* 题目详情抽屉 */}
            <QuestionDetailDrawer
              open={drawerOpen}
              onOpenChange={(open) => {
                setDrawerOpen(open);
                if (open === true) {
                  cancelAnswer();
                } else {
                  runDelayRef.current = setTimeout(() => {
                    runAnswer();
                  }, 60000);
                }
                if (!open) {
                  selectQuestion(null);
                }
              }}
              questionId={selectedQuestionId.value || undefined}
              //   floatButton={
              //     <ResultFloatingButton
              //       resultPanlData={panel}
              //       isLoading={isPanlLoading}
              //     />
              //   }
            />
          </div>
        ) : (
          <div className="align-center pt-50 flex flex-col justify-center p-4">
            <Image
              src={comingSoon}
              alt="coming soom"
              className="w-30 m-auto"
            ></Image>

            <div className="text-gray-4 py-6 text-center text-sm">
              新功能敬请期待
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
