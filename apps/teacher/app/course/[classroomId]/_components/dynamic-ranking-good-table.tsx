"use client";
import IcExpand from "@/public/icons/ic_expand.svg";
import IcRetract from "@/public/icons/ic_retract.svg";

import IcZan from "@/public/icons/ic_zan.svg";
import { GetBehaviorCategorysResponse, StudentInfo } from "@/types/course";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import Avatar from "@/ui/tch-avatar";
import { Button } from "@/ui/tch-button";
import { umeng, UmengCategory, UmengCourseAction } from "@/utils";
import { cn } from "@/utils/utils";
import { useSessionStorageState } from "ahooks";
import { useMemo } from "react";

export function DynamicRankingGoodTable({
  setStudent,
  praiseStudentRequest,
  studentLatestBehaviorData,
  subject,
  isSubjectTeacher,
  isInClass,
}: {
  setStudent: (student: StudentInfo) => void;
  praiseStudentRequest: { run: (studentId: number[]) => void };
  studentLatestBehaviorData?: GetBehaviorCategorysResponse;
  subject: string;
  isSubjectTeacher: boolean;
  isInClass: boolean;
}) {
  const [isExpanded, setIsExpanded] = useSessionStorageState(
    "dynamic-ranking-good-table-is-expanded",
    {
      defaultValue: false,
    }
  );

  const displayPraiseList = useMemo(() => {
    const praiseList = studentLatestBehaviorData?.praiseList ?? [];

    return praiseList.slice(0, isExpanded ? undefined : 4);
  }, [studentLatestBehaviorData?.praiseList, isExpanded]);

  return (
    <div className={cn(isExpanded && "pb-8")}>
      {Boolean(displayPraiseList?.length) && (
        <Table className="table-fixed overflow-hidden rounded-t-lg">
          <TableHeader>
            <TableRow className="bg-green-5 border-b-0! hover:bg-green-5">
              <TableHead className="text-gray-2 h-9.25 w-45.5 pl-4 text-sm/normal font-normal">
                学生
              </TableHead>
              <TableHead className="text-gray-2 h-9.25 text-sm/normal font-normal">
                课堂行为
              </TableHead>
              <TableHead className="text-gray-2 h-9.25 w-1/10 text-center text-sm/normal font-normal">
                鼓励他们
              </TableHead>
            </TableRow>
          </TableHeader>

          <TableBody className="text-gray-1">
            {displayPraiseList.map((student) => (
              <TableRow
                key={student.studentId}
                className={cn(
                  `h-13 overflow-hidden border-[#e7ebe7] bg-white hover:bg-white`
                )}
              >
                <TableCell
                  className={cn("pl-4", student.isHandled && "opacity-50")}
                >
                  <div className="flex items-center gap-2.5">
                    <Avatar
                      src={student.avatarUrl}
                      alt="avatar"
                      className="h-6 w-6 active:opacity-80"
                      onClick={() => {
                        // setStudent(student);
                      }}
                    />

                    <span className="flex-1 truncate">
                      {student.studentName}
                    </span>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="flex items-center gap-2.5">
                    {student.behaviorTags.map((tag) => (
                      <span
                        className={cn(
                          "h-5.5 bg-green-1 inline-flex items-center justify-center rounded-xl px-1.5 text-xs/normal font-normal text-white",
                          (student.isHandled || !tag.isTrigger) && "opacity-50"
                        )}
                        key={tag.text}
                      >
                        {tag.text}
                      </span>
                    ))}

                    <span className="h-5.5 bg-primary-6 text-primary-1 ml-auto inline-flex items-center justify-center rounded-sm px-1.5 text-xs/normal font-normal">
                      已鼓励{student.praiseCount}次
                    </span>
                  </div>
                </TableCell>

                <TableCell className="text-center">
                  <Button
                    className={cn(
                      "border-line-2 h-7 w-11 rounded-[1.125rem]",
                      (student.isHandled || !isInClass) && "cursor-not-allowed"
                    )}
                    isIconOnly
                    onClick={() => {
                      if (student.isHandled || !isInClass) return;

                      praiseStudentRequest.run([student.studentId]);

                      umeng.trackEvent(
                        UmengCategory.COURSE,
                        UmengCourseAction.CLASSROOM_REPORT_SINGLE_LIKE,
                        {
                          single_like_action: {
                            subject: subject,
                            job: isSubjectTeacher ? "任课教师" : "班主任",
                          },
                        }
                      );
                    }}
                  >
                    <IcZan />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {Boolean(
        studentLatestBehaviorData?.praiseList &&
          studentLatestBehaviorData.praiseList.length > 4
      ) && (
        <div className="absolute bottom-0 left-0 right-0 flex h-16 items-center justify-center rounded-b-lg bg-[linear-gradient(0deg,_#FFF_-12.5%,_rgba(255,_255,_255,_0.00)_139.06%)]">
          <div
            className="h-6.25 border-line-3 bg-line-1 text-gray-2 flex cursor-pointer select-none items-center gap-2 rounded-2xl border-[1px] border-solid px-3 py-0.5 text-sm/normal active:opacity-80"
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
          >
            {isExpanded ? `收起展示` : "展开查看全部学生状态"}
            {isExpanded ? (
              <IcRetract className="cursor-pointer" />
            ) : (
              <IcExpand className="cursor-pointer" />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
