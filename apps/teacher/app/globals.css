@import "tailwindcss";
@import "tw-animate-css";
@config "../tailwind.config.ts";
@custom-variant dark (&:is(.dark *));
@font-face {
  font-family: "AlibabaPuHuiTi-3-55-Regular";
  font-style: normal;
  font-weight: 400;
  src: url("https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff2") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "AlibabaPuHuiTi-3-55-RegularL3";
  font-style: normal;
  font-weight: 400;
  src: url("https://static.xiaoluxue.com/assets/fonts/AlibabaPuHuiTi-3-55-RegularL3.woff2") format("truetype");
  font-display: swap;
}

/* shadcn-ui */
@theme inline {
  /* 背景 */
    --background: var(--color-fill-white);
    --foreground: var(--color-gray-2);
  
    /* 卡片 */
    --card: var(--color-fill-white);
    --card-foreground: var(--color-gray-2);
  
    /* 弹出框 */
    --popover: var(--color-fill-white);
    --popover-foreground: var(--color-gray-2);
  
    /* 主色 */
    --primary: var(--color-primary-2);
    --primary-foreground: var(--color-fill-white);
  
    /* 次要色 */
    --secondary: var(--color-fill-white);
    --secondary-foreground: var(--color-primary-2);
  
    /* 禁用 */
    --muted: var(--color-primary-4);
    --muted-foreground: var(--color-fill-white);
  
    /* 悬浮 */
    --accent: var(--color-primary-3);
    --accent-foreground: var(--color-fill-white);
  
    /* 警示 */
    --destructive: var(--color-danger-2);
    --destructive-foreground: oklch(0.577 0.245 27.325);
  
    /* 边框 */
    --border: var(--color-line-2);
    /* 输入组件边框 */
    --input: var(--color-line-3);
    /* 输入组件聚焦时边框 */
    --ring: var(--color-primary-2);
  
    /* 圆角 */
    --radius: 0.625rem;
  
    /* 图表颜色 目前还没用到 */
    /* --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08); */
  
    /* 侧边栏 */
    --sidebar: var(--color-primary-6);
    --sidebar-foreground: var(--color-gray-3);
    --sidebar-width: 10.5625rem;
      --sidebar-width-icon: 3rem;
    /* --sidebar-primary: var(--color-primary-2);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: var(--color-primary-2);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0); */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
}

/* tch */
@theme {
  /* 断点 */
  --breakpoint-tablet: 62.5rem; /* 平板 1000px */
  --breakpoint-laptop: 80rem; /* 笔记本 1280px */
  --breakpoint-desktop: 120rem; /* 台式机 1920px */
  --breakpoint-large: 160rem; /* 大屏幕 2560px */

  /* 文字大小 */
  --text-xxs: 0.625rem;
  --text-xxs--line-height: calc(1 / 0.625);

  /* 品牌色 */
  --color-primary-1: #4A4FED;  /* 激活-字色 */
  --color-primary-2: #6574FC;  /* 主按钮-常规 */
  --color-primary-3: #9CB1FC;  /* 思维导图-1级 */
  --color-primary-4: #BBCBFC;  /* primary-4 */
  --color-primary-5: #E0EAFF;  /* primary-5 */
  --color-primary-6: #EBF1FF;  /* 白底 悬浮 hover */

  /* 辅助色 - Blue */
  --color-blue-0: #0071df;  /* blue-0 */
  --color-blue-1: #2091ff;  /* blue-1 */
  --color-blue-2: #60b0ff;  /* blue-2 */
  --color-blue-3: #B0D8FF;  /* 课程-主功能背景 */
  --color-blue-4: #D0E8FF;  /* 思维导图-3级 */
  --color-blue-5: #E9F4FF;  /* 课程-任务卡背景 个人中心-校长背景色 */

  /* 辅助色 - Purple */
  --color-purple-1: #6f60ff;  /* purple-1 */
  --color-purple-2: #A99FFF;  /* purple-2 */
  --color-purple-3: #D1CCFF;  /* 作业-主功能背景 */
  --color-purple-4: #E3E0FF;  /* 思维导图-2级 作业-任务卡背景点击 */
  --color-purple-5: #F0F0FF;  /* 作业-任务卡背景 */

  /* 辅助色 - Green */
  --color-green-0: #2dc01f;  /* 文字 */
  --color-green-1: #4de03f;  /* green-1 */
  --color-green-2: #80e976;  /* green-2 */
  --color-green-3: #B4F2AE;  /* 测试-主功能背景 */
  --color-green-4: #D9F8D7;  /* 思维导图-4级 背景点击 */
  --color-green-5: #E9FBE4;  /* 测试-任务卡背景 个人中心-教师背景色 */

  /* 辅助色 - Orange */
  --color-orange-0: #DB7505;  /* orange-0 */
  --color-orange-1: #fa9524;  /* tag-内容推荐 */
  --color-orange-2: #fcb363;  /* orange-2 */
  --color-orange-3: #FDD1A0;  /* 资源-主功能背景 */
  --color-orange-4: #FEE3C6;  /* 资源-任务卡背景点击 */
  --color-orange-5: #FFF3E6;  /* 资源-任务卡背景 */

  /* 辅助色 - Danger */
  --color-danger-1: #E0443F;  /* 警示/文字 */
  --color-danger-2: #FA5A57;  /* 小红点 */
  --color-danger-3: #fca5a2;  /* danger-3 */
  --color-danger-4: #FEDEDD;  /* 个人中心-班主任背景色-点击 */
  --color-danger-5: #FFF0EB;  /* 个人中心-班主任背景色 取消按钮背景 */

  /* 辅助色 - Carmine */
  --color-carmine-0: #CC3373;  /* 文字 */
  --color-carmine-1: #E53E8C;  /* 时间文字 */
  --color-carmine-2: #FC77BC;  /* 时间线 */
  --color-carmine-3: #FFBDE2;  /* carmine-3 */
  --color-carmine-4: #FFDEF1;  /* 时间线-灰 */
  --color-carmine-5: #FFEBF6;  /* 个人中心-班主任任背景色 */

  /* 文字颜色 */
  --color-gray-1: #101019;  /* 重点强调 颜色 */
  --color-gray-2: #444963;  /* 正文 */
  --color-gray-3: #646B8A;  /* 次要内容 */
  --color-gray-4: #838BAB;  /* 辅助\描述 */
  --color-gray-5: #ABB4D1;  /* 未激活文字 */

  /* 背景颜色 */
  --color-fill-gray-1: #F0F2F7;  /* 侧边背景色-灰 */
  --color-fill-gray-2: #F4F7FE;  /* 二级页面背景色-灰 */
  --color-fill-white: #FFFFFF;   /* 背景色-白 */
  --color-fill-light: #F5FAFF;  /* 背景色-浅蓝 */
  --color-linear-light: linear-gradient(212deg, #F5FAFF 13.1%, #FFF 80.77%);  /* 渐变背景 */

  /* 线条颜色 */
  --color-line-1: #E9ECF5;
    --color-line-2: #DFE3F0;
    --color-line-3: #CFD5E8;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: AlibabaPuHuiTi-3-55-Regular, AlibabaPuHuiTi-3-55-RegularL3;
  }
}

@layer components {
  .typography {
    p {
      font-size: 1rem;
      color: var(--color-gray-2);
      line-height: 1.75;
      margin: 1rem 0;
    }

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--color-gray-1);
      margin-bottom: 1rem;
    }

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-gray-1);
      margin-top: 1.5rem;
      margin-bottom: 0.75rem;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--color-gray-2);
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }

    ul, ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
      line-height: 1.75;
      color: var(--color-gray-3);
    }

    li {
      margin: 0.25rem 0;
    }

    a {
      color: var(--color-primary-1);
      text-decoration: underline;
      
      &:hover {
        color: var(--color-primary-2);
      }
    }
  }

  .gil-date-picker-popup {
    @apply text-gray-2! fixed top-[50%]! left-[50%]! translate-x-[-50%]! translate-y-[-50%]!;
  }
  .gil-date-picker-popup .ant-picker-panel-container {
      @apply rounded-3xl! shadow-[0_13px_61px_rgba(169,169,169,0.37)]!;
  }
  .gil-date-picker-popup .ant-picker-header {
      @apply p-5!;
  }
  .gil-date-picker-popup .ant-picker-header * {
      @apply leading-normal! text-neutral-800!;
      @apply text-base!;
  }
  .gil-date-picker-popup .ant-picker-body {
      @apply p-4! pr-3!;
  }
  .gil-date-picker-popup .ant-picker-cell {
      @apply text-slate-400!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view {
      @apply text-gray-2!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-disabled {
      @apply text-slate-400!;
  }
  .gil-date-picker-popup .ant-picker-cell-inner {
      @apply rounded-full! size-7! leading-7! min-w-7! font-medium! text-sm!;
  }

  .gil-date-picker-popup .ant-picker-content th {
      @apply text-gray-4! text-sm! font-medium!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-column {
      @apply w-16.5!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-cell-inner {
      @apply px-5! py-1.5! text-gray-2! text-sm! font-medium! w-14.5! h-auto! rounded-lg!;
  }
  .gil-date-picker-popup .ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
      @apply text-gray-5!;
  }
  .gil-date-picker-popup .ant-picker-ranges {
      @apply px-8! py-5!;
  }
  .gil-date-picker-popup .ant-picker-ranges >li {
      @apply leading-none!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok {
      @apply p-0!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn {
      @apply text-white! text-base! font-medium! leading-normal! py-2! px-6! h-auto! rounded-full! bg-indigo-500!;
  }
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn:disabled,
  .gil-date-picker-popup .ant-picker-ranges .ant-picker-ok .ant-btn.ant-btn-disabled {
      @apply bg-indigo-200! border-indigo-200!;
  }
  .gil-date-picker-popup .ant-picker-footer-extra {
      @apply p-0!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner {
      @apply text-primary-1!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner {
      @apply text-white!;
  }
  .gil-date-picker-popup .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
      @apply border-none! top-auto! -bottom-2! left-[50%]! translate-x-[-50%]! size-1! bg-primary-2!;
  }
  .gil-date-picker-popup .ant-picker-header-view button {
    @apply font-medium!;
  }

  .gil-date-picker-popup .ant-picker-dropdown .ant-picker-time-panel-column {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent !important;
  }
}

/* 延迟显示动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}