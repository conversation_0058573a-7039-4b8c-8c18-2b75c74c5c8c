"use client";

import { r } from "@/libs/axios";
import { Button } from "@/ui/button";
import { useSignal } from "@preact-signals/safe-react";
import DocPreview from "./doc-preview";
import { WebTokenResponse } from "./doc-preview/type";

const resourceIdMap = {
  "1": "docx",
  "2": "pptx",
  "3": "xlsx",
  "4": "pdf",
};

// TODO: remove this
export default function Page() {
  const generateToken = () =>
    r.get<WebTokenResponse>("https://samesource.fun/hello", {
      params: {
        resourceId: resourceId.value + "",
      },
    });

  const resourceId = useSignal(1);

  return (
    <div className="flex h-full w-full flex-col">
      <div>
        {new Array(4).fill(null).map((e, i) => {
          return (
            <Button key={i} onClick={() => (resourceId.value = i + 1)}>
              {resourceIdMap[`${i + 1}` as keyof typeof resourceIdMap] || ""}
            </Button>
          );
        })}
      </div>
      <div className="flex-1">
        <DocPreview
          resourceId={resourceId.value + ""}
          generateTokenMethod={generateToken}
          storeTokenByResourceId
          enableMobileAdapt
          enableJumpToPPTFirstSlide
        />
      </div>
    </div>
  );
}
