import type { ScriptProps } from "next/script";
import { ScriptLoadedCB, ScriptLoadErrorCB } from "./hooks/useScriptLoaded";


export interface DocPreviewProps {
    resourceId: string;

    generateTokenMethod: GenerateTokenMethod;

    refreshTokenMethod?: RefreshTokenMethod;
    storeTokenByResourceId?: boolean;
    refreshTokenGapMs?: number; // Token 过期前 xx毫秒 刷新时间，单位：毫秒，默认一分钟

    onScriptLoaded?: ScriptLoadedCB;
    onScriptLoadError?: ScriptLoadErrorCB;

    scriptLoadStrategy?: ScriptProps['strategy'];

    onTokenGenerated?: (tokenInfo: WebTokenResponse) => void;
    onTokenRefreshed?: (refreshTokenInfo: RefreshTokenResponse) => void;
    // 文件加载成功时的回调
    onPreview?: (instance: IMMInstance) => void;

    className?: string;
    style?: React.CSSProperties;
    loadingRender?: React.ComponentType;
    failedRender?: React.ComponentType;
    enableMobileAdapt?: boolean;
    enableJumpToPPTFirstSlide?: boolean;
    officeInstanceRef?: React.RefObject<IMMInstance | null>;
}


export type WebTokenRequest = {
    resourceId: string;
}

export type WebTokenResponse = {
    requestId?: string,
    webofficeURL: string,
    accessToken: string,
    refreshToken: string,
    accessTokenExpiredTime: string,
    refreshTokenExpiredTime: string
}

export type RefreshTokenRequest = {
    accessToken: string;
    refreshToken: string;
}
export type RefreshTokenResponse = Omit<WebTokenResponse, "previewUrl">

export type GenerateTokenMethod = (params: WebTokenRequest) => Promise<WebTokenResponse>

export type RefreshTokenMethod = (params: RefreshTokenRequest) => Promise<RefreshTokenResponse>

export type IMMInstance = {
    setToken: (params: {
        token: string,
        timeout?: number,
    }) => void;
    ready: () => Promise<void>;
    Application: {
        ActiveDocument: {
            SwitchTypoMode: (mode: boolean) => Promise<boolean>;
        };
        ActivePresentation: {
            SlideShowSettings: {
                SetMiniThumbnailVisible: (visible: boolean) => Promise<boolean>;
            }
            SlideShowWindow: {
                View: {
                    GotoSlide: (slideIndex: number) => Promise<void>;
                }
            }
        }
        ActivePDF: {
            PageMode: number;
        }
    };
    iframe: HTMLIFrameElement;
    destroy: () => void
    // 可参考文档1：https://help.aliyun.com/zh/imm/user-guide/events-1
    // 可参考文档2：https://help.aliyun.com/zh/imm/user-guide/event-handling
    /** @deprecated */
    on: (event: string, callback: (...args: unknown[]) => void) => void;
    /** @deprecated */
    off: (event: string, callback: (...args: unknown[]) => void) => void;
    ApiEvent: {
        AddApiEventListener: (event: string, callback: (...args: unknown[]) => void) => void;
        RemoveApiEventListener: (event: string, callback: (...args: unknown[]) => void) => void;
    }
}

declare global {
    interface Window {
        aliyun?: {
            config: (config: {
                url: string,
                mount?: HTMLElement,
                // 普通模式，展示所有功能界面；极简模式，不显示头部和工具栏。
                mode?: "normal" | "simple"

                refreshToken?: () => Promise<{ token: string, timeout: number }>;

                // TODO: 待补充更多的参数：https://help.aliyun.com/zh/imm/user-guide/parameters
            }) => IMMInstance;
        };
    }
}