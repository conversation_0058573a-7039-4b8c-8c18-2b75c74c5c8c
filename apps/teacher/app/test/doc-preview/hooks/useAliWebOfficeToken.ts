import { useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useCallback, useEffect } from "react";
import { DEFAULT_REFRESH_TOKEN_GAP } from "../const";
import { GenerateTokenMethod, RefreshTokenMethod, WebTokenResponse } from "../type";


type CachedTokenInfo = Record<string, WebTokenResponse>;
const cachedTokenInfoKey = "_web-office-token";

const filterValidTokenInfo = (tokenInfo: CachedTokenInfo) => {
    Object.keys(tokenInfo).forEach(key => {
        if (!tokenInfo[key].accessToken || !tokenInfo[key].refreshToken || (Date.parse(tokenInfo[key].refreshTokenExpiredTime) < Date.now())) {
            delete tokenInfo[key];
        }
    })
    return tokenInfo;
}

export default function useAliWebOfficeToken(
    resourceId: string,
    generateTokenMethod: GenerateTokenMethod,
    refreshTokenMethod?: RefreshTokenMethod,
    storeTokenByResourceId?: boolean,
    refreshTokenGapMs?: number,
) {
    const cachedTokenInfo = useSignal<CachedTokenInfo>({});

    useEffect(() => {
        if (storeTokenByResourceId === true) {
            const storageInfo = localStorage.getItem(cachedTokenInfoKey);
            if (storageInfo === null || storageInfo === "") { return }
            const info = JSON.parse(storageInfo);
            cachedTokenInfo.value = info;
        }
    }, [cachedTokenInfo, storeTokenByResourceId]);

    const updateCachedTokenInfoByResourceId = useCallback((resourceId: string, tokenInfo: WebTokenResponse) => {
        cachedTokenInfo.value[resourceId] = tokenInfo;
        cachedTokenInfo.value = filterValidTokenInfo(cachedTokenInfo.value);

        if (storeTokenByResourceId === true) {
            localStorage.setItem(cachedTokenInfoKey, JSON.stringify(cachedTokenInfo.value));
        }
    }, [cachedTokenInfo, storeTokenByResourceId]);

    const refreshTokenRequest = useRequest(async () => {
        if (refreshTokenMethod && cachedTokenInfo.value[resourceId]) {
            return await refreshTokenMethod({
                accessToken: cachedTokenInfo.value[resourceId].accessToken,
                refreshToken: cachedTokenInfo.value[resourceId].refreshToken,
            });
        }
    }, {
        manual: true,
    });

    const refreshToken = useCallback(() => {
        if (refreshTokenMethod) {
            return Promise.reject(new Error("refreshTokenMethod is not set"));
        }

        return refreshTokenRequest.runAsync().then(refreshTokenInfo => {
            if (!refreshTokenInfo) {
                throw new Error("refreshToken failed");
            }
            updateCachedTokenInfoByResourceId(resourceId, {
                ...cachedTokenInfo.value[resourceId],
                ...refreshTokenInfo,
            });
            return {
                token: refreshTokenInfo.accessToken,
                timeout: Date.parse(refreshTokenInfo.accessTokenExpiredTime) - Date.now() - (refreshTokenGapMs ?? DEFAULT_REFRESH_TOKEN_GAP),
            };
        })
    }, [refreshTokenGapMs, cachedTokenInfo, resourceId, refreshTokenRequest, updateCachedTokenInfoByResourceId, refreshTokenMethod]);

    const { data: tokenInfo, loading: isFetchingTokenInfo, error } = useRequest(async () => {
        const existTokenInfo = cachedTokenInfo.value[resourceId];
        // 1. 如果有没有过期的缓存，则直接返回
        if (existTokenInfo) {
            const refreshTokenGap = refreshTokenGapMs ?? DEFAULT_REFRESH_TOKEN_GAP;
            // 1.1 如果 AccessToken 没有过期，则直接返回
            if (Date.parse(existTokenInfo.accessTokenExpiredTime) > Date.now() + refreshTokenGap) {
                return existTokenInfo;
            }

            // 1.2 如果 AccessToken 过期，refreshToken没过期，则刷新 RefreshToken，并更新缓存
            if (refreshTokenMethod && +new Date(existTokenInfo.refreshTokenExpiredTime) > Date.now() + refreshTokenGap) {
                try {
                    const refreshTokenInfo = await refreshTokenMethod({
                        accessToken: existTokenInfo.accessToken,
                        refreshToken: existTokenInfo.refreshToken,
                    });
                    const refreshedTokenInfo = {
                        ...existTokenInfo,
                        ...refreshTokenInfo,
                    };
                    updateCachedTokenInfoByResourceId(resourceId, refreshedTokenInfo);
                    return refreshedTokenInfo;
                } catch (err) {
                    console.error(err)
                    delete cachedTokenInfo.value[resourceId]
                }
            }
        }

        // 2. 如果没有可用缓存，则生成新的 Token
        const tokenInfo = await generateTokenMethod({ resourceId });

        if (storeTokenByResourceId === true) {
            updateCachedTokenInfoByResourceId(resourceId, tokenInfo);
        }

        return tokenInfo;
    }, {
        refreshDeps: [resourceId],
    });

    return {
        tokenInfo,
        isFetchingTokenInfo,
        error,
        refreshToken
    }
}
