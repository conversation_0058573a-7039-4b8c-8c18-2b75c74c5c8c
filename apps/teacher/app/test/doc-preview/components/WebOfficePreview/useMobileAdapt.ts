import { useCallback } from "react";
import { IMMInstance } from "../../type";

// fork from ali <PERSON>K 
const isMobile = /Android|webOS|iPhone|iPod|BlackBerry/i.test(
  navigator.userAgent
);


export default function useMobileAdapt(enableMobileAdapt: boolean) {
  const adaptMobile = useCallback(async (instance: IMMInstance) => {
    if (!enableMobileAdapt) { return; }

    await instance.ready();
    const app = instance.Application;

    if (app && isMobile) {
      //切换为分页模式
      app.ActiveDocument?.SwitchTypoMode?.(false);

      // 切出PPT的缩略图
      app.ActivePresentation?.SlideShowSettings?.SetMiniThumbnailVisible(
        true
      );
    }
  }, [enableMobileAdapt]);


  return adaptMobile;
}
