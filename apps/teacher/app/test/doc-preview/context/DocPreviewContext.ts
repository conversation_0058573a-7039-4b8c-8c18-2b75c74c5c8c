import { createContext, useContext } from "react";
import useAliWebOfficeToken from "../hooks/useAliWebOfficeToken";
import useScriptLoaded from "../hooks/useScriptLoaded";
import { DocPreviewProps } from "../type";


export type UseDocPreviewModelProps = Omit<DocPreviewProps, "loadingRender" | "failedRender">;

// NOTE：看loading那里要不要变逻辑了，要不然可以再组合下这里的逻辑
export function useDocPreviewModel({
    resourceId,
    onScriptLoaded,
    onScriptLoadError,
    generateTokenMethod,
    refreshTokenMethod,
    storeTokenByResourceId,
    refreshTokenGapMs,
}: UseDocPreviewModelProps) {
    const { status: scriptLoadStatus, handleScriptLoad, handleScriptLoadError } = useScriptLoaded(
        onScriptLoaded,
        onScriptLoadError,
    );

    const { tokenInfo, isFetchingTokenInfo, error, refreshToken } = useAliWebOfficeToken(
        resourceId,
        generateTokenMethod,
        refreshTokenMethod,
        storeTokenByResourceId,
        refreshTokenGapMs,
    );

    return {
        scriptLoadStatus,
        handleScriptLoad,
        handleScriptLoadError,

        tokenInfo, isFetchingTokenInfo, error, refreshToken: refreshTokenMethod ? refreshToken : undefined,
    };
}


export const DocPreviewContext = createContext<ReturnType<typeof useDocPreviewModel> | null>(null)

export const useDocPreviewContext = () => {
    const context = useContext(DocPreviewContext)
    if (!context) {
        throw new Error("useDocPreviewContext must be used within a DocPreviewProvider")
    }
    return context
}
