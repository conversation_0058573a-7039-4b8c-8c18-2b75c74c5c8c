"use client";

import { Skeleton } from "@repo/ui/components/skeleton";
import Script from "next/script";
import { memo, useMemo } from "react";
import WebOfficePreviewLayout from "./WebOfficePreviewLayout";
import { ALI_IMM_SDK_URL } from "./const";
import {
  DocPreviewContext,
  useDocPreviewContext,
  useDocPreviewModel,
} from "./context/DocPreviewContext";
import { PreviewConfigContext } from "./context/PreviewConfigContext";
import { DocPreviewProps } from "./type";

const DefaultLoadingRender = memo(function DefaultLoadingRender() {
  return <Skeleton className="h-full w-full" />;
});

const DefaultFailedRender = memo(function DefaultFailedRender() {
  return (
    <div className="text-center text-sm text-red-500">
      预览SDK加载失败，请稍后重试
    </div>
  );
});

function DocPreviewContent({
  loadingRender,
  failedRender,
}: Pick<DocPreviewProps, "loadingRender" | "failedRender">) {
  const { scriptLoadStatus, handleScriptLoad, handleScriptLoadError } =
    useDocPreviewContext();

  const Loading = useMemo(() => {
    return loadingRender || DefaultLoadingRender;
  }, [loadingRender]);

  const Failed = useMemo(() => {
    return failedRender || DefaultFailedRender;
  }, [failedRender]);

  return (
    <>
      <Script
        src={ALI_IMM_SDK_URL}
        onLoad={handleScriptLoad}
        onError={handleScriptLoadError}
      />
      {scriptLoadStatus.value === "loaded" && <WebOfficePreviewLayout />}
      {scriptLoadStatus.value === "loading" && <Loading />}
      {scriptLoadStatus.value === "failed" && <Failed />}
    </>
  );
}

/**
 * 资源中心文件预览器
 *
 * NOTE：文件需要上传到资源中心才可以预览
 *
 * <AUTHOR>
 * @see {@link https://help.aliyun.com/zh/imm/user-guide/getting-started-3 | 可预览文件类型列表}
 */
export default function DocPreview({
  resourceId,
  generateTokenMethod,

  onScriptLoaded,
  onScriptLoadError,
  onPreview,
  loadingRender,
  failedRender,
  refreshTokenMethod,
  storeTokenByResourceId,
  refreshTokenGapMs,

  className,
  style,
  enableMobileAdapt,
  enableJumpToPPTFirstSlide,
  officeInstanceRef,
}: DocPreviewProps) {
  const state = useDocPreviewModel({
    resourceId,

    onScriptLoaded,
    onScriptLoadError,

    generateTokenMethod,
    refreshTokenMethod,
    storeTokenByResourceId,
    refreshTokenGapMs,
  });

  const previewConfig = useMemo(() => {
    return {
      className,
      style,
      enableMobileAdapt,
      enableJumpToPPTFirstSlide,
      officeInstanceRef,
      onPreview,
    };
  }, [
    className,
    style,
    enableMobileAdapt,
    enableJumpToPPTFirstSlide,
    officeInstanceRef,
    onPreview,
  ]);

  return (
    <DocPreviewContext.Provider value={state}>
      <PreviewConfigContext.Provider value={previewConfig}>
        <DocPreviewContent
          loadingRender={loadingRender}
          failedRender={failedRender}
        />
      </PreviewConfigContext.Provider>
    </DocPreviewContext.Provider>
  );
}
