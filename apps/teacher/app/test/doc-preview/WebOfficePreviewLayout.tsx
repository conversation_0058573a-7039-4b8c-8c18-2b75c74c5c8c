"use client";
import { useContext } from "react";
import WebOfficePreview from "./components/WebOfficePreview";
import { useDocPreviewContext } from "./context/DocPreviewContext";
import { PreviewConfigContext } from "./context/PreviewConfigContext";

export default function WebOfficePreviewLayout() {
  const { tokenInfo, scriptLoadStatus, refreshToken } = useDocPreviewContext();

  const previewConfig = useContext(PreviewConfigContext);
  return (
    <WebOfficePreview
      tokenInfo={tokenInfo}
      scriptLoaded={scriptLoadStatus.value === "loaded"}
      refreshTokenRequest={refreshToken}
      {...previewConfig}
    />
  );
}
