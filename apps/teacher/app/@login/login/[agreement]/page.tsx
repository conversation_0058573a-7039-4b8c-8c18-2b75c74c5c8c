import { ScrollArea } from "@/ui/scroll-area";
import { marked } from "marked";
import { headers } from "next/headers";
import { notFound } from "next/navigation";
import sanitizeHtml from "sanitize-html";
import BackButton from "./back-button";

export default async function AgreementPage({ params }: { params: { agreement: string } }) {
  const { agreement } = await params;
  const allowedAgreements = ["user-agreement", "privacy-policy"];

  if (!allowedAgreements.includes(agreement)) {
    notFound();
  }

  const host = (await headers()).get("host");

  const response = await fetch(
    `${process.env.NODE_ENV === "development" ? "http" : "https"}://${host}/api/login/agreement?name=${agreement}`,
    { cache: "no-store" } // 保证每次都服务端拉取
  );
  const markdown = response.ok ? await response.text() : "协议加载失败";

  return (
    <div className="flex h-full flex-col bg-white">
      <div className="px-8 pb-4 pt-8">
        <BackButton />
      </div>
      <ScrollArea className="flex-1 overflow-hidden">
        <div className="px-8 pb-4">
          <article
            className="typography"
            dangerouslySetInnerHTML={{
              __html: sanitizeHtml(marked(markdown) as string),
            }}
          />
        </div>
      </ScrollArea>
    </div>
  );
}
