"use client";
import { LOGIN_CODE_LENGTH, TEACHER_USER_TYPE_ID } from "@/configs";
import { useAuth } from "@/hooks";
import { useUmeng } from "@/hooks/useUmeng";
import CheckboxCheckedIcon from "@/public/icons/icon_checkbox_checked.svg";
import Ic<PERSON>ogoLogin from "@/public/icons/logo_login.svg";
import loginBg from "@/public/images/login-bg.webp";
import { login as loginApi } from "@/services";
import { Organization } from "@/types";
import { Button } from "@/ui/button";
import { toast, ToastContainer, type ToastProps } from "@/ui/toast";
import {
  cn,
  UmengCategory,
  UmengLoginAction,
  UmengLoginPageName,
} from "@/utils";
import {
  useEffect,
  useMemo,
  useRef,
  useState,
} from "@preact-signals/safe-react/react";
import { useMount, useRequest } from "ahooks";
import to from "await-to-js";
import { AxiosResponse } from "axios";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import Script from "next/script";
import store from "store2";
import { match, P } from "ts-pattern";
import Captcha from "./captcha";
import { SchoolSwitchModal } from "./school-switch-modal";

export default function Page() {
  const umeng = useUmeng(UmengCategory.LOGIN, UmengLoginPageName.LOGIN_PAGE);
  const { login } = useAuth();

  const router = useRouter();
  const searchParams = useSearchParams(); // 获取 schoolId

  const token = useRef("");
  const [phoneNumber, setPhoneNumber] = useState(""); // 18772964832 13269015927

  const isPhoneNumberValid = useMemo(() => {
    const phoneRegex = /^[1][3-9][0-9]{9}$/;
    return phoneRegex.test(phoneNumber);
  }, [phoneNumber]);

  useEffect(() => {
    if (!isPhoneNumberValid) {
      return;
    }

    umeng.trackEvent(
      UmengCategory.LOGIN,
      UmengLoginAction.LOGIN_PAGE_ADD_DONE,
      {
        phone_number: phoneNumber,
      }
    );
  }, [isPhoneNumberValid, phoneNumber, umeng]);

  const [code, setCode] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [isSchoolDialogOpen, setIsSchoolDialogOpen] = useState(false);
  const [schools, setSchools] = useState<Organization[]>([]);
  const [isAgreed, setIsAgreed] = useState(false);
  const [hasRequestedCode, setHasRequestedCode] = useState(false);
  const [toastProps, setToastProps] = useState<ToastProps>();
  const timer = useRef<NodeJS.Timeout | null>(null);

  useMount(() => {
    window.TecJsb?.firstPageReady?.();

    requestAnimationFrame(() => {
      router.prefetch("/course");
    });

    store.clearAll();

    return () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
    };
  });

  const handleSendCode = () => {
    setCountdown(60);
    setHasRequestedCode(true);

    timer.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer.current!);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const loginRequest = useRequest(loginApi, {
    manual: true,
    debounceWait: 500,
  });

  const handleVerifyTeacher = async () => {
    if (loginRequest.loading) {
      return;
    }

    if (!isPhoneNumberValid) {
      setToastProps({
        message: "请输入正确手机号",
        type: "warning",
      });
      return;
    }

    if (!code) {
      setToastProps({
        message: "请输入验证码",
        type: "warning",
      });
      return;
    }

    if (!isAgreed) {
      setToastProps({
        message: "请阅读并同意用户协议和隐私政策",
        type: "warning",
      });
      return;
    }

    const [err, ret] = await to(loginRequest.runAsync(phoneNumber, code));

    umeng.trackEvent(
      UmengCategory.LOGIN,
      UmengLoginAction.LOGIN_PAGE_SMS_VERIFY_DONE,
      {
        sms_status: err ? "失败" : "成功",
      }
    );

    if (err) {
      setToastProps({
        message:
          (err as unknown as AxiosResponse)?.data?.message ??
          err.message ??
          "登录失败",
        type: "warning",
      });

      umeng.trackEvent(
        UmengCategory.LOGIN,
        UmengLoginAction.LOGIN_PAGE_SUBMIT,
        {
          login_status: "失败",
        }
      );
      return;
    }

    token.current = ret.token;

    const schools = ret.user.userTypes.find(
      (userType) => userType.userTypeId === TEACHER_USER_TYPE_ID
    )?.organizationList;

    if (!schools) {
      setToastProps({
        message: "没有查到学校信息，请稍后重试",
        type: "error",
      });
      return;
    }

    umeng.trackEvent(UmengCategory.LOGIN, UmengLoginAction.LOGIN_PAGE_SUBMIT, {
      login_status: "成功",
    });

    const schoolId = searchParams?.get("schoolId");

    if (schoolId) {
      const school = schools.find(
        (school) => school.organizationId === Number(schoolId)
      );

      if (school) {
        handleSelectSchool(school);

        return;
      }
    }

    if (schools.length === 1) {
      handleSelectSchool(schools[0]);

      return;
    }

    setSchools(schools);
    setIsSchoolDialogOpen(true);
  };

  const handleSelectSchool = async (school: Organization) => {
    umeng.trackEvent(
      UmengCategory.LOGIN,
      UmengLoginAction.LOGIN_PAGE_SCHOOL_LIST_DONE,
      {
        school_selected: school.organizationName,
      }
    );

    login({ token: token.current, schoolId: school.organizationId });

    toast.success("登录成功～");
  };

  return (
    <div className="flex h-full bg-white">
      <Script
        id="aliyun-captcha-main"
        src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"
        strategy="afterInteractive"
      />

      <div className="flex-1">
        <Image
          className="h-full w-full object-cover object-left-top"
          src={loginBg}
          alt="银河教师端"
          priority
        />
      </div>

      {/* 登录 form */}
      <div className="min-w-100 flex w-2/5 flex-none flex-col items-center justify-center bg-[linear-gradient(214deg,_#F5FAFF_17.17%,_#FFF_79.83%)] px-10 text-sm">
        <IcLogoLogin className="h-12" />

        <div className="mt-10 w-full">
          <div className="space-y-2">
            <label className="text-gray-2 block h-5 font-semibold">
              手机号
            </label>

            <div className="border-line-3 focus-within:border-primary-2 flex h-11 rounded-3xl border focus-within:shadow-[0_4px_8px_#1012190a]">
              <input
                type="tel"
                value={phoneNumber}
                onChange={(e) => {
                  if (/^[0-9]{0,11}$/.test(e.target.value)) {
                    setPhoneNumber(e.target.value);
                  }
                }}
                className="text-gray-1 placeholder:text-line-3 h-full flex-1 px-3 outline-none"
                placeholder="请输入手机号"
              />
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <label className="text-gray-2 block h-5 text-sm font-semibold">
              验证码
            </label>
            <div className="border-line-3 focus-within:border-primary-2 flex h-11 items-center rounded-3xl border focus-within:shadow-[0_4px_8px_#1012190a]">
              <div className="h-full flex-1 overflow-hidden">
                <input
                  type="text"
                  value={code}
                  onChange={(e) => {
                    if (/^[a-zA-Z0-9]{0,6}$/.test(e.target.value)) {
                      setCode(e.target.value);
                    }
                  }}
                  className="text-gray-2 placeholder:text-line-3 h-full w-full rounded-l-full px-3 outline-none"
                  placeholder="请输入验证码"
                />
              </div>

              <div className="h-4.5 border-line-3 flex flex-none items-center justify-center border-l px-3">
                <Captcha
                  canCaptcha={isPhoneNumberValid && countdown === 0}
                  phoneNumber={phoneNumber}
                  onBizResultCallback={() => {
                    handleSendCode();
                  }}
                >
                  {match({ countdown, isPhoneNumberValid })
                    .with({ countdown: 0, isPhoneNumberValid: true }, () => {
                      return (
                        <span className="text-primary-1 cursor-pointer whitespace-nowrap text-sm active:opacity-80">
                          {hasRequestedCode ? "重新获取" : "获取验证码"}
                        </span>
                      );
                    })
                    .with({ countdown: 0, isPhoneNumberValid: false }, () => {
                      return (
                        <span
                          className="text-primary-1 cursor-pointer whitespace-nowrap text-sm active:opacity-80"
                          onClick={() => {
                            setToastProps({
                              message: "请输入正确手机号",
                              type: "warning",
                            });
                          }}
                        >
                          {hasRequestedCode ? "重新获取" : "获取验证码"}
                        </span>
                      );
                    })
                    .with(
                      { countdown: P.when((countdown) => countdown > 0) },
                      ({ countdown }) => {
                        return (
                          <span className="text-gray-4 whitespace-nowrap text-sm">
                            {countdown}秒后重试
                          </span>
                        );
                      }
                    )
                    .otherwise(() => null)}
                </Captcha>
              </div>
            </div>
          </div>

          <div className="mt-4 flex cursor-default items-center gap-1">
            <div className="relative flex active:opacity-80">
              <input
                type="checkbox"
                checked={isAgreed}
                onChange={(e) => setIsAgreed(e.target.checked)}
                className="border-1 border-line-3 checked:bg-primary-2 rounded-xs h-3.5 w-3.5 cursor-pointer appearance-none checked:border-0 active:outline-0"
              />

              <CheckboxCheckedIcon
                onClick={() => setIsAgreed(false)}
                className={cn(
                  `absolute h-full w-full cursor-pointer`,
                  isAgreed ? "" : "-z-1"
                )}
              />
            </div>
            <span className="text-xs text-[#202224]">
              我已知晓并同意
              <span
                className="cursor-pointer text-[#2E42FB] active:opacity-80"
                onClick={() => router.push("/login/user-agreement")}
              >
                《用户协议》
              </span>
              和
              <span
                className="cursor-pointer text-[#2E42FB] active:opacity-80"
                onClick={() => router.push("/login/privacy-policy")}
              >
                《隐私政策》
              </span>
            </span>
          </div>

          <Button
            onClick={handleVerifyTeacher}
            disabled={
              !isAgreed || !phoneNumber || code.length !== LOGIN_CODE_LENGTH
            }
            className="w-50 [disabled]:cursor-not-allowed bg-primary-2 hover:bg-primary-2 mx-auto mt-8 flex h-12 cursor-pointer items-center justify-center rounded-full text-xl font-semibold text-white active:opacity-80"
          >
            登 录
          </Button>

          <div className="mt-2.5 flex h-0 justify-center">
            <div>
              {toastProps && (
                <ToastContainer
                  className="translate-[unset] static"
                  {...toastProps}
                  onClose={() => {
                    setToastProps(undefined);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      <SchoolSwitchModal
        open={isSchoolDialogOpen}
        setOpen={setIsSchoolDialogOpen}
        onOpenChange={setIsSchoolDialogOpen}
        schools={schools}
        onConfirm={handleSelectSchool}
        title="选择学校"
        description="您的账号关联了多个学校的职务，请选择其中一个学校进行登录"
      />
    </div>
  );
}
