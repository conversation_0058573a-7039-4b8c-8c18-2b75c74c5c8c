"use client";
import { AssignCategoryCard } from "@/components/assign/assign-category-card";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignSubjectSelect } from "@/components/assign/assign-subject-select";
import { PageHeader } from "@/components/PageHeader";
import { useApp } from "@/hooks";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { useUmeng } from "@/hooks/useUmeng";
import ArrowRight from "@/public/icons/ic_fold.svg";
import { getTaskLatestAssign } from "@/services/assign";
import { Homework } from "@/types/homeWork";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import { Button } from "@/ui/tch-button";
import {
  getAssignCategories,
  sortLatestAssignTasks,
  umeng,
  UmengAssignAction,
  UmengAssignPageName,
  UmengCategory,
} from "@/utils";
import { cn } from "@/utils/utils";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useMount, useRequest } from "ahooks";
import { useContext } from "react";
import AssignSkeleton from "./components/AssignSkeleton";
import AssignTaskCard from "./components/AssignTaskCard";
import { AssignContext } from "./store";

export default function AssignPage() {
  useUmeng(UmengCategory.ASSIGN, UmengAssignPageName.TASK_LIST);
  const { getRoleSummary, setOpen, userInfo } = useApp();
  const { gotoTaskCreatePage, gotoHomeworkPage } = useTchNavigation();
  const { currentSubject, setCurrentSubject, subjectTaskTypes } =
    useContext(AssignContext);
  const playGuide = useSignal<boolean>(false);
  const assignCategories = useComputed(() => {
    return getAssignCategories(currentSubject.value.taskTypes);
  });

  const currentSubjectKey = useComputed(() => {
    return currentSubject.value?.subjectKey || 0;
  });

  // 太抽象了
  const onCategoryClick = (id: string, umengTag: string) => {
    if (currentSubjectKey.value) {
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_ADD_TASK_CLICK,
        {
          type: umengTag,
          subject: currentSubject.value.subjectName,
          job: getRoleSummary(),
        }
      );

      gotoTaskCreatePage(currentSubjectKey.value, id);
    }
  };

  const viewAllTasks = () => {
    umeng.trackEvent(
      UmengCategory.ASSIGN,
      UmengAssignAction.TASK_LIST_MORE_CLICK,
      {}
    );
    gotoHomeworkPage({
      source: "assign",
      subjectId: currentSubjectKey.value,
    });
  };

  const { data: latestAssignTasks, loading: isLatestAssignTasksLoading } =
    useRequest(
      async () => {
        if (!currentSubjectKey.value) {
          return [];
        }
        const res = await getTaskLatestAssign(Number(currentSubjectKey.value));
        const tasks: Homework[] = res?.tasks ?? [];
        const sortedTasks = sortLatestAssignTasks(tasks);

        return sortedTasks;
      },
      {
        refreshDeps: [currentSubjectKey.value],
      }
    );

  useSignalEffect(() => {
    if (playGuide.value) {
      umeng.trackEvent(
        UmengCategory.ASSIGN,
        UmengAssignAction.TASK_LIST_NEW_GUIDE_CLICK,
        {}
      );
    }
  });

  useMount(() => {
    setOpen(true);
  });

  if (!userInfo) {
    return <AssignSkeleton />;
  }

  return (
    <div className="flex h-full flex-col overflow-hidden bg-[#F5FAFF] px-6">
      <PageHeader className="pl-0">
        {/* 当前学科的选择器 */}
        <AssignSubjectSelect
          onChange={setCurrentSubject}
          value={currentSubject}
          taskTypes={subjectTaskTypes}
        />
      </PageHeader>

      <div
        className={cn("flex-0 mb-8 grid gap-4")}
        style={{
          gridTemplateColumns: `repeat(${assignCategories.value.length}, minmax(0, 1fr))`,
        }}
      >
        {assignCategories.value.map((category, index) => (
          <AssignCategoryCard
            key={index}
            {...category}
            className={cn("col-span-1")}
            onClick={onCategoryClick}
          />
        ))}
      </div>

      <div className="flex-0 mb-5 flex items-center justify-between">
        <h2 className="text-gray-1 text-xl font-medium leading-normal tracking-wider">
          最近布置
        </h2>
        {!isLatestAssignTasksLoading && latestAssignTasks?.length !== 0 ? (
          <Button
            radius="full"
            size="lg"
            className="text-gray-4 h-7 w-16 bg-transparent text-xs font-medium"
            onClick={viewAllTasks}
          >
            <span style={{ lineHeight: "normal" }}>全部</span>
            <ArrowRight className="ml-1 h-3 w-2" />
          </Button>
        ) : null}
      </div>
      {isLatestAssignTasksLoading ? (
        <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
          <div className="grid grid-cols-4 gap-4">
            {/* 第一行 - 大卡片 */}
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />
            <Skeleton className="col-span-1 h-6" />

            {/* 第二行 - 小标签 */}
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />
            <Skeleton className="w-19.5 col-span-1 h-4" />

            {/* 内容卡片 */}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row3-${index}`} className="h-25.75 col-span-1" />
            ))}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
            ))}
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={`row4-${index}`} className="h-25.75 col-span-1" />
            ))}
          </div>
        </ScrollArea>
      ) : latestAssignTasks?.length !== 0 ? (
        <ScrollArea orientation="vertical" className="flex-1 overflow-hidden">
          <div
            className="grid grid-cols-2 gap-4"
            style={{
              gridTemplateColumns: `repeat(${4}, minmax(0, 1fr))`,
            }}
          >
            {latestAssignTasks?.map((task) => (
              <AssignTaskCard key={task.taskId} taskData={task} />
            ))}
          </div>
        </ScrollArea>
      ) : (
        <AssignEmpty
          type="homepage"
          className="flex-1 border-none bg-gradient-to-b from-slate-50 to-white"
          content={
            <div className="space-y-1 text-center leading-normal">
              <div className="text-gray-2 text-center text-base">
                当前暂无布置内容
              </div>
              <div className="text-gray-4 text-center text-xs">
                快去布置一项任务吧
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
