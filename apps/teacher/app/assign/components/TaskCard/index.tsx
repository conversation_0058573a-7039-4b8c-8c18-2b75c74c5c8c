"use client";
import { taskTypeEnumManager } from "@/enums";
import { Homework, HomeworkReport, StatData } from "@/types/homeWork";
import { Skeleton } from "@/ui/skeleton";
import { cn } from "@/utils/utils";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
// 首页没这个功能，后边再重构吧
// import { MoreMenu } from "./MoreMenu";
import ReportCard from "./ReportCard";

// // 格式化日期
export function formatDate(statData?: StatData): string {
  if (!statData?.startTime) return "";

  const startTimestamp = statData.startTime * 1000;
  const endTimestamp = statData?.deadline ? statData.deadline * 1000 : null;

  const startStr = format(startTimestamp, "MM/dd HH:mm", { locale: zhCN });
  const endStr = endTimestamp
    ? ` 至 ${format(endTimestamp, "MM/dd HH:mm", { locale: zhCN })}`
    : "";

  return `${startStr}${endStr}`;
}

export interface TaskCardProps {
  taskData: Homework;
  className?: string;
  onlyShow?: boolean;
  onClick?: () => void;
  onClickReportCard?: (classData: HomeworkReport, taskData: Homework) => void;
  classNames?: string;
  reportCardProps?: Partial<React.ComponentProps<typeof ReportCard>>;
  timeFormatter?: (statData?: StatData) => string;
}

/**
 * 任务卡片组件
 * 显示任务详情和相关班级数据
 */
export function TaskCard({
  taskData,
  className,
  // onlyShow,
  onClick,
  onClickReportCard,
  timeFormatter = formatDate,
}: TaskCardProps) {
  // const { userInfo } = useApp();
  // const isOwnTask = userInfo?.userID === taskData.creatorId;

  const typeInfo = taskTypeEnumManager.getEnumByValue(taskData.taskType);

  return (
    // 任务卡片容器，添加点击状态样式
    <div
      className={cn(
        "flex flex-1 cursor-pointer flex-col items-start gap-4 rounded-2xl transition-all duration-200",
        "active:scale-[0.98] active:opacity-40",
        className
      )}
      onClick={onClick}
    >
      <div className={`mb-4 w-full`}>
        {/* 任务标题区域 */}
        <h2 className="text-gray-2 mb-0.25 text-base font-medium leading-[150%] tracking-wider">
          {taskData.taskName}
        </h2>
        {/* 任务类型和日期信息 */}
        <p className="text-gray-4 mb-4 whitespace-nowrap text-xs font-normal leading-[150%]">
          <span className="text-[#8388ab]">{typeInfo?.label}</span>{" "}
          {timeFormatter(taskData.reports[0]?.statData)}
        </p>

        {/* 班级数据列表 */}
        {taskData.reports.map((report) => (
          <ReportCard
            key={report.assignId}
            report={{
              ...report,
            }}
            taskInfo={{
              taskId: taskData.taskId,
              taskType: taskData.taskType,
            }}
            onClick={() => {
              onClickReportCard?.(report, taskData);
            }}
            // headerSuffix={
            //   isOwnTask && !onlyShow && <MoreMenu taskData={taskData} />
            // }
          />
        ))}
      </div>
    </div>
  );
}

export function TaskCardSkeleton() {
  return (
    <div className="h-[221px] rounded-2xl p-6">
      {/* 标题和时间 */}
      <Skeleton className="mb-1 h-6 w-2/3 bg-gray-100" />
      <Skeleton className="w-9/10 mb-4 h-4 bg-gray-100" />

      {/* 两个班级卡片 */}
      <Skeleton className="mb-4 h-28 w-full rounded-xl bg-gray-100" />
    </div>
  );
}
