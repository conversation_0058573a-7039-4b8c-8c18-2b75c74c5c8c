"use client";
import { useApp } from "@/hooks";
import { useCurrentSubjectLocalStorage } from "@/hooks/useCurrentSubjectLocalStorage";
import { UserSubjectItem } from "@/types/assign";
import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { createContext, useMemo } from "react";

export interface AssignContextType {
  currentSubject: Signal<UserSubjectItem>;
  subjectTaskTypes: Signal<UserSubjectItem[]>;
  setCurrentSubject: (subject: UserSubjectItem) => void;
}

export function useAssignState(): AssignContextType {
  const { userInfo } = useApp();
  const { setCurrentSubjectToLocalStorage, getCurrentSubjectFromLocalStorage } =
    useCurrentSubjectLocalStorage();

  const subjectTaskTypes = useComputed(() => {
    if (!userInfo?.teacherJobInfos) return [];
    const res = new Map<number, UserSubjectItem>();
    for (let i = 0; i < userInfo.teacherJobInfos.length; i++) {
      const item = userInfo.teacherJobInfos[i];
      if (
        item.schoolID !== userInfo.currentSchoolID ||
        !item.jobSubject ||
        item.jobSubject.jobSubject === 0
      ) {
        continue;
      }

      res.set(item.jobSubject.jobSubject, {
        subjectKey: item.jobSubject.jobSubject,
        subjectName: item.jobSubject.name,
        // NOTE: 这个目前是写死的，只开布置课程，但是是在数据计算方法上控制的
        taskTypes: [10, 20, 30, 40],
      });
    }

    return [...res.values()];
  });

  const defaultSubject = useMemo(() => {
    const cached = getCurrentSubjectFromLocalStorage();
    if (
      cached &&
      subjectTaskTypes.value.find(
        (item) => item.subjectKey === cached.subjectKey
      )
    ) {
      return {
        subjectKey: cached.subjectKey,
        subjectName:
          subjectTaskTypes.value.find(
            (item) => item.subjectKey === cached.subjectKey
          )?.subjectName || "",
        taskTypes: [10, 20, 30, 40],
      };
    }
    return subjectTaskTypes.value.length
      ? subjectTaskTypes.value[0]
      : {
          subjectKey: 0,
          subjectName: "",
          // NOTE: 这个目前是写死的，只开布置课程
          taskTypes: [10, 20, 30, 40],
        };
  }, [subjectTaskTypes.value, getCurrentSubjectFromLocalStorage]);

  // 布置页选择的科目
  const currentSubject = useSignal<UserSubjectItem>(defaultSubject);

  const setCurrentSubject = (subject: UserSubjectItem) => {
    currentSubject.value = subject;
    setCurrentSubjectToLocalStorage({
      subjectKey: subject.subjectKey,
    });
  };

  return {
    currentSubject,
    subjectTaskTypes,
    setCurrentSubject,
  };
}

export const AssignContext = createContext<AssignContextType>(
  {} as AssignContextType
);

export function AssignProvider({ children }: { children: React.ReactNode }) {
  const state = useAssignState();

  return (
    <AssignContext.Provider value={state}>{children}</AssignContext.Provider>
  );
}
