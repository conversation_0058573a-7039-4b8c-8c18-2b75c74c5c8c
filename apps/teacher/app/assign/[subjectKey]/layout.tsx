"use client";
import { useApp } from "@/hooks";
import { useMount } from "ahooks";
import { useParams, useRouter } from "next/navigation";
import { useContext } from "react";
import { AssignContext } from "../store";
import { AssignSubjectProvider, useAssignSubjectState } from "./store";

function AssignSubjectLayoutContainer({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { setOpen } = useApp();
  const { subjectKey } = useParams()!;
  const { subjectTaskTypes } = useContext(AssignContext);

  useMount(() => {
    setOpen(false);
  });

  if (
    !subjectKey ||
    !subjectTaskTypes.value.find(
      (item) =>
        item.subjectKey === Number(subjectKey) && item.taskTypes.includes(10)
    )
  ) {
    router.push("/assign");
    return null;
  }

  return (
    <div className="tch-assign-subject-layout bg-fill-light h-full w-full">
      {children}
    </div>
  );
}

export default function AssignSubjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const state = useAssignSubjectState();

  return (
    <AssignSubjectProvider value={state}>
      <AssignSubjectLayoutContainer>{children}</AssignSubjectLayoutContainer>
    </AssignSubjectProvider>
  );
}
