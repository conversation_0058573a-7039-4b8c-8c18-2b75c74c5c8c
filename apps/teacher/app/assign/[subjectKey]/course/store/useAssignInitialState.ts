import { AssignResourceTypeEnum } from "@/configs/assign";
import { useApp } from "@/hooks";
import { getAssignTaskDetail } from "@/services/assign";
import { AssignCourseResourceItem } from "@/types/assign/course";
import { isPathLowerThan } from "@/utils/tree";
import { useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useSearchParams } from "next/navigation";
import { createContext, useContext } from "react";

type InitialState = {
    selectedAiCourseIds: number[]
    selectedPracticeIds: number[]
    treeId: number
    bizTreeNodeId: number
}

/* 分2种情况：
    1. 从布置首页直接进来的
        只需要获取之前班级对应选择的章节就可以了
    2. 复制作业过来的
        需要获取作业的taskId，然后获取作业的章节信息
*/
export function useAssignInitialState() {
    const searchParams = useSearchParams()
    const taskId = searchParams?.get("taskId");
    const subjectKey = searchParams?.get("subjectKey");
    const { userInfo } = useApp()

    // 有taskId，就获取缓存
    const isInitialted = useSignal(taskId === null);
    const mode = useSignal<"copy" | "create">(taskId ? "copy" : "create");

    const getCachedAssignInitialStateKey = () => `assignInitialState_${userInfo?.userID}_${subjectKey}`

    const getCachedAssignInitialState = () => {
        const cached = localStorage.getItem(getCachedAssignInitialStateKey())
        if (!cached) {
            return null
        }

        return JSON.parse(cached) as InitialState
    }
    const setCachedAssignInitialState = (state: InitialState) => {
        localStorage.setItem(getCachedAssignInitialStateKey(), JSON.stringify(state))
    }

    const defaultState = useSignal<InitialState | null>(null)

    // 统一初始化逻辑
    useRequest(async () => {
        if (taskId === null) {
            return;
        }

        try {
            const res = await getAssignTaskDetail(Number(taskId))

            // 现阶段Ai课和练习需要单独处理下
            const aiCourseIds: number[] = []
            const practiceIds: number[] = []

            if (!res.resources || res.resources.length === 0) {
                return;
            }

            // NOTE：现在不支持跨教材选课，所以直接拿就好了
            const treeId = res.resources[0].bizTreeId!
            let lowerNode: AssignCourseResourceItem | null = null

            for (let i = 0; i < res.resources.length; i++) {
                const item = res.resources[i]
                if (isPathLowerThan(item.resourceExtra.lastLevelBizTreeNodeSerialPath, lowerNode?.resourceExtra.lastLevelBizTreeNodeSerialPath)) {
                    lowerNode = item
                }

                if (item.resourceType === AssignResourceTypeEnum.RESOURCE_AI_COURSE) {
                    aiCourseIds.push(Number(item.resourceId))
                } else if (item.resourceType === AssignResourceTypeEnum.RESOURCE_CONSOLIDATION_PRACTICE) {
                    practiceIds.push(Number(item.resourceId))
                }
            }

            defaultState.value = {
                selectedAiCourseIds: aiCourseIds,
                selectedPracticeIds: practiceIds,
                treeId: treeId,
                bizTreeNodeId: lowerNode?.bizTreeNodeId || 0,
            }
        } catch (err) {
            console.error(err)
        } finally {
            isInitialted.value = true
        }
    }, {
        ready: taskId !== null
    })


    return {
        isInitialted,
        defaultState,
        mode,

        setCachedAssignInitialState,
        getCachedAssignInitialState,
    };
}

export type AssignInitialStateContextType = ReturnType<typeof useAssignInitialState>

export const AssignInitialStateProvider = createContext<AssignInitialStateContextType | null>(null)

export function useAssignInitialStateWithCache() {
    const state = useContext(AssignInitialStateProvider)

    if (!state) {
        throw new Error("AssignInitialStateProvider is not found")
    }

    return state
}
