import type { DateTimeChangePayload } from "@/components/assign/assign-date-picker";
import { AssignResourceTypeEnum, AssignTaskTypeEnum } from "@/configs/assign";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { assignCourse } from "@/services/assign";
import { BizTreeDetailNode } from "@/types/assign";
import { AiCourse, AssignCourseParams, AssignCourseTimeRange, CoursePracticeItem, Practice } from "@/types/assign/course";
import { TargetJobClass } from "@/types/user";
import { toast } from "@/ui/toast";
import { calculateClassTime, getEndOfDayAfter } from "@/utils/date";
import { isPathLowerThan } from "@/utils/tree";
import { umeng, UmengAssignAction, UmengCategory } from "@/utils/umeng";
import { batch, Signal, useComputed, useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import type { AssignStep } from "../../store";

export type AiCourseWithBizTreeNode = AiCourse & {
    bizTreeNodeId?: number;
    bizTreeNodeName?: string;
}

export type PracticeWithBizTreeNode = Practice & {
    bizTreeNodeId?: number;
    bizTreeNodeName?: string;
}


export function getRecommendEndTime(startTime: number, totalClassTimes: number) {
    return getEndOfDayAfter(startTime, totalClassTimes);
}

// NOTE: 分的有点乱，后边再改
export function useAssignStep({
    selectedAiCourses,
    selectedPractices,
    checkedTargetClassList,
    currentSubject,
    treeId,
    bizTreeNodeMap,
    onConfirmed,
}: {
    selectedAiCourses: Signal<CoursePracticeItem[]>;
    selectedPractices: Signal<CoursePracticeItem[]>;
    checkedTargetClassList: Signal<TargetJobClass[]>;
    currentSubject: number;
    treeId: Signal<number | undefined>;
    bizTreeNodeMap: Signal<Map<number, BizTreeDetailNode & { firstNodeId: number | undefined }>>;
    onConfirmed?: (params: AssignCourseParams) => void;
}) {
    // "select-target" | "select-resource" | "set-time" | "course-preview" | "practice-preview"
    const currentAssignStep = useSignal<AssignStep>("select-target");

    // 需求不稳定，先冗余下资源
    const aiCoursePreviewData = useSignal<CoursePracticeItem | undefined>(undefined);
    const practicePreviewData = useSignal<CoursePracticeItem | undefined>(undefined);

    function setCurrentAssignStep(step: AssignStep) {
        currentAssignStep.value = step;
    }

    function goToSelectTarget() {
        setCurrentAssignStep("select-target")
    }

    function goToAiCoursePreview(aiCourse: CoursePracticeItem) {
        batch(() => {
            setCurrentAssignStep("course-preview")
            aiCoursePreviewData.value = aiCourse;
        })
    }

    function goToPracticePreview(practice: CoursePracticeItem) {
        batch(() => {
            setCurrentAssignStep("practice-preview")
            practicePreviewData.value = practice;
        })
    }

    function goToSetTime() {
        batch(() => {
            // 兜底下时间
            calcAssignTimeRange()
            setCurrentAssignStep("set-time")
        })
    }

    const assignTimeRanges = useSignal<AssignCourseTimeRange[]>([]);
    const hasInvalidTimeRanges = useComputed(() => {
        return assignTimeRanges.value.some((item) => {
            return item.endTime <= item.startTime;
        });
    });

    // 任务名称
    const assignName = useComputed(() => {
        // 选择最小的路径作为名称，如果有多个，就在名称最后加一个"等"
        if (selectedAiCourses.value.length === 0 && selectedPractices.value.length === 0) {
            return "";
        }

        const minPathNode = [...selectedAiCourses.value, ...selectedPractices.value].reduce((acc, item) => {
            if (item) {
                if (isPathLowerThan(item.bizTreeNodeSerialPath, acc.bizTreeNodeSerialPath)) {
                    return item;
                }
            }

            return acc;
        });

        return `${minPathNode.bizTreeNodeSerialPath} ${minPathNode.bizTreeNodeName}${selectedAiCourses.value.length + selectedPractices.value.length > 1 ? "等" : ""}`;
    });

    const totalClassTimes = useComputed(() => {
        // 总课时
        return calculateClassTime(selectedAiCourses.value.reduce((acc, item) => {
            return acc + (item.aiCourse.totalDuration || 0) / 60;
        }, 0) + selectedPractices.value.reduce((acc, item) => {
            return acc + (item.practice.estimatedTime || 0);
        }, 0));
    })

    // 按班级分时间
    function calcAssignTimeRange() {
        // TODO: 这里需要更新assignTimeRanges
        const pendingAssignTimeRanges: AssignCourseTimeRange[] = [];
        const checkedClasses = checkedTargetClassList.value;

        for (const cls of checkedClasses) {
            pendingAssignTimeRanges.push({
                classInfo: cls,
                isImmediate: true,
                isRecommend: true,
                startTime: Date.now(),
                // PM: 推荐时间的策略：根据已选的课时数做推荐，1课时对应后延1工作日（举例：今天是3-17，布置1课时的课程任务，则推荐时间是2025-3-18 23:59；布置2课时的课程任务，则推荐时间是2025-3-19 23:59）
                endTime: getEndOfDayAfter(Date.now(), totalClassTimes.value),
            })
        }

        assignTimeRanges.value = pendingAssignTimeRanges;

        return pendingAssignTimeRanges;
    }

    useSignalEffect(() => {
        // 这里加是因为布置页里还能通过Drawer增减课程
        // NOTE：需要跟PM battle下这里了，都没了会怎么样，这是个交互Bug
        calcAssignTimeRange()
    })

    function updateAssignTimeRange(payload: DateTimeChangePayload) {
        const { type, isStart, index, customTimestamp } = payload;

        const assignTimeRange = assignTimeRanges.value[index];
        if (!assignTimeRange) {
            return;
        }

        if (type === "immediate") {
            assignTimeRange.isImmediate = type === "immediate";
            assignTimeRange.startTime = Date.now();
        } else if (type === "recommend") {
            assignTimeRange.isRecommend = true;
            assignTimeRange.endTime = getEndOfDayAfter(Date.now(), totalClassTimes.value);
        } else if (type === "custom") {
            if (isStart) {
                assignTimeRange.isImmediate = false;
                assignTimeRange.startTime = customTimestamp;

                // 如果结束时间小于开始时间，则需要重新计算结束时间
                if (assignTimeRange.endTime < customTimestamp) {
                    assignTimeRange.isRecommend = true;
                    assignTimeRange.endTime = getEndOfDayAfter(customTimestamp, totalClassTimes.value);
                }
            } else {
                assignTimeRange.isRecommend = false;
                assignTimeRange.endTime = customTimestamp;
            }
        }

        assignTimeRanges.value[index] = assignTimeRange

        assignTimeRanges.value = [...assignTimeRanges.value];
    }

    // 无效的时间范围
    // 感觉可以优化控件，而不是靠计算
    const invalidTimeRanges = useComputed(() => {
        return assignTimeRanges.value.filter((item) => {
            return item.endTime <= item.startTime;
        });
    });

    const { gotoTchAssignPage } = useTchNavigation();
    const confirmAssignRequest = useRequest(async (params: AssignCourseParams) => {
        try {
            await assignCourse(params)
            toast.success("布置成功");
            umeng.trackEvent(
                UmengCategory.ASSIGN,
                UmengAssignAction.TASK_LIST_COURSE_SETUP_SUBMIT,
                {}
            );
            gotoTchAssignPage(true);
        } catch (err) {
            console.error(err);
            if (err instanceof Error && err.message) {
                toast.error(err.message);
            }
        }
    }, {
        manual: true,
    })

    function confirmAssign() {
        const resourceList = [...selectedAiCourses.value.map(item => {
            const parentId = bizTreeNodeMap.value.get(item.bizTreeNodeId)?.firstNodeId
            const parentNode = parentId ? bizTreeNodeMap.value.get(parentId) || item : item;

            return {
                resourceType: AssignResourceTypeEnum.RESOURCE_AI_COURSE,
                resourceId: item.aiCourse.id + '',
                resourceExtra: {
                    firstLevelBizTreeNodeId: parentNode.bizTreeNodeId,
                    firstLevelBizTreeNodeName: parentNode.bizTreeNodeName,
                    firstLevelBizTreeNodeSerialPath: parentNode.bizTreeNodeSerialPath,

                    lastLevelBizTreeNodeId: item.bizTreeNodeId,
                    lastLevelBizTreeNodeName: item.bizTreeNodeName,
                    lastLevelBizTreeNodeSerialPath: item.bizTreeNodeSerialPath,
                }
            }
        }), ...selectedPractices.value.map(item => {
            const parentId = bizTreeNodeMap.value.get(item.bizTreeNodeId)?.firstNodeId
            const parentNode = parentId ? bizTreeNodeMap.value.get(parentId) || item : item;

            return {
                resourceType: AssignResourceTypeEnum.RESOURCE_CONSOLIDATION_PRACTICE,
                resourceId: item.practice.questionSetId + '',
                resourceExtra: {
                    firstLevelBizTreeNodeId: parentNode.bizTreeNodeId,
                    firstLevelBizTreeNodeName: parentNode.bizTreeNodeName,
                    firstLevelBizTreeNodeSerialPath: parentNode.bizTreeNodeSerialPath,

                    lastLevelBizTreeNodeId: item.bizTreeNodeId,
                    lastLevelBizTreeNodeName: item.bizTreeNodeName,
                    lastLevelBizTreeNodeSerialPath: item.bizTreeNodeSerialPath,
                }
            }
        })]

        const params = {
            subject: currentSubject,
            taskType: AssignTaskTypeEnum.TASK_TYPE_COURSE,
            taskName: assignName.value,
            // 这个参数传的就很奇怪，要是这么传的话，就不应该跨教材选课
            bizTreeId: treeId.value || 0,
            teacherComment: "",
            resources: resourceList,
            studentGroups: assignTimeRanges.value.map((item) => {
                const { classInfo, startTime, endTime, isImmediate, isRecommend } = item;
                return {
                    groupType: 2,// 有待确认是个啥
                    groupId: classInfo.jobClass,
                    studentIds: [],
                    startTime: isImmediate
                        ? Math.floor(Date.now() / 1000)
                        : Math.floor(startTime / 1000),
                    deadline: isRecommend
                        ? Math.floor(getRecommendEndTime(startTime, totalClassTimes.value) / 1000)
                        : Math.floor(endTime / 1000),
                };
            }),
        };

        return confirmAssignRequest.runAsync(params).then(() => {
            onConfirmed?.(params)
        });
    }

    return {
        currentAssignStep,


        aiCoursePreviewData,
        practicePreviewData,

        // 这些View切换应该拆除去
        goToSelectTarget,
        goToAiCoursePreview,
        goToPracticePreview,
        goToSetTime,

        assignTimeRanges,
        hasInvalidTimeRanges,

        assignName,
        totalClassTimes,
        updateAssignTimeRange,
        invalidTimeRanges,

        confirmAssign,
    }
}