"use client";
import { AssignCard } from "@/components/assign/assign-card";
import { AssignCourseList } from "@/components/assign/assign-course-list";
import { AssignEmpty } from "@/components/assign/assign-empty";
import { AssignHeader } from "@/components/assign/assign-header";
import { AssignHeading } from "@/components/assign/assign-heading";
import { AssignKnowledgeSelect } from "@/components/assign/assign-knowledge-select";
import { AssignPageContainer } from "@/components/assign/assign-page-container";
import { AssignTargets } from "@/components/assign/assign-targets";
import { useRef } from "react";

import { AssignCancelAlert } from "@/components/assign/assign-cancel-alert";
import { AssignCourseTime } from "@/components/assign/assign-course-time";
import { AssignFooter } from "@/components/assign/assign-footer";
import { useApp } from "@/hooks/useApp";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { ScrollArea } from "@/ui/scroll-area";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useDebounceFn } from "ahooks";
import AssignCoursePreview from "./_components/AssignCoursePreview";
import AssignPracticePreview from "./_components/AssignPracticePreview";
import { useAssignCourseContext } from "./store";

export default function AssignPage() {
  const {
    currentAssignStep,
    checkedTargetClassIds,
    goToSelectTarget,
    selectedPractices,
    selectedAiCourses,
    mode,
  } = useAssignCourseContext();

  const { statusBarHeight } = useApp();

  const { gotoBack } = useTchNavigation();

  // NOTE：页面逻辑 —— 当滑动布置内容的元素时，自动滚动到顶部
  const containerRef = useRef<HTMLDivElement>(null);

  const { run: handleScroll } = useDebounceFn(
    (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.target as HTMLDivElement;
      const scrollTop = target.scrollTop;

      if (scrollTop > 0) {
        containerRef.current?.scrollTo({
          top: containerRef.current?.scrollHeight,
          behavior: "smooth",
        });
        return;
      }
      containerRef.current?.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    },
    {
      wait: 100,
    }
  );

  // 挺离谱的，之后改成function call吧
  const cancelAssignOpen = useSignal(false);
  const onBack = () => {
    if (currentAssignStep.peek() !== "select-target") {
      goToSelectTarget();
      return;
    }

    if (
      selectedAiCourses.value.length > 0 ||
      selectedPractices.value.length > 0
    ) {
      cancelAssignOpen.value = true;
    } else {
      gotoBack();
    }
  };

  const showTreeNode = useComputed(() => {
    if (mode.value === "copy") {
      return true;
    } else {
      return checkedTargetClassIds.value.length > 0;
    }
  });

  return (
    <>
      {/* 练习预览 */}
      {currentAssignStep.value === "practice-preview" && (
        <AssignPracticePreview />
      )}

      {/* 课程预览 */}
      {currentAssignStep.value === "course-preview" && <AssignCoursePreview />}

      {/* 其他情况 */}
      {currentAssignStep.value !== "course-preview" &&
        currentAssignStep.value !== "practice-preview" && (
          <AssignPageContainer>
            <AssignHeader
              className="flex-shrink-0 flex-grow-0"
              title="布置课程任务"
              onBack={onBack}
            />
            <div className="mb-17 flex h-full overflow-hidden">
              {currentAssignStep.value === "select-target" ? (
                <ScrollArea
                  orientation="vertical"
                  className="flex-1 overflow-hidden"
                  viewportRef={containerRef}
                >
                  <div className="flex h-full flex-1 flex-col pl-6 pr-4">
                    {/* 1.选择布置对象 */}
                    <div className="pb-6 pt-2">
                      <AssignHeading
                        content="1.选择布置对象"
                        className="mb-4"
                      />
                      <AssignTargets />
                    </div>

                    {/* 2.选择任务内容 */}
                    <div className="pb-4 pt-2">
                      <AssignHeading content="2.选择任务内容" />
                    </div>

                    {showTreeNode.value ? (
                      <div
                        className="flex h-full flex-shrink-0 flex-grow-0 items-stretch gap-2"
                        style={{
                          height: `calc(100vh - ${statusBarHeight}px - 8.625rem)`,
                        }}
                      >
                        {/* 业务树 - 教材 + 章节树 */}
                        <AssignCard
                          className="w-[27%] rounded-[1.25rem]"
                          onScroll={handleScroll}
                        >
                          <AssignKnowledgeSelect />
                        </AssignCard>
                        {/* 资源列表 - 章节（Ai课 + 练习） */}
                        <AssignCourseList
                          className="flex-1"
                          onScroll={handleScroll}
                        />
                      </div>
                    ) : (
                      <AssignEmpty
                        type="course"
                        style={{
                          height: `calc(100vh - ${statusBarHeight}px - 18.375rem)`,
                        }}
                      />
                    )}
                  </div>
                </ScrollArea>
              ) : (
                <div className="flex h-full flex-1 flex-col pb-2 pl-6 pr-4 pt-2">
                  <AssignCourseTime className="h-full" />
                </div>
              )}
            </div>
            <div className="fixed bottom-0 left-0 right-0">
              <AssignFooter />
            </div>
            <AssignCancelAlert
              open={cancelAssignOpen.value}
              onCancel={() => {
                cancelAssignOpen.value = false;
              }}
              onOk={() => {
                cancelAssignOpen.value = false;
                gotoBack();
              }}
            />
          </AssignPageContainer>
        )}
    </>
  );
}
