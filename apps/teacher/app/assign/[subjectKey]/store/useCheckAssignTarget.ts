import { useApp } from "@/hooks/useApp";
import { getStudentsByClassIds } from "@/services/assign";
import { TargetJobClass } from "@/types";
import { getTeacherClassListBySubjectKey } from "@/utils";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useRequest } from "ahooks";
import { useParams } from "next/navigation";
import { useMemo } from "react";

export default function useCheckAssignTarget() {
    const { userInfo } = useApp();
    const { subjectKey } = useParams()!;
    const currentSubjectKey = Number(subjectKey);

    // 可选择的班级 from 用户信息
    const targetClassList = useMemo(() => {
        return getTeacherClassListBySubjectKey(currentSubjectKey, userInfo);
    }, [currentSubjectKey, userInfo])

    // 选中的班级
    const checkedClasses = useSignal<TargetJobClass[]>([]);

    // NOTE: 目前是选了班级就需要学生信息，未来可以考虑拆除去，但目前是非必须
    // 选中的班级学生列表
    const { data: checkedClassStudentList, loading: isLoadingCheckedClassStudentList } = useRequest(async () => {
        const classIdsStr = checkedClasses.value.map((item) => item.jobClass).join(",");
        if (!classIdsStr) {
            return [];
        }

        const res = await getStudentsByClassIds(classIdsStr)
        return res.map((item) => ({
            ...item,
            classIDStr: String(item.classID),
        }))
    }, {
        refreshDeps: [checkedClasses.value.map((item) => item.jobClass).join(",")],
    })

    // Methods
    function checkClass(jobClass: number) {
        const targetClass = targetClassList?.find((item) => item.jobClass === jobClass);
        if (targetClass) {
            checkedClasses.value.push(targetClass);
        }
    }

    function unCheckClass(jobClass: number) {
        const targetClass = targetClassList?.find((item) => item.jobClass === jobClass);
        if (targetClass) {
            checkedClasses.value = checkedClasses.value.filter((item) => item.jobClass !== jobClass);
        }
    }

    function toggleClass(jobClass: number) {
        const targetClass = targetClassList?.find((item) => item.jobClass === jobClass);
        if (targetClass) {
            checkedClasses.value = checkedClasses.value.includes(targetClass) ? checkedClasses.value.filter((item) => item.jobClass !== jobClass) : [...checkedClasses.value, targetClass];
        }
    }

    // 不确定要不要的属性，八成是给ui用的，要没用就干掉
    // 选中的班级id
    const checkedTargetClassIds = useComputed(() => {
        return checkedClasses.value.map((item) => item.jobClass);
    });

    // 班级map
    const targetClassMap = useMemo(() => {
        return targetClassList?.reduce((acc, item) => {
            acc[item.jobClass] = item;
            return acc;
        }, {} as Record<number, TargetJobClass>);
    }, [targetClassList]);

    return {
        targetClassList,

        checkedClasses,
        checkedClassStudentList,
        isLoadingCheckedClassStudentList,

        checkedTargetClassIds,
        targetClassMap,

        checkClass,
        unCheckClass,
        toggleClass,
    }
}