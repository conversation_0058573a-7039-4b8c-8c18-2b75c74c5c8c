"use client";
import { questionFilterExtraOptions } from "@/configs";
import { getQuestionFilterEnum } from "@/services/assign-homework";
import { QuestionFilterEnumDatas, QuestionFilterEnumDatasMap } from "@/types/assign";
import { convertQuestionFilterEnumToMap } from "@/utils";
import { useRequest } from "ahooks";
import { createContext, useMemo } from "react";

export type AssignTimeRangeType = "immediate" | "recommend" | "custom";
export interface UpdateAssignTimeRangeParams {
    isStart: boolean;
    index: number;
    type: AssignTimeRangeType;
    customTimestamp?: number;
}

export interface AssignSubjectContextType {
    questionFilterEnumMap: QuestionFilterEnumDatasMap;
}

export type AssignStep = 'select-target' | 'select-resource' | 'set-time' | 'course-preview' | 'practice-preview';

export function useAssignSubjectState(): AssignSubjectContextType {
    const { data: questionFilterEnum } = useRequest(async () => {
        try {
            const res = await getQuestionFilterEnum()
            const result = res;
            return {
                ...questionFilterExtraOptions,
                ...result,
            }
        } catch (e) {
            return {
                ...questionFilterExtraOptions,
            }
        }
    })

    const questionFilterEnumMap = useMemo(() => {
        if (!questionFilterEnum) {
            return {} as QuestionFilterEnumDatasMap
        }
        // TODO: fix type
        return convertQuestionFilterEnumToMap(questionFilterEnum as QuestionFilterEnumDatas) as QuestionFilterEnumDatasMap
    }, [questionFilterEnum])

    return {
        questionFilterEnumMap
    };
}

export const AssignSubjectContext =
    createContext<AssignSubjectContextType>({} as AssignSubjectContextType);

export const AssignSubjectProvider =
    AssignSubjectContext.Provider as React.Provider<AssignSubjectContextType>;
