import { CommonResponseType } from ".";

export type TreeType = "bizTree" | "knowledgeTree";

export interface BizTreeListItem {
  bizTreeId: number;
  bizTreeName: string;
  subject: number;
  baseTreeId?: number;
  bizTreeVersion?: string;
  classType?: number;
  material?: number;
  phase?: number;
  // 自定义字段 - 存放树的详情
  treeFormatInfo?: BizTreeDetailFormatInfo;
}
export type BizTreeListData = BizTreeListItem[]
export type BizTreeListResponse = CommonResponseType<BizTreeListData>;





export type BizTreeDetailResponse = CommonResponseType<BizTreeDetailData>

export interface BizTreeDetailData {
  baseTreeId: number;
  bizTreeDetail: BizTreeDetailNode;
  bizTreeId: number;
  bizTreeName: string;
  bizTreeVersion: string;
  phase: number;
  subject: number;
  [property: string]: unknown;
}

export interface BizTreeDetailNode {
  bizTreeId: number;
  bizTreeName: string;
  bizTreeNodeChildren: BizTreeDetailNode[];
  bizTreeNodeId: number;
  bizTreeNodeName: string;
  bizTreeNodeSerialPath: string;
  nodeTag?: string;
  nodeIndex?: number;
  parentIds?: number[];
  [property: string]: unknown;
}

export interface BizTreeDetailFormatInfo {
  treeData: BizTreeDetailData | undefined;
  treeNodeMap: Map<number, BizTreeDetailNode>;
  treeNodeIndexMap: Map<number, BizTreeDetailNode>;
}

export interface BizTreeDetailBaseTreeNodeNameMap {
  [property: string]: unknown;
}

