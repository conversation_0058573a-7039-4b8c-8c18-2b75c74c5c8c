import { AssignResourceTypeEnum, AssignTaskTypeEnum } from "@/configs/assign";
import { CommonResponseType } from ".";
import { TargetJobClass } from "../user";

export type AssignCourseResourceType = "aiCourse" | "practice";

export interface CoursePracticeListParams {
  subject: number;
  bizTreeId: number;
  bizTreeNodeId: number;
}

export interface CourseBaseInfo {
  bizTreeNodeId: number;
  bizTreeNodeName: string;
  bizTreeNodeSerialPath?: string;
}

export interface CoursePracticeItem {
  aiCourse: AiCourse;
  bizTreeNodeId: number;
  bizTreeNodeName: string;
  bizTreeNodeSerialPath: string;
  isRecommend: boolean;
  /**
   * 巩固练习
   */
  practice: Practice;
}

export interface CourseResourceItem {
  bizTreeNodeId: number;
  bizTreeNodeName: string;
  isRecommend: boolean;
  aiCourse?: AiCourse;
  practice?: Practice;
  resourceExtra: ResourceExtra;
}

export interface Practice {
  /**
   * 该资源已布置过的班级ID
   */
  assignedClassIds?: number[];
  id: number;
  questionSetId?: number;
  estimatedTime?: number;
  estimatedTimeStr?: string;
  bizTreeNodeIndex?: number;
}

export interface AiCourse {
  id: number;
  assignedClassIds?: number[];
  estimatedTime?: number;
  estimatedTimeStr?: string;
  bizTreeNodeIndex?: number;
  totalDuration?: number;
}

export type CoursePracticeListResponse = CommonResponseType<
  CoursePracticeItem[]
>;

export interface AssignCourseParams {
  subject: number;
  taskType: AssignTaskTypeEnum;
  taskName: string;
  bizTreeId?: number;
  teacherComment: string;
  resources: AssignCourseResourceItem[];
  studentGroups: AssignCourseStudentGroupItem[];
}

export interface AssignCourseResourceItem {
  resourceId: string;
  resourceType: AssignResourceTypeEnum;
  subject?: number;
  bizTreeId?: number;
  bizTreeNodeId?: number;
  resourceExtra: ResourceExtra;
}

// TODO: 这个类型是临时用的，需要优化
export interface AssignCourseResourceItemWithExtra
  extends AssignCourseResourceItem {
  resourceExtra: ResourceExtra;
}

export interface AssignCourseStudentGroupItem {
  groupType: number;
  groupId: number;
  studentIds: number[];
  // classId: number;
  startTime: number;
  deadline: number;

  assignId?: number;
  schoolId?: number;
  taskId?: number;
}

export type AssignCourseResponse = CommonResponseType<unknown>;

export interface AssignCourseTimeRange {
  isImmediate: boolean;
  isRecommend: boolean;
  startTime: number;
  endTime: number;
  classInfo: TargetJobClass;
}

export interface AssignCourseTimeRangeItem {
  isImmediate: boolean;
  isRecommend: boolean;
  startTime: number;
  endTime: number;
}

export interface AssignTaskDetailData {
  subject: number;
  taskType: AssignTaskTypeEnum;
  taskName: string;
  bizTreeId?: number;
  teacherComment: string;
  resources: AssignCourseResourceItem[];
  studentGroups: AssignCourseStudentGroupItem[];
  taskExtraInfo: string;

  createTime: number;
  phase: number;
  schoolId: number;
  taskId: number;
  taskSubType: number;
  updateTime: number;
}

export interface ResourceExtra {
  /**
   * 业务树下第一层节点
   */
  firstLevelBizTreeNodeId: number;
  /**
   * 业务树下第一层节点章节名称
   */
  firstLevelBizTreeNodeName: string;
  /**
   * 1
   */
  firstLevelBizTreeNodeSerialPath: string;
  /**
   * 业务树下最底层知识点ID
   */
  lastLevelBizTreeNodeId: number;
  /**
   * 业务树下最底层知识点名称
   */
  lastLevelBizTreeNodeName: string;
  /**
   * 层级 例如 三级1.1.1 四级1.1.1.1
   */
  lastLevelBizTreeNodeSerialPath: string;
  [property: string]: any;
}
