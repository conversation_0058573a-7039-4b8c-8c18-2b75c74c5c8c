import assignIcon from "@/public/assign/assign.png";
import homeworkIcon from "@/public/assign/homework.png";
import resourceIcon from "@/public/assign/resource.png";
import testIcon from "@/public/assign/test.png";
import { StaticImageData } from "next/image";

export const CLASS_HOUR = 50;
export const CLASS_HOUR_DEVIATION = 10;

// 主色
export const assign_color = '#D2E8FF';
export const homework_color = '#E0E0FF';
export const resource_color = '#FFE5C7';
export const test_color = '#D2FACE';

// 辅色
export const assign_color_light = '#C2E0FF';
export const homework_color_light = '#CCCCFF';
export const resource_color_light = '#FFD29E';
export const test_color_light = '#B9F2B3';

export enum AssignResourceTypeEnum {
    RESOURCE_AI_COURSE = 101,
    RESOURCE_CONSOLIDATION_PRACTICE = 102,
    RESOURCE_QUESTION = 103,
    RESOURCE_PAPER = 104,
    RESOURCE_OTHER = 100,
}

export enum AssignTaskTypeEnum {
    TASK_TYPE_COURSE = 10,
    TASK_TYPE_HOMEWORK = 20,
    TASK_TYPE_TEST = 30,
    TASK_TYPE_RESOURCE = 40,
}


export interface AssignCategory {
    taskType: AssignTaskTypeEnum;
    title: string;
    subtitle: string;
    icon: StaticImageData;
    show: boolean;
    id: string;
    defaultClassName: string;
    hoverClassName: string;
    activeClassName: string;
    umengTag: string;
    enable: boolean;
}



export const ASSIGN_CATEGORIES: AssignCategory[] = [
    {
        taskType: AssignTaskTypeEnum.TASK_TYPE_COURSE,
        title: "课程",
        subtitle: "布置AI课及巩固练习",
        icon: assignIcon,
        show: true,
        id: "course",
        defaultClassName: "from-blue-100 to-blue-200 text-[#1B4D7F]",
        hoverClassName: "hover:from-blue-100 hover:to-blue-200",
        activeClassName: "active:bg-none! active:bg-blue-3!",
        umengTag: "course_task",
        enable: true,
    },
    {
        taskType: AssignTaskTypeEnum.TASK_TYPE_HOMEWORK,
        title: "作业",
        subtitle: "布置课后作业",
        icon: homeworkIcon,
        show: true,
        id: "homework",
        defaultClassName: "from-indigo-100 to-indigo-200 text-[#502C8F]",
        hoverClassName: "hover:from-indigo-100 hover:to-indigo-200",
        activeClassName: "active:bg-none! active:bg-purple-3!",
        umengTag: "homework_task",
        enable: false,
    },
    {
        taskType: AssignTaskTypeEnum.TASK_TYPE_RESOURCE,
        title: "资源",
        subtitle: "布置学习资源",
        icon: resourceIcon,
        show: true,
        id: "resource",
        defaultClassName: "from-amber-100 to-orange-200 text-[#6F340E]",
        hoverClassName: "hover:from-amber-100 hover:to-orange-200",
        // activeClassName: "active:bg-none! active:bg-orange-3!",
        activeClassName: "",
        umengTag: "resource_task",
        enable: false,
    },
    {
        taskType: AssignTaskTypeEnum.TASK_TYPE_TEST,
        title: "测验",
        subtitle: "布置在线测试",
        icon: testIcon,
        show: true,
        id: "test",
        defaultClassName: "from-emerald-100 to-green-200 text-[#106615]",
        hoverClassName: "hover:from-emerald-100 hover:to-green-200",
        // activeClassName: "active:bg-none! active:bg-green-3!",
        activeClassName: "",
        umengTag: "test_task",
        enable: false,
    },
];

export const HOMEWORK_RESOURCE_TYPES = [
    {
        id: "public",
        label: "公共资源",
    },
    // {
    //     id: "private",
    //     label: "我的资源",
    // }
]

export const PUBLIC_RESOURCE_CATEGORIES = [
    {
        id: "chapter",
        label: "章节选题",
    },
    {
        id: "knowledge",
        label: "知识点选题",
    },
    // {
    //     id: "survey",
    //     label: "试卷选题",
    // },
]

export const PRIVATE_RESOURCE_CATEGORIES = [
    {
        id: "chapter",
        label: "章节选题",
    },
    {
        id: "survey",
        label: "试卷选题",
    },
]