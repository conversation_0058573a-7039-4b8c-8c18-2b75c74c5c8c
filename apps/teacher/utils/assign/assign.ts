import { getStatsFromReport } from "@/app/homework/_components/task-card";
import { ASSIGN_CATEGORIES, AssignTaskTypeEnum } from "@/configs/assign";
import { JOB_TYPE, QuestionFilterSort, QuestionFilterSource, taskTypeEnumManager } from "@/enums";
import { TargetJobClass, TeacherJobInfo, UserInfo } from "@/types";
import { BizTreeDetailData, BizTreeDetailFormatInfo, BizTreeDetailNode, CommonOptionType, QuestionFilterEnumDatas, QuestionFilterEnumDatasMap, QuestionFilterEnumMapKey, QuestionFilterEnumValueType, QuestionFilterOption, QuestionFiltersType, UserSubjectItem } from "@/types/assign";
import { Homework, StatData } from "@/types/homeWork";
import { formatPublishTime } from "../date";

// 根据taskTypes获取对应的assignCategories
export const getAssignCategories = (taskTypes: AssignTaskTypeEnum[]) => {
  return ASSIGN_CATEGORIES.filter((category) =>
    taskTypes.includes(category.taskType)
  );
};

export const getSubjectBySubjectKey = (
  subjectKey: number,
  subjectTaskTypes: UserSubjectItem[]
) => {
  return subjectTaskTypes.find((subject) => subject.subjectKey === subjectKey);
};

// 根据科目key获取教师job信息
export const getTeacherJobInfoBySubjectKey = (
  subjectKey: number,
  userInfo: UserInfo | null
) => {
  const teacherJobInfos = userInfo?.teacherJobInfos ?? [];
  const assignJobInfos = teacherJobInfos.filter((jobInfo) => {
    const { jobType, jobSubject } = jobInfo;
    // 是否是科目教师，且科目key匹配
    if (jobType?.jobType === JOB_TYPE.JOB_TYPE_SUBJECT_TEACHER) {
      return jobSubject?.jobSubject === subjectKey;
    }
    // 是否是班主任，班主任不判断科目key， 他有所有科目
    return jobType?.jobType === JOB_TYPE.JOB_TYPE_CLASS_TEACHER;
  });

  return assignJobInfos;
};

export const getTeacherTargetClassList = (jobs: TeacherJobInfo[]) => {
  const jobGradeClassesMap = new Map<string, TargetJobClass>();
  jobs.forEach((job) => {
    const { jobInfos = [] } = job;
    jobInfos?.forEach((jobInfo) => {
      const { jobGrade, jobClass = [], name } = jobInfo;
      jobClass.forEach((cls) => {
        jobGradeClassesMap.set(`${jobGrade}_${cls.jobClass}`, {
          ...cls,
          jobGrade,
          jobGradeName: name
        })
      });
    });
  });
  return Array.from(jobGradeClassesMap.values());
}

// 根据科目key获取教师job信息中的班级列表
export const getTeacherClassListBySubjectKey = (
  subjectKey: number,
  userInfo: UserInfo | null
) => {
  const teacherJobInfo = getTeacherJobInfoBySubjectKey(subjectKey, userInfo);
  if (!teacherJobInfo || teacherJobInfo.length === 0) {
    return [];
  }
  // const { jobInfos = [] } = teacherJobInfo;
  // return jobInfos.reduce((prev: TargetJobClass[], item: JobInfo) => {
  //   const { jobGrade, jobClass = [] } = item;
  //   const classes = jobClass.map((cls) => ({
  //     ...cls,
  //     jobGrade,
  //     name: `${cls.name}`,
  //   }));
  //   return [...prev, ...classes];
  // }, []);
  return getTeacherTargetClassList(teacherJobInfo);
};

export const sortLatestAssignTasks = (tasks: Homework[]) => {
  const taskTypeOrder = {} as Record<AssignTaskTypeEnum, number>;
  ASSIGN_CATEGORIES.forEach((category, idx) => {
    taskTypeOrder[category.taskType] = idx;
  });
  return tasks.sort((a, b) => {
    const indexA = taskTypeOrder[a.taskType as unknown as AssignTaskTypeEnum];
    const indexB = taskTypeOrder[b.taskType as unknown as AssignTaskTypeEnum];

    if (indexA !== undefined && indexB !== undefined) {
      return indexA - indexB;
    } else if (indexA !== undefined) {
      return -1;
    }
    // 如果只有 b 在 arr1 中，b 排在前面
    else if (indexB !== undefined) {
      return 1;
    }
    // 如果都不在 arr1 中，保持原顺序
    else {
      return 0;
    }
  });
};

export const formatAssignTaskDate = (statData?: StatData) => {
  if (!statData?.startTime) return "";
  const startTimestamp = statData.startTime * 1000;
  return formatPublishTime(startTimestamp);
}

export const formatAssignTaskExtraInfo = (task: Homework) => {
  const { taskId, taskName, taskType, reports } = task

  const classes = reports.map(report => {
    const stats = getStatsFromReport(report.statData, taskType)
    const isDue = report.statData?.deadline ? report.statData.deadline < Date.now() / 1000 : false

    return {
      assignId: report.assignId,
      id: report.assignObject.id,
      statData: report.statData,
      name: report.assignObject.name,
      stats,
      isDue,
      classData: {
        taskId, taskName, taskType,
        ...report
      }
    }
  })

  return {
    creatorId: task.creatorId,
    subject: task.subject,
    id: taskId,
    title: taskName,
    type: taskType,
    typeName: taskTypeEnumManager.getLabelByValue(taskType) || "",
    bgColor: taskTypeEnumManager.getEnumByValue(taskType)?.bg || "",
    lineColor: taskTypeEnumManager.getEnumByValue(taskType)?.lineColor || "",
    date: formatAssignTaskDate(task.reports[0]?.statData),
    startTime: task.reports[0]?.statData?.startTime || 0,
    endTime: task.reports[0]?.statData?.deadline || 0,
    classes,
    homeworkData: task
  }
};

export const initTreeFormatInfo = () => ({
  treeData: undefined,
  treeNodeMap: new Map<number, BizTreeDetailNode>(),
  treeNodeIndexMap: new Map<number, BizTreeDetailNode>(),
});

export function traverseBizTree(treeData: BizTreeDetailData | undefined): BizTreeDetailFormatInfo {
  if (!treeData) return initTreeFormatInfo();
  const treeNodes = treeData.bizTreeDetail.bizTreeNodeChildren;
  if (!treeNodes || treeNodes.length === 0) return initTreeFormatInfo();
  const treeNodeMap = new Map<number, BizTreeDetailNode>();
  const treeNodeIndexMap = new Map<number, BizTreeDetailNode>();

  let nodeIndex = 0;
  // 遍历树节点生成树数据列表和叶子节点列表
  function traverse(node: BizTreeDetailNode, index: number, parentTag: string, parentIds: number[]) {
    const tag = String(index + 1);
    const nodeTag = parentTag ? `${parentTag}.${tag}` : tag;
    nodeIndex++;
    node.nodeTag = nodeTag;
    node.nodeIndex = nodeIndex;
    node.parentIds = parentIds;
    treeNodeMap.set(node.bizTreeNodeId, node);
    treeNodeIndexMap.set(nodeIndex, node);
    if (node.bizTreeNodeChildren && node.bizTreeNodeChildren.length > 0) {
      node.bizTreeNodeChildren.forEach((child, idx) => {
        traverse(child, idx, node.nodeTag ?? '', [...parentIds, node.bizTreeNodeId]);
      });
    }
  }
  treeNodes.forEach((node, idx) => traverse(node, idx, '', []));

  return {
    treeData,
    treeNodeMap,
    treeNodeIndexMap,
  }
}

export function getTreeDefaultNodeById(treeInfo: BizTreeDetailFormatInfo, id: number | undefined) {
  const { treeData, treeNodeMap } = treeInfo;
  const root = treeData?.bizTreeDetail;
  // 如果根节点不存在，返回undefined
  if (!root) return undefined;
  // 如果id不存在，返回根节点
  if (!id) return root;
  // 如果id存在，返回对应的节点
  const node = treeNodeMap.get(id);
  if (node) return node;
  // 如果id不存在，返回根节点
  return root;
}

export function filterTreeNodes(tree: BizTreeDetailNode[], str: string) {
  // 如果str是空字符串，直接返回原树
  if (str === '') {
    return tree;
  }

  // 递归过滤函数
  const filterNode = (node: BizTreeDetailNode) => {
    // 检查当前节点是否匹配
    const isMatch = node.bizTreeNodeName.includes(str);

    // 过滤子节点
    const filteredChildren: BizTreeDetailNode[] = node.bizTreeNodeChildren
      ? node.bizTreeNodeChildren.map(filterNode).filter(child => child !== null)
      : [];

    // 如果当前节点匹配或者有匹配的子节点，则保留该节点
    if (isMatch || filteredChildren.length > 0) {
      return {
        ...node,
        bizTreeNodeChildren: filteredChildren
      };
    }

    // 否则返回null表示不保留该节点
    return null;
  };

  // 对树的每个节点进行过滤并去除null值
  return tree.map(filterNode).filter(node => node !== null);
}
export function traverseKnowledgeTree(treeData: BizTreeDetailData | undefined): BizTreeDetailFormatInfo {
  if (!treeData) return initTreeFormatInfo();
  const treeNodes = treeData.bizTreeDetail.bizTreeNodeChildren;
  if (!treeNodes || treeNodes.length === 0) return initTreeFormatInfo();
  const treeNodeMap = new Map<number, BizTreeDetailNode>();
  const treeNodeIndexMap = new Map<number, BizTreeDetailNode>();

  let nodeIndex = 0;
  // 遍历树节点生成树数据列表和叶子节点列表
  function traverse(node: BizTreeDetailNode, index: number, parentTag: string) {
    const tag = String(index + 1);
    const nodeTag = parentTag ? `${parentTag}.${tag}` : tag;
    nodeIndex++;
    node.nodeTag = nodeTag;
    node.nodeIndex = nodeIndex;
    treeNodeMap.set(node.bizTreeNodeId, node);
    treeNodeIndexMap.set(nodeIndex, node);
    if (node.bizTreeNodeChildren && node.bizTreeNodeChildren.length > 0) {
      node.bizTreeNodeChildren.forEach((child, idx) => {
        traverse(child, idx, node.nodeTag ?? '');
      });
    }
  }
  treeNodes.forEach((node, idx) => traverse(node, idx, ''));

  return {
    treeData,
    treeNodeMap,
    treeNodeIndexMap,
  }
}

// 默认的题目筛选条件
export const defaultQuestionFilters = (): QuestionFiltersType => ({
  provinceList: [],
  questionDifficultList: [],
  questionTypeList: [],
  yearList: [],
  sort: QuestionFilterSort.CREATE_TIME,
  sourceList: [],
});

export const convertQuestionFilterOptions = (options: QuestionFilterOption<number | string | QuestionFilterSort | QuestionFilterSource>[]): CommonOptionType<number | string | QuestionFilterSort | QuestionFilterSource>[] => {
  return options.map((item) => ({
    label: item.nameZh,
    value: item.value,
  }));
}

// 将题目筛选枚举值转换为map
export const convertQuestionFilterEnumToMap = (enumData: QuestionFilterEnumDatas): QuestionFilterEnumDatasMap => {
  const map = {} as QuestionFilterEnumDatasMap;
  Object.entries(enumData).forEach(([key, items]) => {
    map[key as QuestionFilterEnumMapKey] = new Map();
    items.forEach((item: QuestionFilterOption<QuestionFilterEnumValueType>) => {
      map[key as QuestionFilterEnumMapKey].set(item.value, item);
    });
  });
  return map;
}


export function groupBy<T, K extends string | number | symbol>(array: T[], callbackFn: (item: T) => K): Record<K, T[]> {
  return array.reduce((result, item) => {
    const key = callbackFn(item);
    (result[key] = result[key] || []).push(item);
    return result;
  }, {} as Record<K, T[]>);
}