/**
 * 友盟统计 https://manual.sensorsdata.cn/sa/docs/tech_sdk_client_web_use/v0300
 * 教师端数据埋点汇总 https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?renamingWikiNode=false&sheet=ceLy7M
 */
export * from "./assign";
export * from "./common";
export * from "./course";
export * from "./homework";
export * from "./login";
export * from "./personal-center";
// import { getAppInfo, getDeviceInfo } from "@repo/lib/utils/device";

class SensorsAnalytics {
  private static instance: SensorsAnalytics;

  public static getInstance(): SensorsAnalytics {
    if (!SensorsAnalytics.instance) {
      SensorsAnalytics.instance = new SensorsAnalytics();
    }

    return SensorsAnalytics.instance;
  }

  /**
   * trackEvent
   * @param category 目前按页面分类
   * @param action 事件名
   * @param payload 用于更详细的描述事件
   * @description 用于发送页面上按钮等交互元素被触发时的事件统计请求。如视频的"播放、暂停、调整音量"，页面上的"返回顶部"、"赞"、"收藏"等。也可用于发送Flash事件统计请求。
   * trackEvent事件的String字段长度为255，请使用时注意不要太长
   */
  public trackEvent(
    pag_name: UmengCategory,
    action: string,
    payload?: Record<string | number, unknown>
  ): void {
    const sensors = window["sensorsDataAnalytic201505"];

    if (!sensors) {
      return;
    }

    requestIdleCallback(() => {
      sensors.track(action, {
        // 页面分类
        pag_name,
        ...payload,
      });
    })
  }
}

declare global {
  interface Window {
    sensorsDataAnalytic201505: {
      track: <T>(eventName: string, params: T) => void;
      init: <T>(config: T) => void;
      registerPage: <T>(payload: T) => void;
      quick: <T>(payload: T) => void;
    };
  }
}

/**
 * 神策事件类别 (目前按页面划分)
 */
export enum UmengCategory {
  /**
   * 通用
   */
  COMMON = "通用",
  /**
   * 布置
   */
  ASSIGN = "布置页",
  /**
   * 课程
   */
  COURSE = "课程页",
  /**
   * 作业
   */
  HOMEWORK = "作业页",
  /**
   * 个人中心
   */
  PERSONAL_CENTER = "个人中心",
  /**
   * 登录
   */
  LOGIN = "登录页",
}

/**
 * 神策统计实例
 */
export const umeng = SensorsAnalytics.getInstance();
