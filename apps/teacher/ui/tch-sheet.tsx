"use client";
import { cn } from "@/utils/utils";
import * as SheetPrimitive from "@radix-ui/react-dialog";
import { useControllableValue } from "ahooks";
import { type ComponentProps, type ReactNode } from "react";

function SheetClose({ ...props }: ComponentProps<typeof SheetPrimitive.Close>) {
  return <SheetPrimitive.Close data-slot="sheet-close" {...props} />;
}

export type GilSheetProps = ComponentProps<typeof SheetPrimitive.Root> &
  ComponentProps<typeof SheetPrimitive.Content> & {
    side?: "top" | "right" | "bottom" | "left";
    trigger?: ReactNode;
    /**
     * 是否显示遮罩层
     */
    mask?: boolean;
    /**
     * 关闭时销毁 Drawer 里的子元素
     */
    destroyOnClose?: boolean;
    overlayClassName?: string;
  };

function TchSheet({
  trigger,
  children,
  modal,
  mask = true,
  side = "right",
  className,
  destroyOnClose = true,
  open,
  onOpenChange,
  overlayClassName,
  ...props
}: GilSheetProps) {
  const [_open, _setOpen] = useControllableValue(
    { open, onOpenChange },
    {
      defaultValuePropName: "defaultOpen",
      valuePropName: "open",
      trigger: "onOpenChange",
    }
  );

  return (
    <SheetPrimitive.Root
      data-slot="sheet"
      open={_open}
      onOpenChange={_setOpen}
      modal={modal}
    >
      {/* 触发器 */}
      <SheetPrimitive.Trigger asChild data-slot="sheet-trigger">
        {trigger}
      </SheetPrimitive.Trigger>

      <SheetPrimitive.Portal data-slot="sheet-portal">
        {/* 遮罩层 */}
        {
          <SheetPrimitive.Overlay
            data-slot="sheet-overlay"
            className={cn(
              "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-[#32374A73]",
              overlayClassName
            )}
            hidden={!mask}
          />
        }

        {/* 内容 */}
        <SheetPrimitive.Content
          data-slot="sheet-content"
          aria-describedby={undefined}
          className={cn(
            "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
            side === "right" &&
              "data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 sm:max-w-sm",
            side === "left" &&
              "data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 sm:max-w-sm",
            side === "top" &&
              "data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto",
            side === "bottom" &&
              "data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto",
            className
          )}
          onOpenAutoFocus={(e) => e.preventDefault()}
          {...props}
        >
          <SheetPrimitive.Title data-slot="sheet-title" className="hidden" />

          {_open || !destroyOnClose ? children : null}
        </SheetPrimitive.Content>
      </SheetPrimitive.Portal>
    </SheetPrimitive.Root>
  );
}

export { SheetClose, TchSheet };
