"use client";

import { cn } from "@/utils/utils";
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area";
import * as React from "react";

function ScrollArea({
  className,
  children,
  orientation,
  viewportRef,
  viewportClassName,
  onScroll,
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.Root> &
  Pick<
    React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,
    "orientation"
  > & {
    viewportRef?: React.RefObject<HTMLDivElement | null>;
    viewportClassName?: string;
  }) {
  return (
    <ScrollAreaPrimitive.Root
      data-slot="scroll-area"
      className={cn("scroll-area-root relative", className)}
      {...props}
    >
      <ScrollAreaPrimitive.Viewport
        ref={viewportRef}
        data-slot="scroll-area-viewport"
        className={cn(
          "scroll-area-viewport focus-visible:ring-ring/50 size-full rounded-[inherit] outline-none transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-[3px]",
          viewportClassName
        )}
        onScroll={onScroll}
      >
        {children}
      </ScrollAreaPrimitive.Viewport>

      <ScrollBar orientation={orientation} />

      <ScrollAreaPrimitive.Corner />
    </ScrollAreaPrimitive.Root>
  );
}

function ScrollBar({
  className,
  orientation = "vertical",
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {
  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar
      data-slot="scroll-area-scrollbar"
      orientation={orientation}
      className={cn(
        "flex touch-none select-none p-px transition-colors",
        orientation === "vertical" &&
          "w-1.75 h-full border-l border-l-transparent",
        orientation === "horizontal" &&
          "h-1.75 flex-col border-t border-t-transparent",
        className
      )}
      {...props}
    >
      <ScrollAreaPrimitive.ScrollAreaThumb
        data-slot="scroll-area-thumb"
        className="bg-line-2 rounded-xs relative flex-1"
      />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  );
}

export { ScrollArea };
