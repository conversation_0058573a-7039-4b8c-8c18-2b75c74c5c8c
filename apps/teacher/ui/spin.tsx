import { cn } from "@/utils/utils";
import <PERSON><PERSON> from "lottie-react";
import loadingData from "@/public/lottie/loading.json";
import { memo } from "react";

function Spin({
  className,
  children,
  loading = true,
  mask = false,
}: {
  className?: string;
  children?: React.ReactNode;
  loading?: boolean;
  mask?: boolean;
}) {
  return (
    <div className={cn("gil-loading-wrapper relative isolate", className)}>
      <div>
        {/* 遮罩层 */}
        {children && (
          <div
            className={cn(
              loading ? "animate-in fade-in-0" : "animate-out fade-out-0",
              mask && loading ? "z-50 bg-[#32374A73]" : "-z-50",
              "absolute inset-0"
            )}
          />
        )}

        {/* loading动画 */}
        {loading && (
          <Lottie
            className={cn(
              `w-15 h-11.25`,
              children &&
                "absolute left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2"
            )}
            animationData={loadingData}
            loop={true}
          />
        )}
      </div>

      {/* 内容 */}
      {children}
    </div>
  );
}

export default memo(Spin);
