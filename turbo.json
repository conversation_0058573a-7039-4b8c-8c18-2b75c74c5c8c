{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["NODE_ENV"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:dev": {"dependsOn": ["^build:dev"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:test": {"dependsOn": ["^build:test"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:prod": {"dependsOn": ["^build:prod"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build:local": {"dependsOn": ["^build:local"], "cache": false}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["^start"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "deploy:dev": {"dependsOn": ["^deploy:dev"], "cache": false}}}